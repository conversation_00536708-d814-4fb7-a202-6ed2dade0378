-- CreateEnum
CREATE TYPE "public"."PaymentMethodType" AS ENUM ('visa', 'mastercard', 'amex', 'discover', 'paypal', 'bank', 'apple', 'google');

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- CreateTable
CREATE TABLE "public"."payment_method" (
    "id" TEXT NOT NULL,
    "holder_name" TEXT NOT NULL,
    "ref_number" TEXT NOT NULL,
    "expiry_date" TEXT NOT NULL,
    "cvv" TEXT NOT NULL,
    "type" "public"."PaymentMethodType" NOT NULL DEFAULT 'visa',
    "accountId" TEXT NOT NULL,

    CONSTRAINT "payment_method_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."payment_method" ADD CONSTRAINT "payment_method_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
