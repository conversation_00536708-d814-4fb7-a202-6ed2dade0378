-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- CreateTable
CREATE TABLE "public"."notification" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "readAt" TIMESTAMP(3),
    "deletedAt" TIMESTAMP(3),
    "userId" TEXT NOT NULL,
    "data" JSONB,
    "priority" TEXT NOT NULL DEFAULT 'normal',
    "expiresAt" TIMESTAMP(3),
    "sourceId" TEXT,
    "sourceType" TEXT,
    "deliveredAt" TIMESTAMP(3),
    "failedAt" TIMESTAMP(3),
    "retryCount" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "notification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "notification_userId_idx" ON "public"."notification"("userId");

-- CreateIndex
CREATE INDEX "notification_userId_isRead_idx" ON "public"."notification"("userId", "isRead");

-- CreateIndex
CREATE INDEX "notification_userId_isDeleted_idx" ON "public"."notification"("userId", "isDeleted");

-- CreateIndex
CREATE INDEX "notification_userId_createdAt_idx" ON "public"."notification"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "notification_type_idx" ON "public"."notification"("type");

-- CreateIndex
CREATE INDEX "notification_category_idx" ON "public"."notification"("category");

-- CreateIndex
CREATE INDEX "notification_priority_idx" ON "public"."notification"("priority");

-- CreateIndex
CREATE INDEX "notification_expiresAt_idx" ON "public"."notification"("expiresAt");

-- CreateIndex
CREATE INDEX "notification_sourceId_sourceType_idx" ON "public"."notification"("sourceId", "sourceType");

-- AddForeignKey
ALTER TABLE "public"."notification" ADD CONSTRAINT "notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
