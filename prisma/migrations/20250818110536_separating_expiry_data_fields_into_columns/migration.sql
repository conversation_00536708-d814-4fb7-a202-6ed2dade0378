/*
  Warnings:

  - You are about to drop the column `expiry_date` on the `payment_method` table. All the data in the column will be lost.
  - Added the required column `expiry_month` to the `payment_method` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expiry_year` to the `payment_method` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."payment_method" DROP COLUMN "expiry_date",
ADD COLUMN     "expiry_month" TEXT NOT NULL,
ADD COLUMN     "expiry_year" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;
