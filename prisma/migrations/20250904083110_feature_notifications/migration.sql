-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- CreateTable
CREATE TABLE "public"."pushSubscription" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "endpoint" TEXT NOT NULL,
    "p256dhKey" TEXT NOT NULL,
    "authKey" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pushSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."notificationLog" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "payload" TEXT,
    "recipientCount" INTEGER NOT NULL DEFAULT 0,
    "successCount" INTEGER NOT NULL DEFAULT 0,
    "failureCount" INTEGER NOT NULL DEFAULT 0,
    "sentBy" TEXT NOT NULL,
    "sentAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notificationLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pushSubscription_userId_idx" ON "public"."pushSubscription"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "pushSubscription_userId_key" ON "public"."pushSubscription"("userId");

-- CreateIndex
CREATE INDEX "notificationLog_type_idx" ON "public"."notificationLog"("type");

-- CreateIndex
CREATE INDEX "notificationLog_sentBy_idx" ON "public"."notificationLog"("sentBy");

-- CreateIndex
CREATE INDEX "notificationLog_sentAt_idx" ON "public"."notificationLog"("sentAt");

-- AddForeignKey
ALTER TABLE "public"."pushSubscription" ADD CONSTRAINT "pushSubscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."notificationLog" ADD CONSTRAINT "notificationLog_sentBy_fkey" FOREIGN KEY ("sentBy") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
