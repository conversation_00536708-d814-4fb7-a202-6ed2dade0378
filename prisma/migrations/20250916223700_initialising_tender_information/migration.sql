-- CreateTable
CREATE TABLE "public"."nest_tender" (
    "id" TEXT NOT NULL,
    "ocid" TEXT NOT NULL,
    "language" TEXT,
    "initiation_type" TEXT,
    "description" TEXT,
    "address" JSONB,
    "timeline" JSONB,
    "procuring_entity" TEXT,
    "procuring_method" TEXT,
    "items" JSONB[],
    "parties" JSONB[],
    "category" TEXT,
    "status" "public"."status" NOT NULL DEFAULT 'created',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "nest_tender_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "nest_tender_id_key" ON "public"."nest_tender"("id");

-- CreateIndex
CREATE UNIQUE INDEX "nest_tender_ocid_key" ON "public"."nest_tender"("ocid");
