/*
  Warnings:

  - You are about to drop the column `name` on the `user` table. All the data in the column will be lost.
  - You are about to drop the `categories` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `clients` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `clients_interest` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `clients_rels` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `media` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `payload_locked_documents` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `payload_locked_documents_rels` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `payload_migrations` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `payload_preferences` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `payload_preferences_rels` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products_details` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products_features` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products_gallery` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `products_rels` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `profile` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects_gallery` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects_links` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects_rels` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `users` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `users_sessions` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."clients_interest" DROP CONSTRAINT "clients_interest_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."clients_rels" DROP CONSTRAINT "clients_rels_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."clients_rels" DROP CONSTRAINT "clients_rels_projects_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_categories_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_clients_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_media_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_products_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_projects_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_users_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_preferences_rels" DROP CONSTRAINT "payload_preferences_rels_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."payload_preferences_rels" DROP CONSTRAINT "payload_preferences_rels_users_fk";

-- DropForeignKey
ALTER TABLE "public"."products" DROP CONSTRAINT "products_image_id_media_id_fk";

-- DropForeignKey
ALTER TABLE "public"."products_details" DROP CONSTRAINT "products_details_parent_id_fk";

-- DropForeignKey
ALTER TABLE "public"."products_features" DROP CONSTRAINT "products_features_parent_id_fk";

-- DropForeignKey
ALTER TABLE "public"."products_gallery" DROP CONSTRAINT "products_gallery_parent_id_fk";

-- DropForeignKey
ALTER TABLE "public"."products_gallery" DROP CONSTRAINT "products_gallery_picture_id_media_id_fk";

-- DropForeignKey
ALTER TABLE "public"."products_rels" DROP CONSTRAINT "products_rels_categories_fk";

-- DropForeignKey
ALTER TABLE "public"."products_rels" DROP CONSTRAINT "products_rels_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."profile" DROP CONSTRAINT "profile_userId_fkey";

-- DropForeignKey
ALTER TABLE "public"."projects" DROP CONSTRAINT "projects_image_id_media_id_fk";

-- DropForeignKey
ALTER TABLE "public"."projects_gallery" DROP CONSTRAINT "projects_gallery_parent_id_fk";

-- DropForeignKey
ALTER TABLE "public"."projects_gallery" DROP CONSTRAINT "projects_gallery_picture_id_media_id_fk";

-- DropForeignKey
ALTER TABLE "public"."projects_links" DROP CONSTRAINT "projects_links_parent_id_fk";

-- DropForeignKey
ALTER TABLE "public"."projects_rels" DROP CONSTRAINT "projects_rels_categories_fk";

-- DropForeignKey
ALTER TABLE "public"."projects_rels" DROP CONSTRAINT "projects_rels_parent_fk";

-- DropForeignKey
ALTER TABLE "public"."users_sessions" DROP CONSTRAINT "users_sessions_parent_id_fk";

-- AlterTable
ALTER TABLE "public"."user" DROP COLUMN "name",
ADD COLUMN     "companyId" TEXT,
ADD COLUMN     "firstName" TEXT,
ADD COLUMN     "lastName" TEXT,
ADD COLUMN     "phone" TEXT;

-- DropTable
DROP TABLE "public"."categories";

-- DropTable
DROP TABLE "public"."clients";

-- DropTable
DROP TABLE "public"."clients_interest";

-- DropTable
DROP TABLE "public"."clients_rels";

-- DropTable
DROP TABLE "public"."media";

-- DropTable
DROP TABLE "public"."payload_locked_documents";

-- DropTable
DROP TABLE "public"."payload_locked_documents_rels";

-- DropTable
DROP TABLE "public"."payload_migrations";

-- DropTable
DROP TABLE "public"."payload_preferences";

-- DropTable
DROP TABLE "public"."payload_preferences_rels";

-- DropTable
DROP TABLE "public"."products";

-- DropTable
DROP TABLE "public"."products_details";

-- DropTable
DROP TABLE "public"."products_features";

-- DropTable
DROP TABLE "public"."products_gallery";

-- DropTable
DROP TABLE "public"."products_rels";

-- DropTable
DROP TABLE "public"."profile";

-- DropTable
DROP TABLE "public"."projects";

-- DropTable
DROP TABLE "public"."projects_gallery";

-- DropTable
DROP TABLE "public"."projects_links";

-- DropTable
DROP TABLE "public"."projects_rels";

-- DropTable
DROP TABLE "public"."users";

-- DropTable
DROP TABLE "public"."users_sessions";

-- DropEnum
DROP TYPE "public"."enum_clients_interest";

-- DropEnum
DROP TYPE "public"."enum_projects_type";

-- CreateTable
CREATE TABLE "public"."company" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tin" TEXT NOT NULL,
    "category" TEXT,
    "email" TEXT,
    "address" TEXT,
    "phone" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "company_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "company_id_key" ON "public"."company"("id");

-- CreateIndex
CREATE UNIQUE INDEX "company_name_key" ON "public"."company"("name");

-- AddForeignKey
ALTER TABLE "public"."user" ADD CONSTRAINT "user_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "public"."company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
