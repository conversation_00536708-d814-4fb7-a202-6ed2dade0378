-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "public"."status" ADD VALUE 'online';
ALTER TYPE "public"."status" ADD VALUE 'offline';

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- CreateTable
CREATE TABLE "public"."socket" (
    "id" TEXT NOT NULL,
    "socketId" TEXT NOT NULL,
    "sessionId" TEXT,
    "status" "public"."status",
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "socket_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "socket_id_key" ON "public"."socket"("id");

-- CreateIndex
CREATE UNIQUE INDEX "socket_userId_socketId_key" ON "public"."socket"("userId", "socketId");

-- AddForeignKey
ALTER TABLE "public"."socket" ADD CONSTRAINT "socket_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
