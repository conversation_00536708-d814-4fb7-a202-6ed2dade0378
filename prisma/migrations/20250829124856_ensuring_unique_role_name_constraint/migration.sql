/*
  Warnings:

  - You are about to drop the column `accountId` on the `role` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name]` on the table `role` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "public"."role" DROP CONSTRAINT "role_accountId_fkey";

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."role" DROP COLUMN "accountId";

-- CreateIndex
CREATE UNIQUE INDEX "role_name_key" ON "public"."role"("name");
