/*
  Warnings:

  - You are about to drop the `requests` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."requests" DROP CONSTRAINT "requests_userId_fkey";

-- DropTable
DROP TABLE "public"."requests";

-- CreateTable
CREATE TABLE "public"."request" (
    "id" TEXT NOT NULL,
    "message" TEXT,
    "status" "public"."status" NOT NULL DEFAULT 'active',
    "tender_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "request_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "request_id_key" ON "public"."request"("id");

-- CreateIndex
CREATE UNIQUE INDEX "request_tender_id_key" ON "public"."request"("tender_id");

-- CreateIndex
CREATE UNIQUE INDEX "unique_tender_user_request" ON "public"."request"("tender_id", "userId");

-- AddForeignKey
ALTER TABLE "public"."request" ADD CONSTRAINT "request_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
