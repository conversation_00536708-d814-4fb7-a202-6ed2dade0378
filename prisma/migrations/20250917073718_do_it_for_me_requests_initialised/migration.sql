-- CreateTable
CREATE TABLE "public"."subscription" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" "public"."status" NOT NULL DEFAULT 'active',
    "features" JSONB[],
    "price" DOUBLE PRECISION NOT NULL,
    "subscribers" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."requests" (
    "id" TEXT NOT NULL,
    "message" TEXT,
    "status" "public"."status" NOT NULL DEFAULT 'active',
    "tender_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "subscription_id_key" ON "public"."subscription"("id");

-- CreateIndex
CREATE UNIQUE INDEX "subscription_name_key" ON "public"."subscription"("name");

-- CreateIndex
CREATE UNIQUE INDEX "requests_id_key" ON "public"."requests"("id");

-- CreateIndex
CREATE UNIQUE INDEX "requests_tender_id_key" ON "public"."requests"("tender_id");

-- AddForeignKey
ALTER TABLE "public"."requests" ADD CONSTRAINT "requests_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
