-- CreateEnum
CREATE TYPE "public"."TransactionType" AS ENUM ('full_payment', 'partial_payment', 'advanced_payment');

-- AlterEnum
ALTER TYPE "public"."status" ADD VALUE 'pending';

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- CreateTable
CREATE TABLE "public"."transaction" (
    "id" TEXT NOT NULL,
    "type" "public"."TransactionType" NOT NULL DEFAULT 'full_payment',
    "amount" DOUBLE PRECISION NOT NULL,
    "payment_method_id" TEXT,
    "condition" TEXT,
    "status" "public"."status" NOT NULL DEFAULT 'created',
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "accountId" TEXT NOT NULL,

    CONSTRAINT "transaction_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."transaction" ADD CONSTRAINT "transaction_payment_method_id_fkey" FOREIGN KEY ("payment_method_id") REFERENCES "public"."payment_method"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."transaction" ADD CONSTRAINT "transaction_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
