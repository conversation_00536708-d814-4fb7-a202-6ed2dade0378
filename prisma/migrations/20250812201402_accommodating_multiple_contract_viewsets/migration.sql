/*
  Warnings:

  - Added the required column `accountId` to the `contract` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accountId` to the `role` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accountId` to the `room` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."contract" DROP CONSTRAINT "contract_clientId_fkey";

-- AlterTable
ALTER TABLE "public"."contract" ADD COLUMN     "accountId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."role" ADD COLUMN     "accountId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."room" ADD COLUMN     "accountId" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."contract" ADD CONSTRAINT "contract_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."room" ADD CONSTRAINT "room_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."role" ADD CONSTRAINT "role_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "public"."account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
