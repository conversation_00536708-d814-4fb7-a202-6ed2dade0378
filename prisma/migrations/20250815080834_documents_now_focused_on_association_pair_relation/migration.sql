/*
  Warnings:

  - You are about to drop the column `contractId` on the `document` table. All the data in the column will be lost.
  - You are about to drop the column `messageId` on the `document` table. All the data in the column will be lost.
  - You are about to drop the column `proposalId` on the `document` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."document" DROP CONSTRAINT "document_contractId_fkey";

-- DropForeignKey
ALTER TABLE "public"."document" DROP CONSTRAINT "document_messageId_fkey";

-- DropForeignKey
ALTER TABLE "public"."document" DROP CONSTRAINT "document_proposalId_fkey";

-- AlterTable
ALTER TABLE "public"."document" DROP COLUMN "contractId",
DROP COLUMN "messageId",
DROP COLUMN "proposalId";

-- AlterTable
ALTER TABLE "public"."products" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;

-- AlterTable
ALTER TABLE "public"."projects" ALTER COLUMN "date" SET DEFAULT '2024-01-01 00:00:00+00'::timestamp with time zone;
