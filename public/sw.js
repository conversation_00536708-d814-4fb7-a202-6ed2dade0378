// Service Worker with Workbox and Push Notifications
console.log("Service Worker: Starting initialization...");

// Import Workbox
importScripts(
  "https://storage.googleapis.com/workbox-cdn/releases/7.0.0/workbox-sw.js"
);

// Check if workbox loaded successfully
if (workbox) {
  console.log("Service Worker: Workbox loaded successfully");

  // Enable workbox logging (always enabled for debugging)
  workbox.setConfig({ debug: true });

  // Skip waiting and claim clients immediately
  workbox.core.skipWaiting();
  workbox.core.clientsClaim();

  // Initialize precaching with fallback for missing manifest
  try {
    // Check if manifest exists and is an array
    if (
      typeof self.__WB_MANIFEST !== "undefined" &&
      Array.isArray(self.__WB_MANIFEST)
    ) {
      workbox.precaching.precacheAndRoute(self.__WB_MANIFEST);
      console.log("Service Worker: Precaching configured with manifest");
    } else {
      console.log(
        "Service Worker: No precache manifest found, skipping precaching"
      );
      // Initialize with empty array to avoid errors
      workbox.precaching.precacheAndRoute([]);
    }
  } catch (error) {
    console.warn("Service Worker: Precaching setup failed:", error);
    // Fallback: initialize with empty array
    try {
      workbox.precaching.precacheAndRoute([]);
    } catch (fallbackError) {
      console.error(
        "Service Worker: Fallback precaching failed:",
        fallbackError
      );
    }
  }

  // Cache strategies for different types of resources
  console.log("Service Worker: Setting up cache strategies...");

  try {
    // Cache API responses with network first strategy
    workbox.routing.registerRoute(
      ({ url }) => url.pathname.startsWith("/api/"),
      new workbox.strategies.NetworkFirst({
        cacheName: "api-cache",
        networkTimeoutSeconds: 3,
        plugins: [
          new workbox.expiration.ExpirationPlugin({
            maxEntries: 50,
            maxAgeSeconds: 5 * 60, // 5 minutes
          }),
          new workbox.cacheableResponse.CacheableResponsePlugin({
            statuses: [0, 200],
          }),
        ],
      })
    );
    console.log("Service Worker: API cache strategy registered");
  } catch (error) {
    console.error(
      "Service Worker: Failed to register API cache strategy:",
      error
    );
  }

  // Cache images with cache first strategy
  workbox.routing.registerRoute(
    ({ request }) => request.destination === "image",
    new workbox.strategies.CacheFirst({
      cacheName: "images-cache",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 100,
          maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
        }),
      ],
    })
  );

  // Cache CSS and JS files with stale while revalidate
  workbox.routing.registerRoute(
    ({ request }) =>
      request.destination === "style" || request.destination === "script",
    new workbox.strategies.StaleWhileRevalidate({
      cacheName: "static-resources",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 50,
          maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
        }),
      ],
    })
  );

  // Cache fonts with cache first strategy
  workbox.routing.registerRoute(
    ({ request }) => request.destination === "font",
    new workbox.strategies.CacheFirst({
      cacheName: "fonts-cache",
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 10,
          maxAgeSeconds: 365 * 24 * 60 * 60, // 1 year
        }),
      ],
    })
  );

  // Cache documents and other resources
  workbox.routing.registerRoute(
    ({ request }) => request.destination === "document",
    new workbox.strategies.NetworkFirst({
      cacheName: "documents-cache",
      networkTimeoutSeconds: 3,
      plugins: [
        new workbox.expiration.ExpirationPlugin({
          maxEntries: 30,
          maxAgeSeconds: 24 * 60 * 60, // 1 day
        }),
      ],
    })
  );

  console.log("Service Worker: All cache strategies configured successfully");
} else {
  console.error(
    "Service Worker: Workbox failed to load - falling back to basic service worker functionality"
  );

  // Basic service worker functionality without Workbox
  self.addEventListener("install", function (event) {
    console.log("Service Worker: Installing (fallback mode)");
    self.skipWaiting();
  });

  self.addEventListener("activate", function (event) {
    console.log("Service Worker: Activating (fallback mode)");
    event.waitUntil(self.clients.claim());
  });
}

// Push Notification Event Handlers
self.addEventListener("push", function (event) {
  console.log("Push event received:", event);

  let notificationData = {
    title: "New Notification",
    body: "You have a new notification",
    icon: "/favicons/android-chrome-192x192.png",
    badge: "/favicons/favicon-32x32.png",
    tag: "default",
    data: {},
    actions: [],
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        title: pushData.title || notificationData.title,
        body: pushData.body || notificationData.body,
        icon: pushData.icon || notificationData.icon,
        badge: pushData.badge || notificationData.badge,
        tag: pushData.tag || notificationData.tag,
        data: pushData.data || {},
        actions: pushData.actions || [],
        image: pushData.image,
        requireInteraction: pushData.requireInteraction || false,
        silent: pushData.silent || false,
        timestamp: Date.now(),
        ...pushData.options,
      };
    } catch (error) {
      console.error("Error parsing push data:", error);
    }
  }

  // Add default actions if none provided
  if (notificationData.actions.length === 0) {
    notificationData.actions = [
      {
        action: "view",
        title: "View",
        icon: "/favicons/favicon-16x16.png",
      },
      {
        action: "dismiss",
        title: "Dismiss",
      },
    ];
  }

  const promiseChain = self.registration.showNotification(
    notificationData.title,
    {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      tag: notificationData.tag,
      data: notificationData.data,
      actions: notificationData.actions,
      image: notificationData.image,
      requireInteraction: notificationData.requireInteraction,
      silent: notificationData.silent,
      timestamp: notificationData.timestamp,
      vibrate: [200, 100, 200],
    }
  );

  event.waitUntil(promiseChain);
});

// Notification click event handler
self.addEventListener("notificationclick", function (event) {
  console.log("Notification clicked:", event);

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  // Close the notification
  notification.close();

  if (action === "dismiss") {
    return;
  }

  // Handle different notification actions
  let urlToOpen = "/";

  if (data.url) {
    urlToOpen = data.url;
  } else if (data.type) {
    // Route based on notification type
    switch (data.type) {
      case "chat-message":
        urlToOpen = `/chat/${data.roomId || ""}`;
        break;
      case "document-update":
        urlToOpen = `/documents/${data.documentId || ""}`;
        break;
      case "contract-change":
        urlToOpen = `/contracts/${data.contractId || ""}`;
        break;
      case "proposal-update":
        urlToOpen = `/proposals/${data.proposalId || ""}`;
        break;
      case "system-alert":
        urlToOpen = "/settings/notifications";
        break;
      default:
        urlToOpen = "/";
    }
  }

  // Focus existing window or open new one
  const promiseChain = clients
    .matchAll({
      type: "window",
      includeUncontrolled: true,
    })
    .then(function (clientList) {
      // Check if there's already a window open
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes(urlToOpen) && "focus" in client) {
          return client.focus();
        }
      }

      // If no matching window found, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    });

  event.waitUntil(promiseChain);
});

// Notification close event handler
self.addEventListener("notificationclose", function (event) {
  console.log("Notification closed:", event);

  const notification = event.notification;
  const data = notification.data || {};

  // Track notification dismissal if needed
  if (data.trackDismissal) {
    // Send analytics or tracking data
    fetch("/api/notifications/track", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        action: "dismissed",
        notificationId: data.id,
        timestamp: Date.now(),
      }),
    }).catch((error) => {
      console.error("Failed to track notification dismissal:", error);
    });
  }
});

// Background sync for offline actions
self.addEventListener("sync", function (event) {
  console.log("Background sync event:", event.tag);

  if (event.tag === "background-sync-notifications") {
    event.waitUntil(syncNotifications());
  }
});

// Sync notifications when back online
async function syncNotifications() {
  try {
    console.log("Service Worker: Starting notification sync...");

    // Fetch pending notifications from server
    const response = await fetch("/api/notifications/pending", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include", // Include cookies for authentication
    });

    if (!response.ok) {
      console.error(
        "Service Worker: Failed to fetch pending notifications:",
        response.status,
        response.statusText
      );
      return;
    }

    const data = await response.json();
    console.log("Service Worker: Received pending notifications:", data);

    if (
      !data.success ||
      !data.notifications ||
      data.notifications.length === 0
    ) {
      console.log("Service Worker: No pending notifications to sync");
      return;
    }

    // Show browser notifications for pending notifications
    let syncedCount = 0;
    for (const notification of data.notifications) {
      try {
        await self.registration.showNotification(notification.title, {
          body: notification.body,
          icon: notification.icon || "/favicons/android-chrome-192x192.png",
          badge: notification.badge || "/favicons/favicon-32x32.png",
          tag: notification.tag || `notification-${notification.id}`,
          data: notification.data || {},
          actions: notification.actions || [
            {
              action: "view",
              title: "View",
              icon: "/favicons/favicon-16x16.png",
            },
            {
              action: "dismiss",
              title: "Dismiss",
            },
          ],
          requireInteraction: notification.requireInteraction || false,
          timestamp: notification.timestamp || Date.now(),
          vibrate: [200, 100, 200],
        });
        syncedCount++;
      } catch (error) {
        console.error("Service Worker: Failed to show notification:", error);
      }
    }

    console.log(
      `Service Worker: Successfully synced ${syncedCount} notifications`
    );

    // Optionally, trigger email and push notifications for high-priority items
    await syncHighPriorityNotifications(data.notifications);
  } catch (error) {
    console.error("Service Worker: Failed to sync notifications:", error);
  }
}

// Sync high-priority notifications via server-side channels
async function syncHighPriorityNotifications(notifications) {
  try {
    const highPriorityNotifications = notifications.filter(
      (notif) =>
        notif.data?.priority === "high" || notif.data?.priority === "urgent"
    );

    if (highPriorityNotifications.length === 0) {
      return;
    }

    console.log(
      `Service Worker: Syncing ${highPriorityNotifications.length} high-priority notifications`
    );

    // Send high-priority notifications to the server for email/push processing
    const response = await fetch("/api/notifications/sync", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({
        notifications: highPriorityNotifications.map((notif) => ({
          id: notif.id,
          type: notif.data?.type || "system",
          priority: notif.data?.priority,
          userId: notif.data?.userId,
        })),
      }),
    });

    if (response.ok) {
      console.log(
        "Service Worker: High-priority notifications synced successfully"
      );
    } else {
      console.warn(
        "Service Worker: Failed to sync high-priority notifications:",
        response.status
      );
    }
  } catch (error) {
    console.error(
      "Service Worker: Failed to sync high-priority notifications:",
      error
    );
  }
}

// Service Worker initialization complete
console.log(
  "Service Worker: Initialization complete with push notification support"
);
console.log(
  "Service Worker: Ready to handle push notifications, caching, and background sync"
);

// Enhanced message handling for service worker communication
self.addEventListener("message", function (event) {
  console.log("Service Worker: Received message:", event.data);

  if (event.data && event.data.type === "GET_VERSION") {
    event.ports[0].postMessage({
      type: "VERSION",
      version: "1.0.0",
      features: ["push-notifications", "caching", "background-sync"],
    });
  }

  if (event.data && event.data.type === "SKIP_WAITING") {
    console.log("Service Worker: Received SKIP_WAITING message");
    self.skipWaiting();
  }
});
