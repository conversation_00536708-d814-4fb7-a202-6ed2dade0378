#!/bin/bash

# Script to convert .js and .jsx files to .ts and .tsx
# Usage: ./convert_js_to_ts.sh /path/to/directory

# Check if directory argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <directory>"
    echo "Example: $0 /path/to/your/project"
    exit 1
fi

DIRECTORY="$1"

# Check if directory exists
if [ ! -d "$DIRECTORY" ]; then
    echo "Error: Directory '$DIRECTORY' does not exist"
    exit 1
fi

echo "Converting JavaScript files to TypeScript in: $DIRECTORY"
echo "----------------------------------------"

# Counter for tracking conversions
js_count=0
jsx_count=0

# Find and convert .js files to .ts
while IFS= read -r -d '' file; do
    if [ -f "$file" ]; then
        new_file="${file%.js}.ts"
        mv "$file" "$new_file"
        echo "Converted: $(basename "$file") → $(basename "$new_file")"
        ((js_count++))
    fi
done < <(find "$DIRECTORY" -type f -name "*.js" -print0)

# Find and convert .jsx files to .tsx
while IFS= read -r -d '' file; do
    if [ -f "$file" ]; then
        new_file="${file%.jsx}.tsx"
        mv "$file" "$new_file"
        echo "Converted: $(basename "$file") → $(basename "$new_file")"
        ((jsx_count++))
    fi
done < <(find "$DIRECTORY" -type f -name "*.jsx" -print0)

echo "----------------------------------------"
echo "Conversion complete!"
echo "Converted $js_count .js files to .ts"
echo "Converted $jsx_count .jsx files to .tsx"
echo "Total files converted: $((js_count + jsx_count))"