"use client";

import { useState, use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useEffect } from "react";

import { useParams } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import type { RootState, AppDispatch } from "@/store";
import { notify } from "@/lib/notifications";

// Chat notification interfaces
interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  data?: any;
  onClick?: () => void;
}

class ChatNotificationService {
  private permission: NotificationPermission = "default";
  private isSupported: boolean = false;

  constructor() {
    if (typeof window !== "undefined") {
      this.isSupported = "Notification" in window;
      this.permission = this.isSupported ? Notification.permission : "denied";
    }
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!this.isSupported) {
      console.warn("Notifications are not supported in this browser");
      return "denied";
    }

    if (this.permission === "granted") {
      return "granted";
    }

    try {
      this.permission = await Notification.requestPermission();
      return this.permission;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return "denied";
    }
  }

  /**
   * Show a browser notification
   */
  private showBrowserNotification(
    options: NotificationOptions
  ): Notification | null {
    if (!this.isSupported || this.permission !== "granted") {
      return null;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || "/favicon.ico",
        tag: options.tag,
        data: options.data,
        requireInteraction: false,
        silent: false,
      });

      if (options.onClick) {
        notification.onclick = options.onClick;
      }

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    } catch (error) {
      console.error("Error showing notification:", error);
      return null;
    }
  }

  /**
   * Show a toast notification (fallback or additional)
   */
  private showToastNotification(options: NotificationOptions): void {
    toast.info(options.title, {
      description: options.body,
      action: options.onClick
        ? {
            label: "View",
            onClick: options.onClick,
          }
        : undefined,
    });
  }

  /**
   * Show notification for new message
   */
  showMessageNotification(
    senderName: string,
    message: string,
    roomName: string,
    onClickCallback?: () => void
  ): void {
    const options: NotificationOptions = {
      title: `${senderName} in ${roomName}`,
      body: message
        .replace(/<[^>]*>/g, "")
        .trim()
        .slice(0, 100), // Strip HTML and limit length
      tag: `chat-message-${roomName}`,
      data: {
        type: "chat-message",
        roomName,
        senderName,
      },
      onClick: onClickCallback,
    };

    // Show browser notification if permission granted
    const browserNotification = this.showBrowserNotification(options);

    // Always show toast as fallback or additional notification
    if (!browserNotification) {
      this.showToastNotification(options);
    }
  }

  /**
   * Show notification for user joining/leaving
   */
  showUserStatusNotification(
    userName: string,
    status: "joined" | "left",
    roomName: string
  ): void {
    const options: NotificationOptions = {
      title: roomName,
      body: `${userName} ${status} the conversation`,
      tag: `user-status-${roomName}`,
      data: {
        type: "user-status",
        roomName,
        userName,
        status,
      },
    };

    // Only show toast for user status (less intrusive)
    this.showToastNotification(options);
  }

  /**
   * Show notification for typing indicators
   */
  showTypingNotification(userName: string, roomName: string): void {
    // Only show as toast, and only briefly
    toast.info(`${userName} is typing...`, {
      duration: 2000,
      id: `typing-${userName}-${roomName}`, // Prevent duplicates
    });
  }

  /**
   * Check if notifications are supported and enabled
   */
  isEnabled(): boolean {
    return this.isSupported && this.permission === "granted";
  }

  /**
   * Get current permission status
   */
  getPermission(): NotificationPermission {
    return this.permission;
  }

  /**
   * Check if notifications are supported
   */
  isNotificationSupported(): boolean {
    return this.isSupported;
  }
}

// Create singleton instance
const chatNotifications = new ChatNotificationService();

import {
  sendMessage,
  updateMessage,
  deleteMessage,
  createRoom,
  updateRoom,
  deleteRoom,
  updateUserState,
  fetchChatStatistics,
} from "@/store/actions/chat";
import {
  setCurrentRoom,
  clearError,
  addMessageOptimistic,
  markMessagesAsRead,
  setUserState,
  initializeRoomMemberStates,
  clearUserStates,
  bulkUpdateUserStates,
} from "@/store/slices/chat";
import type {
  CreateRoom,
  CreateMessage,
  UserState,
} from "@/lib/api/validators/schemas/chat";
import { useAuth } from "./useAuth";
import { useSocket } from "./useSocket";

export function useChat() {
  const { slug } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const [toReply, setToReply] = useState<any>(null);

  const {
    currentRoomId,
    // messages,
    // typingUsers,
    userStates,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error,
  } = useSelector((state: RootState) => state.chat);

  const { user, accountId } = useAuth();

  // Socket.IO integration will be initialized after memoized handlers are defined

  // Use SWR for fetching rooms
  const {
    data: roomsData = [],
    error: roomsError,
    mutate: mutateRooms,
  } = useSWR(
    accountId ? `chat/room?accountId=${accountId}&includeMembers=true` : null,
    fetcher
  );

  // Use SWR for fetching messages
  const {
    data: messagesData = [],
    error: messagesError,
    mutate: mutateMessages,
  } = useSWR(
    accountId && currentRoomId ? `chat/room/${currentRoomId}/messages` : null,
    fetcher
  );

  const messages: any[] = messagesData?.data || [];

  // Use SWR for fetching statistics
  const { data: statistics, error: statsError } = useSWR(
    "chat/statistics",
    fetcher
  );

  const rooms = roomsData?.data?.rooms || [];
  // Get current room
  const currentRoom =
    rooms.find((room: any) => room.id === currentRoomId) || null;

  // Get messages for current room
  const currentMessages = messages;

  // Derive typing users from userStates with user info (excluding current user)
  const typingUsers = useMemo(() => {
    console.log("🔍 [useChat] Deriving typing users:", {
      userStates,
      accountId,
      currentRoomMembers: currentRoom?.members?.length || 0,
    });

    const result = Object.entries(userStates)
      .filter(([memberAccountId, state]) => {
        const isTyping = state === "typing";
        const isNotCurrentUser = memberAccountId !== accountId;
        console.log(
          `🔍 [useChat] Member ${memberAccountId}: state=${state}, isTyping=${isTyping}, isNotCurrentUser=${isNotCurrentUser}`
        );
        return isTyping && isNotCurrentUser; // Exclude current user
      })
      .map(([memberAccountId]) => {
        // Try to find user info from current room members
        const member = currentRoom?.members?.find(
          (m: any) => m.accountId === memberAccountId
        );
        const userInfo = member?.account?.user || {};

        const typingUser = {
          id: memberAccountId,
          name: userInfo.name,
          email: userInfo.email,
          avatar: userInfo.image || userInfo.avatar,
        };

        console.log(`🔍 [useChat] Created typing user:`, typingUser);
        return typingUser;
      });

    console.log("🔍 [useChat] Final typing users result:", result);
    return result;
  }, [userStates, currentRoom?.members, accountId]);

  // Get typing users for current room (now using the derived typingUsers)
  const currentTypingUsers = typingUsers;

  // Get online members count for current room
  const onlineMembersCount = useMemo(() => {
    return Object.entries(userStates).filter(([, state]) => state === "online")
      .length;
  }, [userStates]);

  // Get all member states for current room
  const roomMemberStates = useMemo(() => {
    if (!currentRoom?.members) return {};

    const memberStates: Record<
      string,
      {
        accountId: string;
        state: UserState;
        userInfo: any;
      }
    > = {};

    currentRoom.members.forEach((member: any) => {
      const state = userStates[member.accountId] || "offline";
      memberStates[member.accountId] = {
        accountId: member.accountId,
        state,
        userInfo: member.account?.user || {},
      };
    });

    return memberStates;
  }, [currentRoom?.members, userStates]);

  // Fetch all rooms for current user
  const fetchUserRooms = useCallback(async () => {
    try {
      await mutateRooms();
    } catch (error) {
      console.error("Failed to fetch rooms:", error);
      throw error;
    }
  }, [mutateRooms]);

  // ============================================================================
  // INITIALIZATION & SETUP
  // ============================================================================

  // Initialize chat (fetch rooms and statistics)
  const initializeChat = useCallback(async () => {
    try {
      await Promise.all([
        mutateRooms(),
        dispatch(fetchChatStatistics()).unwrap(),
      ]);

      // Set user as online
      if (user?.id) {
        await updateCurrentUserState("online");
      }
    } catch (error) {
      console.error("Failed to initialize chat:", error);
    }
  }, [mutateRooms, dispatch, user?.id]);

  // Clear error
  const clearChatError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // ============================================================================
  // ROOM MANAGEMENT
  // ============================================================================

  // Create a new room
  const createChatRoom = useCallback(
    async (roomData: CreateRoom) => {
      try {
        const newRoom = await dispatch(createRoom(roomData)).unwrap();
        // Automatically switch to the new room
        dispatch(setCurrentRoom(newRoom.id));
        // Refresh rooms list
        await notify(
          {
            type: "room",
            title: `You've been invited`,
            message: `Join the new room: ${newRoom.name} to interact with the ai org`,
            data: {
              userId: accountId,
              entityType: "room",
              actionUrl: `/${slug}/dashboard/chat/${newRoom.id}`,
            },
          },
          "create"
        );
        return newRoom;
      } catch (error) {
        console.error("Failed to create room:", error);
        throw error;
      } finally {
        await mutateRooms();
      }
    },
    [dispatch, mutateRooms]
  );

  // Update an existing room
  const updateChatRoom = useCallback(
    async (roomId: string, roomData: Partial<CreateRoom>) => {
      try {
        const updatedRoom = await dispatch(
          updateRoom({ id: roomId, roomData })
        ).unwrap();
        // Refresh rooms list to show updated data
        return updatedRoom;
      } catch (error) {
        console.error("Failed to update room:", error);
        throw error;
      } finally {
        await mutateRooms();
      }
    },
    [dispatch, mutateRooms]
  );

  // Delete a room
  const deleteChatRoom = useCallback(
    async (roomId: string) => {
      try {
        await dispatch(deleteRoom(roomId)).unwrap();

        // If the deleted room was the current room, clear current room
        if (currentRoomId === roomId) {
          dispatch(setCurrentRoom(null));
        }

        return { success: true, message: "Room deleted successfully" };
      } catch (error) {
        console.error("Failed to delete room:", error);
        throw error;
      } finally {
        await mutateRooms();
      }
    },
    [dispatch, currentRoomId, mutateRooms]
  );

  // ============================================================================
  // MESSAGE MANAGEMENT
  // ============================================================================

  function scrollToRepliedMessage(message: any) {
    const repliedMessage = document.querySelector(`#message-${message.id}`);

    if (repliedMessage) {
      repliedMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }

  // Fetch messages for a specific room (placeholder - requires API implementation)
  const fetchRoomMessages = useCallback(
    async (roomId: string, limit = 50, offset = 0) => {
      try {
        // For now, this is a placeholder
      } catch (error) {
        console.error("Failed to fetch messages:", error);
        throw error;
      }
    },
    []
  );

  function serializeMessageWithAttchments(
    message: any,
    attachments: any[]
  ): FormData | undefined {
    if (!message || !attachments) return;

    let formData = new FormData();
    attachments.forEach((file: File) => {
      formData.append("attachment-" + file.name, file, file.name);
    });
    Object.entries(message).forEach(([key, value]: [string, any]) => {
      formData.append(key, value);
    });
    return formData;
  }

  // Send a message
  const sendChatMessage = useCallback(
    async (content: string, reply?: any, attachments?: any[]) => {
      if (!accountId || !content.trim()) return;

      if (!currentRoomId) return;

      let roomId: string = currentRoomId;

      let data: CreateMessage | FormData | undefined = {
        content: content.trim(),
        sent_from: accountId,
        roomId: currentRoomId,
      };

      if (reply) {
        data.type = reply.type;
        data.associations = reply?.association;
      }

      let withAttachments = false;

      if (attachments && attachments?.length > 0) {
        data = serializeMessageWithAttchments(data, attachments);
        withAttachments = true;
      }

      try {
        // Send actual message
        await dispatch(sendMessage({ roomId, data, withAttachments })).unwrap();
        // Note: Success toast is handled in the action for now
      } catch (error) {
        console.error("Failed to send message:", error);
        toast.error("Failed to send message");
        throw error;
      } finally {
        mutateMessages();
        setToReply(null);
      }
    },
    [dispatch, user, currentRoomId]
  );

  // Update a message
  const updateChatMessage = useCallback(
    async (messageId: string, content: string) => {
      try {
        const updatedMessage = await dispatch(
          updateMessage({ messageId, content })
        ).unwrap();
        toast.success("Message updated successfully");
        // Refresh messages list to show updated data
        await mutateMessages();
        return updatedMessage;
      } catch (error) {
        console.error("Failed to update message:", error);
        toast.error("Failed to update message");
        throw error;
      } finally {
        await mutateMessages();
      }
    },
    [dispatch, mutateMessages]
  );

  // Delete a message
  const deleteChatMessage = useCallback(
    async (messageId: string) => {
      try {
        await dispatch(deleteMessage(messageId)).unwrap();
        toast.success("Message deleted successfully");
        // Refresh messages list
        await mutateMessages();
        return { success: true, message: "Message deleted successfully" };
      } catch (error) {
        console.error("Failed to delete message:", error);
        toast.error("Failed to delete message");
        throw error;
      } finally {
        await mutateMessages();
      }
    },
    [dispatch, mutateMessages]
  );

  // Copy message content to clipboard
  const copyMessageToClipboard = useCallback(async (content: string) => {
    try {
      // Strip HTML tags from content
      const textContent = content.replace(/<[^>]*>/g, "").trim();
      await navigator.clipboard.writeText(textContent);
      return { success: true, message: "Message copied to clipboard" };
    } catch (error) {
      console.error("Failed to copy message:", error);
      throw new Error("Failed to copy message to clipboard");
    }
  }, []);

  // Set up reply to a message
  const replyToMessage = useCallback(
    (message: { id: string; content: string; sender?: any }) => {
      // Strip HTML tags for display
      let response = {
        type: "reply",
        association: [
          {
            id: message.id,
            entity: "message",
            content: message.content,
            sender: message.sender?.name || "Unknown",
          },
        ],
        displayContent: message.content.replace(/<[^>]*>/g, "").trim(),
        originalMessage: message,
      };

      setToReply(response);
      return response;
    },
    []
  );

  const closeReply = useCallback(() => {
    setToReply(null);
  }, []);

  // ============================================================================
  // NAVIGATION & ROOM SELECTION
  // ============================================================================

  // Set current room
  const selectRoom = useCallback(
    (roomId: string | null) => {
      // Clear previous room's user states
      dispatch(clearUserStates());

      dispatch(setCurrentRoom(roomId));

      // Initialize user states for new room members
      if (roomId) {
        const selectedRoom = rooms.find((room: any) => room.id === roomId);
        if (selectedRoom?.members) {
          dispatch(
            initializeRoomMemberStates({
              members: selectedRoom.members.map((member: any) => ({
                accountId: member.accountId,
              })),
            })
          );
        }

        // Fetch messages for the selected room
        fetchRoomMessages(roomId);
        // Mark messages as read
        dispatch(markMessagesAsRead(roomId));
      }
    },
    [dispatch, fetchRoomMessages, rooms]
  );

  // ============================================================================
  // USER STATE MANAGEMENT
  // ============================================================================

  // Update multiple user states (for bulk updates)
  const updateMemberStates = useCallback(
    (memberStates: Record<string, UserState>) => {
      dispatch(bulkUpdateUserStates(memberStates));
    },
    [dispatch]
  );

  // Socket-powered userStates mutator - handles various state changes via socket events
  const mutateUserStatesOnSocketEvent = useCallback(
    (eventType: string, eventData: any) => {
      console.log("🔍 [useChat] Socket-powered userStates mutation:", {
        eventType,
        eventData,
        currentUserStates: Object.keys(userStates).length,
      });

      switch (eventType) {
        case "user_joined_room":
          // Add new user to room with online state
          if (eventData.userId && eventData.roomId === currentRoomId) {
            console.log(
              "🔍 [useChat] User joined room - setting online:",
              eventData.userId
            );
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "online",
              })
            );
          }
          break;

        case "user_left_room":
          // Set user as offline when they leave room
          if (eventData.userId && eventData.roomId === currentRoomId) {
            console.log(
              "🔍 [useChat] User left room - setting offline:",
              eventData.userId
            );
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "offline",
              })
            );
          }
          break;

        case "bulk_user_states":
          // Handle bulk state updates (e.g., on room join, server sync)
          if (eventData.states && typeof eventData.states === "object") {
            console.log(
              "🔍 [useChat] Bulk user states update:",
              eventData.states
            );
            dispatch(bulkUpdateUserStates(eventData.states));
          }
          break;

        case "user_activity_detected":
          // Set user as online when activity is detected
          if (eventData.userId) {
            console.log(
              "🔍 [useChat] User activity detected - setting online:",
              eventData.userId
            );
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "online",
              })
            );
          }
          break;

        case "user_idle_timeout":
          // Set user as away when idle timeout occurs
          if (eventData.userId) {
            console.log(
              "🔍 [useChat] User idle timeout - setting away:",
              eventData.userId
            );
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "away",
              })
            );
          }
          break;

        case "force_user_offline":
          // Force user offline (e.g., admin action, session expired)
          if (eventData.userId) {
            console.log("🔍 [useChat] Force user offline:", eventData.userId);
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "offline",
              })
            );
          }
          break;

        case "sync_room_members":
          // Sync all room member states (e.g., on reconnection)
          if (eventData.roomId === currentRoomId && eventData.memberStates) {
            console.log(
              "🔍 [useChat] Syncing room member states:",
              eventData.memberStates
            );

            // Clear existing states for this room
            dispatch(clearUserStates());

            // Set new states
            dispatch(bulkUpdateUserStates(eventData.memberStates));
          }
          break;

        // Handle standard user state events from socket
        case "user_state_online":
          if (eventData.userId) {
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "online",
              })
            );
          }
          break;

        case "user_state_offline":
          if (eventData.userId) {
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "offline",
              })
            );
          }
          break;

        case "user_state_typing":
          if (eventData.userId) {
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "typing",
              })
            );
          }
          break;

        case "user_state_away":
          if (eventData.userId) {
            dispatch(
              setUserState({
                accountId: eventData.userId,
                userState: "away",
              })
            );
          }
          break;

        default:
          console.warn(
            "🔍 [useChat] Unknown socket event type for userStates mutation:",
            eventType
          );
          break;
      }
    },
    [dispatch, userStates, currentRoomId]
  );

  // Memoized socket event handlers to prevent endless loops
  const handleUserStateChanged = useCallback(
    (data: {
      userId: string;
      userName?: string;
      state: string;
      roomId?: string;
      timestamp: string;
    }) => {
      console.log("🔍 [useChat] Socket user state changed:", {
        data,
        currentRoomId,
        hasRoomId: !!data.roomId,
        matchesCurrentRoom: data.roomId === currentRoomId,
        isGlobalUpdate: !data.roomId,
      });

      // Use the socket-powered mutator for standard state changes
      if (data.state && data.userId) {
        // Map standard state changes to mutator events
        const eventType = `user_state_${data.state}`;
        mutateUserStatesOnSocketEvent(eventType, {
          userId: data.userId,
          roomId: data.roomId,
          state: data.state,
          timestamp: data.timestamp,
        });
      }

      // Fallback to direct Redux update for compatibility
      const shouldUpdate = !data.roomId || data.roomId === currentRoomId;

      if (shouldUpdate && data.userId) {
        console.log("🔍 [useChat] Updating user state in Redux:", {
          accountId: data.userId,
          userState: data.state,
          updateType: data.roomId ? "room-specific" : "global",
        });

        // Update user state in Redux for any room member (including current user)
        // Note: data.userId should be treated as accountId from Socket.IO
        dispatch(
          setUserState({
            accountId: data.userId, // This is actually accountId from Socket.IO
            userState: data.state as UserState,
          })
        );
      } else {
        console.log("🔍 [useChat] Skipping user state update:", {
          reason: !data.userId ? "no userId" : "room mismatch",
          dataRoomId: data.roomId,
          currentRoomId,
        });
      }
    },
    [currentRoomId, mutateUserStatesOnSocketEvent, dispatch]
  );

  const handleBulkUserStates = useCallback(
    (data: {
      roomId?: string;
      states: Record<string, string>;
      timestamp: string;
    }) => {
      console.log("🔍 [useChat] Bulk user states received:", data);
      mutateUserStatesOnSocketEvent("bulk_user_states", data);
    },
    [mutateUserStatesOnSocketEvent]
  );

  const handleUserActivityDetected = useCallback(
    (data: { userId: string; roomId?: string; timestamp: string }) => {
      console.log("🔍 [useChat] User activity detected:", data);
      mutateUserStatesOnSocketEvent("user_activity_detected", data);
    },
    [mutateUserStatesOnSocketEvent]
  );

  const handleForceUserOffline = useCallback(
    (data: { userId: string; reason?: string; timestamp: string }) => {
      console.log("🔍 [useChat] Force user offline:", data);
      mutateUserStatesOnSocketEvent("force_user_offline", data);
    },
    [mutateUserStatesOnSocketEvent]
  );

  const handleUserStateUpdated = useCallback(
    (data: {
      success: boolean;
      userId: string;
      state: string;
      roomId?: string;
      timestamp: string;
    }) => {
      console.log("User state update confirmed:", data);
    },
    []
  );

  const handleMessage = useCallback(
    (notification: any) => {
      console.log("🔍 [useChat] Real-time message received:", notification);

      // Only process chat message notifications for the current room
      if (
        notification.type === "chat_message" &&
        notification.data?.roomId === currentRoomId &&
        notification.data?.mutationTrigger === true
      ) {
        mutateMessages();
        // Handle different message event types with enhanced mutation logic
        switch (notification.data.eventType) {
          case "message_created":
            console.log("🔍 [useChat] Triggering message creation mutation");
            // NOTE: Toast notifications are now handled by the unified notification system
            // via useNotifications hook to avoid duplicate notifications
            break;

          case "message_updated":
            console.log("🔍 [useChat] Triggering message update mutation");
            // NOTE: Toast notifications are now handled by the unified notification system
            // via useNotifications hook to avoid duplicate notifications
            break;

          case "message_deleted":
            console.log("🔍 [useChat] Triggering message deletion mutation");
            // NOTE: Toast notifications are now handled by the unified notification system
            // via useNotifications hook to avoid duplicate notifications
            break;

          default:
            console.log(
              "🔍 [useChat] Unknown message event type:",
              notification.data.eventType
            );
        }
      }
    },
    [currentRoomId, mutateMessages, accountId]
  );

  // Socket.IO integration for real-time user state updates (using memoized handlers)
  const {
    updateUserState: updateSocketUserState,
    requestRoomMemberStates: requestSocketRoomMemberStates,
    sendUserActivity: sendSocketUserActivity,
    forceUserOffline: forceSocketUserOffline,
    joinContext: joinSocketContext,
    leaveContext: leaveSocketContext,
    isConnected: isSocketConnected,
  } = useSocket({
    autoConnect: true,
    contexts: currentRoomId ? [{ type: "chat", id: currentRoomId }] : [],
    onUserStateChanged: handleUserStateChanged,
    onBulkUserStates: handleBulkUserStates,
    onUserActivityDetected: handleUserActivityDetected,
    onForceUserOffline: handleForceUserOffline,
    onUserStateUpdated: handleUserStateUpdated,
    onNotification: handleMessage, // Handle real-time messages
  });

  // Update user state (online/offline/typing/away)
  const updateCurrentUserState = useCallback(
    async (state: UserState, roomId?: string) => {
      console.log("🔍 [useChat] updateCurrentUserState called:", {
        state,
        roomId,
        accountId,
        currentRoomId,
        isSocketConnected,
      });

      if (!accountId) return;

      const targetRoomId = roomId || currentRoomId || undefined;

      try {
        // Update Redux store immediately for current user
        console.log("🔍 [useChat] Updating Redux store immediately:", {
          accountId,
          userState: state,
        });
        dispatch(setUserState({ accountId, userState: state }));

        // Update via API for persistence
        await dispatch(
          updateUserState({
            userId: accountId,
            state,
            roomId: targetRoomId,
          })
        ).unwrap();

        // Send real-time update via Socket.IO to other room members
        if (isSocketConnected && targetRoomId) {
          console.log("🔍 [useChat] Sending socket update:", {
            accountId,
            state,
            targetRoomId,
            userName: user?.name || user?.email || "Unknown User",
          });
          updateSocketUserState(
            accountId,
            state,
            targetRoomId,
            user?.name || user?.email || "Unknown User"
          );
        }
      } catch (error) {
        console.error("Failed to update user state:", error);
        // Revert optimistic update on error
        dispatch(setUserState({ accountId, userState: "offline" }));
      } finally {
        // Refresh messages list to show updated data
        await mutateMessages();
      }
    },
    [
      dispatch,
      accountId,
      currentRoomId,
      isSocketConnected,
      updateSocketUserState,
      user?.name,
      user?.email,
      mutateMessages,
    ]
  );

  // Start typing indicator
  const startTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("typing", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // Stop typing indicator
  const stopTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("online", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // ============================================================================
  // NOTIFICATION FUNCTIONS
  // ============================================================================

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    return await chatNotifications.requestPermission();
  }, []);

  // Show message notification
  const showMessageNotification = useCallback(
    (
      senderName: string,
      message: string,
      roomName: string,
      onClickCallback?: () => void
    ) => {
      chatNotifications.showMessageNotification(
        senderName,
        message,
        roomName,
        onClickCallback
      );
    },
    []
  );

  // Show user status notification
  const showUserStatusNotification = useCallback(
    (userName: string, status: "joined" | "left", roomName: string) => {
      chatNotifications.showUserStatusNotification(userName, status, roomName);
    },
    []
  );

  // Show typing notification
  const showTypingNotification = useCallback(
    (userName: string, roomName: string) => {
      chatNotifications.showTypingNotification(userName, roomName);
    },
    []
  );

  // Initialize chat notifications
  const initializeChatNotifications = useCallback(() => {
    if (typeof window !== "undefined") {
      // Request permission after a short delay to avoid blocking initial page load
      setTimeout(() => {
        if (chatNotifications.getPermission() === "default") {
          chatNotifications.requestPermission();
        }
      }, 2000);
    }
  }, []);

  // ============================================================================
  // STATISTICS & UTILITIES
  // ============================================================================

  // Fetch statistics
  const fetchStats = useCallback(async () => {
    try {
      await dispatch(fetchChatStatistics()).unwrap();
    } catch (error) {
      console.error("Failed to fetch statistics:", error);
      throw error;
    }
  }, [dispatch]);

  // Auto-initialize when user is available
  useEffect(() => {
    if (user?.id) {
      initializeChat();
      // Set user as online
      updateCurrentUserState("online");
      // Initialize chat notifications
      initializeChatNotifications();
    }
  }, [
    user?.id,
    initializeChat,
    updateCurrentUserState,
    initializeChatNotifications,
  ]);

  // Initialize room member states when current room or rooms data changes
  useEffect(() => {
    if (currentRoomId && currentRoom?.members) {
      dispatch(clearUserStates());
      dispatch(
        initializeRoomMemberStates({
          members: currentRoom.members.map((member: any) => ({
            accountId: member.accountId,
          })),
        })
      );
    }
  }, [currentRoomId, currentRoom?.members, dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (user?.id) {
        // Set user as offline when component unmounts
        updateCurrentUserState("offline");
      }
    };
  }, [user?.id, updateCurrentUserState]);

  return {
    // State
    toReply,
    rooms: rooms || [],
    currentRoom,
    currentRoomId,
    messages: currentMessages,
    allMessages: messages,
    typingUsers: currentTypingUsers,
    allTypingUsers: typingUsers,
    userStates,
    onlineMembersCount,
    roomMemberStates,
    statistics: statistics?.data || null,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error: error || roomsError || statsError,

    // Actions
    fetchRooms: fetchUserRooms,
    fetchMessages: fetchRoomMessages,
    sendMessage: sendChatMessage,
    updateMessage: updateChatMessage,
    deleteMessage: deleteChatMessage,
    copyMessage: copyMessageToClipboard,
    replyToMessage: replyToMessage,
    closeReply,
    createRoom: createChatRoom,
    updateRoom: updateChatRoom,
    deleteRoom: deleteChatRoom,
    selectRoom,
    updateUserState: updateCurrentUserState,
    updateMemberStates,
    mutateUserStatesOnSocketEvent,
    startTyping,
    stopTyping,
    fetchStatistics: fetchStats,
    scrollToRepliedMessage,
    initializeChat,

    // Utility actions
    clearError: clearChatError,
    markAsRead: (roomId: string) => dispatch(markMessagesAsRead(roomId)),

    // Notification functions
    requestNotificationPermission,
    showMessageNotification,
    showUserStatusNotification,
    showTypingNotification,
    initializeChatNotifications,
    isNotificationEnabled: chatNotifications.isEnabled(),
    isNotificationSupported: chatNotifications.isNotificationSupported(),
    notificationPermission: chatNotifications.getPermission(),

    // Socket.IO integration
    isSocketConnected,
    joinChatContext: joinSocketContext,
    leaveChatContext: leaveSocketContext,
    requestRoomMemberStates: requestSocketRoomMemberStates,
    sendUserActivity: sendSocketUserActivity,
    forceUserOffline: forceSocketUserOffline,

    // Computed values
    hasRooms: (rooms || []).length > 0,
    hasMessages: currentMessages.length > 0,
    isAnyLoading:
      isLoading || isLoadingMessages || isSendingMessage || isCreatingRoom,
    currentUser: { id: accountId },
    currentAccountId: accountId,
  };
}
