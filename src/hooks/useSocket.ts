"use client";

import { useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import {
  connectSocket,
  getSocket,
  joinContext,
  leaveContext,
  updateUserState,
  requestRoomMemberStates,
  sendUserActivity,
  forceUserOffline,
  addEventListener,
  removeEventListener,
  disconnectSocket,
  isSocketConnected,
  getSocketStatus,
  type SocketClientEvents,
  type NotificationSocketData,
} from "@/lib/socket/client";

// Hook options
export interface UseSocketOptions {
  autoConnect?: boolean;
  contexts?: Array<{ type: string; id: string }>;
  onNotification?: (notification: NotificationSocketData) => void;
  onUserStateChanged?: (data: {
    userId: string;
    userName?: string;
    state: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  onUserStateUpdated?: (data: {
    success: boolean;
    userId: string;
    state: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  onBulkUserStates?: (data: {
    roomId?: string;
    states: Record<string, string>;
    timestamp: string;
  }) => void;
  onUserActivityDetected?: (data: {
    userId: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  onForceUserOffline?: (data: {
    userId: string;
    reason?: string;
    timestamp: string;
  }) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Error) => void;
}

/**
 * React hook for managing Socket.IO connections
 */
export function useSocket(options: UseSocketOptions = {}) {
  const { data: session } = useSession();
  const {
    autoConnect = true,
    contexts = [],
    onNotification,
    onUserStateChanged,
    onUserStateUpdated,
    onBulkUserStates,
    onUserActivityDetected,
    onForceUserOffline,
    onConnect,
    onDisconnect,
    onError,
  } = options;

  // Store current contexts to manage joins/leaves
  const currentContexts = useRef<Set<string>>(new Set());
  const listenersRef = useRef<Map<string, Function>>(new Map());

  // Store callback refs to avoid useEffect dependency issues
  const callbackRefs = useRef({
    onNotification,
    onUserStateChanged,
    onUserStateUpdated,
    onBulkUserStates,
    onUserActivityDetected,
    onForceUserOffline,
    onConnect,
    onDisconnect,
    onError,
  });

  // Update callback refs when they change
  useEffect(() => {
    callbackRefs.current = {
      onNotification,
      onUserStateChanged,
      onUserStateUpdated,
      onBulkUserStates,
      onUserActivityDetected,
      onForceUserOffline,
      onConnect,
      onDisconnect,
      onError,
    };
  });

  /**
   * Initialize socket connection
   */
  const connect = useCallback(async () => {
    if (!session?.user?.id) {
      console.warn("Cannot connect socket: No user session");
      return null;
    }

    try {
      const socket = await connectSocket(session.user.id);
      return socket;
    } catch (error) {
      console.error("Failed to connect socket:", error);
      return null;
    }
  }, [session?.user?.id]);

  /**
   * Disconnect socket
   */
  const disconnect = useCallback(() => {
    disconnectSocket();
    currentContexts.current.clear();
    listenersRef.current.clear();
  }, []);

  /**
   * Join a context room
   */
  const joinContextRoom = useCallback(
    (contextType: string, contextId: string) => {
      const roomKey = `${contextType}:${contextId}`;

      if (!currentContexts.current.has(roomKey)) {
        joinContext(contextType, contextId);
        currentContexts.current.add(roomKey);
        console.log(`Joined context: ${roomKey}`);
      }
    },
    []
  );

  /**
   * Leave a context room
   */
  const leaveContextRoom = useCallback(
    (contextType: string, contextId: string) => {
      const roomKey = `${contextType}:${contextId}`;

      if (currentContexts.current.has(roomKey)) {
        leaveContext(contextType, contextId);
        currentContexts.current.delete(roomKey);
        console.log(`Left context: ${roomKey}`);
      }
    },
    []
  );

  /**
   * Join multiple contexts
   */
  const joinContexts = useCallback(
    (contexts: Array<{ type: string; id: string }>) => {
      contexts.forEach(({ type, id }) => {
        joinContextRoom(type, id);
      });
    },
    [joinContextRoom]
  );

  /**
   * Leave multiple contexts
   */
  const leaveContexts = useCallback(
    (contexts: Array<{ type: string; id: string }>) => {
      contexts.forEach(({ type, id }) => {
        leaveContextRoom(type, id);
      });
    },
    [leaveContextRoom]
  );

  /**
   * Leave all current contexts
   */
  const leaveAllContexts = useCallback(() => {
    currentContexts.current.forEach((roomKey) => {
      const [type, id] = roomKey.split(":");
      leaveContext(type, id);
    });
    currentContexts.current.clear();
  }, []);

  /**
   * Get connection status
   */
  const getConnectionStatus = useCallback(() => {
    return {
      connected: isSocketConnected(),
      status: getSocketStatus(),
      contexts: Array.from(currentContexts.current),
    };
  }, []);

  /**
   * Update user state via Socket.IO
   */
  const updateSocketUserState = useCallback(
    (userId: string, state: string, roomId?: string, userName?: string) => {
      updateUserState(userId, state, roomId, userName);
    },
    []
  );

  /**
   * Send notification acknowledgment (handled automatically by client)
   */
  const acknowledgeNotification = useCallback((notificationId: string) => {
    // Note: Acknowledgment is now handled automatically by the Pusher client
    // This function is kept for backward compatibility but does nothing
    console.log(`Notification ${notificationId} acknowledged automatically`);
  }, []);

  // Connect socket and setup listeners
  const initializeConnection = async () => {
    try {
      const socket = await connect();
      if (!socket) return;

      // Setup notification listener
      const notificationListener = (data: NotificationSocketData) => {
        console.log("Received notification:", data);

        // Filter notifications for current user
        if (data.userId === session?.user?.id) {
          callbackRefs.current.onNotification?.(data);
          // Note: Acknowledgment is handled automatically by the client
        }
      };

      // Setup connection listeners
      const connectListener = () => {
        console.log("Socket connected");
        callbackRefs.current.onConnect?.();
      };

      const disconnectListener = () => {
        console.log("Socket disconnected");
        callbackRefs.current.onDisconnect?.();
      };

      const errorListener = (error: Error) => {
        console.error("Socket error:", error);
        callbackRefs.current.onError?.(error);
      };

      // Setup user state listeners
      const userStateChangedListener = (data: {
        userId: string;
        userName?: string;
        state: string;
        roomId?: string;
        timestamp: string;
      }) => {
        console.log("User state changed:", data);
        callbackRefs.current.onUserStateChanged?.(data);
      };

      const userStateUpdatedListener = (data: {
        success: boolean;
        userId: string;
        state: string;
        roomId?: string;
        timestamp: string;
      }) => {
        callbackRefs.current.onUserStateUpdated?.(data);
      };

      const bulkUserStatesListener = (data: {
        roomId?: string;
        states: Record<string, string>;
        timestamp: string;
      }) => {
        console.log("Bulk user states received:", data);
        callbackRefs.current.onBulkUserStates?.(data);
      };

      const userActivityDetectedListener = (data: {
        userId: string;
        roomId?: string;
        timestamp: string;
      }) => {
        console.log("User activity detected:", data);
        callbackRefs.current.onUserActivityDetected?.(data);
      };

      const forceUserOfflineListener = (data: {
        userId: string;
        reason?: string;
        timestamp: string;
      }) => {
        console.log("Force user offline:", data);
        callbackRefs.current.onForceUserOffline?.(data);
      };

      // Add event listeners
      addEventListener("notification", notificationListener);
      addEventListener("user_state_changed", userStateChangedListener);
      addEventListener("user_state_updated", userStateUpdatedListener);
      addEventListener("bulk_user_states", bulkUserStatesListener);
      addEventListener("user_activity_detected", userActivityDetectedListener);
      addEventListener("force_user_offline", forceUserOfflineListener);
      addEventListener("connect", connectListener);
      addEventListener("disconnect", disconnectListener);
      addEventListener("connect_error", errorListener);

      // Store listeners for cleanup
      listenersRef.current.set("notification", notificationListener);
      listenersRef.current.set("user_state_changed", userStateChangedListener);
      listenersRef.current.set("user_state_updated", userStateUpdatedListener);
      listenersRef.current.set("connect", connectListener);
      listenersRef.current.set("disconnect", disconnectListener);
      listenersRef.current.set("connect_error", errorListener);

      // Join initial contexts
      if (contexts.length > 0) {
        joinContexts(contexts);
      }
    } catch (error) {
      console.error("Failed to initialize socket connection:", error);
    }
  };

  // Add connection state tracking
  const isInitializing = useRef(false);
  const isInitialized = useRef(false);

  // Setup event listeners
  useEffect(() => {
    if (!autoConnect || !session?.user?.id || isInitializing.current) return;

    isInitializing.current = true;
    initializeConnection().finally(() => {
      isInitializing.current = false;
      isInitialized.current = true;
    });

    // Cleanup function
    return () => {
      // Remove event listeners
      listenersRef.current.forEach((listener, event) => {
        try {
          removeEventListener(
            event as keyof SocketClientEvents,
            listener as any
          );
        } catch (error) {
          console.error("Error removing event listener:", error);
        }
      });
      // Clear listeners ref
      listenersRef.current.clear();
      // Leave all contexts
      leaveAllContexts();
      isInitialized.current = false;
    };
  }, [
    autoConnect,
    session?.user?.id,
    // Removed all unstable function dependencies
  ]);

  // Update contexts when they change
  useEffect(() => {
    if (!isSocketConnected()) return;

    // Get current context keys
    const newContextKeys = new Set(
      contexts.map(({ type, id }) => `${type}:${id}`)
    );
    const currentKeys = currentContexts.current;

    // Leave contexts that are no longer needed
    currentKeys.forEach((key) => {
      if (!newContextKeys.has(key)) {
        const [type, id] = key.split(":");
        leaveContextRoom(type, id);
      }
    });

    // Join new contexts
    contexts.forEach(({ type, id }) => {
      joinContextRoom(type, id);
    });
  }, [contexts, joinContextRoom, leaveContextRoom]);

  return {
    // Connection management
    connect,
    disconnect,
    isConnected: isSocketConnected(),
    status: getConnectionStatus(),

    // Context management
    joinContext: joinContextRoom,
    leaveContext: leaveContextRoom,
    joinContexts,
    leaveContexts,
    leaveAllContexts,
    currentContexts: Array.from(currentContexts.current),

    // User state management
    updateUserState: updateSocketUserState,
    requestRoomMemberStates: (roomId: string) =>
      requestRoomMemberStates(roomId),
    sendUserActivity: (roomId?: string) => sendUserActivity(roomId),
    forceUserOffline: (targetUserId: string, reason?: string) =>
      forceUserOffline(targetUserId, reason),

    // Utilities
    acknowledgeNotification,
    socket: getSocket(),
  };
}
