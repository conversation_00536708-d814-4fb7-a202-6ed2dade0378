"use client";

import { useSelector, useDispatch } from "react-redux";
import { useSession } from "next-auth/react";
import { AppDispatch } from "@/store";
import { toast } from "sonner";
import {
  createCompany,
  updateCompany,
  deleteCompany,
  upsertCompany,
  getAllCompanies,
  CompanyData,
  CompanyResponse,
} from "@/store/actions/company";
import {
  selectCompany,
  selectIsLoading,
  selectUser,
} from "@/store/slices/auth";

export interface UseCompanyReturn {
  // State (from auth slice selector)
  company: CompanyResponse | null | undefined;
  isLoading: boolean;
  user: any;

  // Actions
  createCompany: (data: CompanyData) => Promise<any>;
  updateCompany: (data: CompanyData & { id: string }) => Promise<any>;
  upsertCompany: (data: CompanyData) => Promise<any>;
  deleteCompany: (id: string) => Promise<any>;
  getAllCompanies: (params?: {
    page?: number;
    limit?: number;
    search?: string;
  }) => Promise<any>;

  // Utility functions
  hasCompany: () => boolean;
  getCompanyId: () => string | null;
  getCompanyName: () => string | null;
  isCompanyOwner: (companyId?: string) => boolean;
}

export function useCompany(): UseCompanyReturn {
  const dispatch = useDispatch<AppDispatch>();

  // Session management for updating company data
  const { data: currentSession, update: updateCurrentSession } = useSession();

  // Redux selectors - company data comes from auth slice
  const company = useSelector(selectCompany);
  const isLoading = useSelector(selectIsLoading);
  const user = useSelector(selectUser);

  // Action creators with session updates
  const handleCreateCompany = async (data: CompanyData) => {
    try {
      const result = await dispatch(createCompany(data)).unwrap();

      // Update session with new company data
      await updateCurrentSession({
        user: {
          ...currentSession?.user,
          company: result,
        },
      });

      toast.success("Company created successfully");
      return result;
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to create company";
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleUpdateCompany = async (data: CompanyData & { id: string }) => {
    try {
      const result = await dispatch(updateCompany(data)).unwrap();

      // Update session with updated company data
      await updateCurrentSession({
        user: {
          ...currentSession?.user,
          company: result,
        },
      });

      toast.success("Company updated successfully");
      return result;
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to update company";
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleUpsertCompany = async (data: CompanyData) => {
    try {
      const result = await dispatch(upsertCompany(data)).unwrap();

      // Update session with company data (create or update)
      await updateCurrentSession({
        user: {
          ...currentSession?.user,
          company: result,
        },
      });

      const isUpdate = company?.id;
      toast.success(
        isUpdate
          ? "Company updated successfully"
          : "Company created successfully"
      );
      return result;
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to save company";
      toast.error(errorMessage);
      throw error;
    }
  };

  const handleDeleteCompany = async (id: string) => {
    try {
      const result = await dispatch(deleteCompany(id)).unwrap();

      // Update session to remove company data
      await updateCurrentSession({
        user: {
          ...currentSession?.user,
          company: null,
        },
      });

      toast.success("Company deleted successfully");
      return result;
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to delete company";
      toast.error(errorMessage);
      throw error;
    }
  };

  // Note: getCompany and getCurrentUserCompany removed since company data
  // now comes from auth slice selector instead of API calls

  const handleGetAllCompanies = async (
    params: {
      page?: number;
      limit?: number;
      search?: string;
    } = {}
  ) => {
    try {
      const result = await dispatch(getAllCompanies(params)).unwrap();
      return result;
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to get companies";
      toast.error(errorMessage);
      throw error;
    }
  };

  // Utility functions
  const hasCompany = (): boolean => {
    return !!company?.id;
  };

  const getCompanyId = (): string | null => {
    return company?.id || null;
  };

  const getCompanyName = (): string | null => {
    return company?.name || null;
  };

  const isCompanyOwner = (companyId?: string): boolean => {
    if (!user?.id || !company?.id) return false;

    // If companyId is provided, check if it matches user's company
    if (companyId) {
      return company.id === companyId;
    }

    // Otherwise, just check if user has a company
    return hasCompany();
  };

  return {
    // State (from auth slice selector)
    company,
    isLoading,
    user,

    // Actions
    createCompany: handleCreateCompany,
    updateCompany: handleUpdateCompany,
    upsertCompany: handleUpsertCompany,
    deleteCompany: handleDeleteCompany,
    getAllCompanies: handleGetAllCompanies,

    // Utility functions
    hasCompany,
    getCompanyId,
    getCompanyName,
    isCompanyOwner,
  };
}

// Export types for external use
export type { CompanyData, CompanyResponse };
