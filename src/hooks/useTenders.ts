"use client";

import { useCallback, useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import useSWR from "swr";
import type { AppDispatch } from "@/store";
import { fetcher } from "@/lib/common/requests";
import { useSearch, type SearchConfig, type SearchParams } from "@/lib/search";
import { createPagination } from "@/lib/pagination";
import {
  createTender,
  updateTender,
  deleteTender,
  upsertTender,
  searchTenders,
  seedTenders,
  bulkUpsertTenders,
  type TenderCreateData,
  type TenderUpdateData,
  type TenderSearchQuery,
  type TenderResponse,
} from "@/store/actions/tender";

// Hook for managing all tenders with SWR and integrated search/pagination
export function useTenders(initialQuery?: Record<string, any>) {
  const dispatch = useDispatch<AppDispatch>();

  // State management
  const [isSearching, setIsSearching] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredTenders, setFilteredTenders] = useState<TenderResponse[]>([]);

  // Initialize pagination
  const [pagination] = useState(() =>
    createPagination({
      page: initialQuery?.page || 1,
      limit: initialQuery?.limit || 10,
      orderBy: { createdAt: "desc" },
    })
  );

  // Build query string for SWR key
  const queryString = initialQuery
    ? `?${new URLSearchParams(
        Object.entries(initialQuery).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            acc[key] = String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      ).toString()}`
    : "";

  // SWR hook for fetching tenders list
  const {
    data: tendersData,
    error: tendersError,
    mutate: mutateTenders,
    isLoading: isLoadingTenders,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse[];
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }>(`tender${queryString}`, fetcher);

  // Extract data from SWR response
  const tenders = tendersData?.data || [];
  const paginationInfo = tendersData?.pagination || {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const error = tendersError;
  const isLoading = isLoadingTenders;

  // Update pagination state when data changes
  useEffect(() => {
    if (paginationInfo) {
      pagination.page = paginationInfo.page;
      pagination.limit = paginationInfo.limit;
      pagination.total = paginationInfo.total;
    }
  }, [paginationInfo, pagination]);

  // Configure search functionality
  const searchConfig: SearchConfig<TenderResponse, TenderResponse> = {
    // Data transformation (API data is already in UI format for tenders)
    adaptApiToUI: (apiData: TenderResponse) => apiData,

    // Define searchable fields
    searchFields: (tender: TenderResponse) => [
      tender.ocid || "",
      tender.description || "",
      tender.procuring_entity || "",
      tender.category || "",
      tender.status || "",
    ],

    // State setters
    setFilteredData: setFilteredTenders,
    setSearchTerm,
    setIsSearching,

    // Redux search action
    searchAction: (params: SearchParams) =>
      searchTenders({
        query: params.query,
        page: params.page,
        limit: params.limit,
        ...params,
      } as TenderSearchQuery),

    // Current data
    currentData: tenders,
    loadedApiData: tenders,

    // Debounce configuration
    debounceDelay: 800,
  };

  // Initialize search function
  const searchFunction = useSearch(dispatch, searchConfig);

  // Refresh tenders data
  const refreshTenders = useCallback(() => {
    mutateTenders();
  }, [mutateTenders]);

  // Create tender action
  const createNewTender = useCallback(
    async (tenderData: TenderCreateData) => {
      try {
        const result = await dispatch(createTender(tenderData));
        if (createTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful creation
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            (result.payload as any)?.message || "Failed to create tender"
          );
        }
      } catch (error) {
        console.error("Failed to create tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Update tender action
  const updateExistingTender = useCallback(
    async (tenderData: TenderUpdateData) => {
      try {
        const result = await dispatch(updateTender(tenderData));
        if (updateTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful update
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            (result.payload as any)?.message || "Failed to update tender"
          );
        }
      } catch (error) {
        console.error("Failed to update tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Delete tender action
  const deleteExistingTender = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(deleteTender(id));
        if (deleteTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful deletion
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            (result.payload as any)?.message || "Failed to delete tender"
          );
        }
      } catch (error) {
        console.error("Failed to delete tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Upsert tender action
  const upsertExistingTender = useCallback(
    async (tenderData: TenderCreateData) => {
      try {
        const result = await dispatch(upsertTender(tenderData));
        if (upsertTender.fulfilled.match(result)) {
          // Refresh SWR cache after successful upsert
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            (result.payload as any)?.message || "Failed to save tender"
          );
        }
      } catch (error) {
        console.error("Failed to upsert tender:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Enhanced search function using the global search library
  const performSearch = useCallback(
    async (query: string, additionalParams?: Record<string, any>) => {
      const searchParams: SearchParams = {
        query: query.trim(),
        page: pagination.page,
        limit: pagination.limit,
        ...additionalParams,
      };

      return await searchFunction(searchParams);
    },
    [searchFunction, pagination]
  );

  // Pagination controls
  const goToPage = useCallback(
    (page: number) => {
      pagination.goToPage(page);
      mutateTenders(); // Refresh data with new page
    },
    [pagination, mutateTenders]
  );

  const nextPage = useCallback(() => {
    if (pagination.hasNextPage) {
      pagination.nextPage();
      mutateTenders();
    }
  }, [pagination, mutateTenders]);

  const previousPage = useCallback(() => {
    if (pagination.hasPreviousPage) {
      pagination.previousPage();
      mutateTenders();
    }
  }, [pagination, mutateTenders]);

  const setPageSize = useCallback(
    (limit: number) => {
      pagination.limit = limit;
      pagination.firstPage(); // Reset to first page when changing page size
      mutateTenders();
    },
    [pagination, mutateTenders]
  );

  // Seed tenders action
  const seedTendersFunction = useCallback(async () => {
    try {
      const result = await dispatch(seedTenders());
      if (seedTenders.fulfilled.match(result)) {
        // Refresh SWR cache after successful seeding
        mutateTenders();
        return result.payload;
      } else {
        throw new Error(
          (result.payload as any)?.message || "Failed to seed tenders"
        );
      }
    } catch (error) {
      console.error("Failed to seed tenders:", error);
      throw error;
    }
  }, [dispatch, mutateTenders]);

  // Bulk upsert tenders action
  const bulkUpsertTendersFunction = useCallback(
    async (tenders: TenderCreateData[]) => {
      try {
        const result = await dispatch(bulkUpsertTenders(tenders));
        if (bulkUpsertTenders.fulfilled.match(result)) {
          // Refresh SWR cache after successful bulk operation
          mutateTenders();
          return result.payload;
        } else {
          throw new Error(
            (result.payload as any)?.message ||
              "Failed to perform bulk operation"
          );
        }
      } catch (error) {
        console.error("Failed to bulk upsert tenders:", error);
        throw error;
      }
    },
    [dispatch, mutateTenders]
  );

  // Clear search and reset filters
  const clearSearch = useCallback(() => {
    setSearchTerm("");
    setFilteredTenders([]);
    setIsSearching(false);
  }, []);

  // Get current data to display (filtered or all tenders)
  const displayTenders =
    filteredTenders.length > 0 || searchTerm ? filteredTenders : tenders;

  return {
    // State (from SWR and search)
    tenders: displayTenders,
    allTenders: tenders, // Original unfiltered data
    filteredTenders,
    searchTerm,
    pagination: {
      ...paginationInfo,
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages: pagination.totalPages,
      hasNextPage: pagination.hasNextPage,
      hasPreviousPage: pagination.hasPreviousPage,
    },
    isLoading,
    isSearching,
    error,

    // Data refresh actions (SWR-based)
    refreshTenders,

    // Search actions (using global search library)
    search: performSearch,
    clearSearch,

    // Pagination actions (using global pagination library)
    goToPage,
    nextPage,
    previousPage,
    setPageSize,

    // CRUD actions (Redux-based with SWR refresh)
    createTender: createNewTender,
    updateTender: updateExistingTender,
    deleteTender: deleteExistingTender,
    upsertTender: upsertExistingTender,
    seedTenders: seedTendersFunction,
    bulkUpsertTenders: bulkUpsertTendersFunction,
  };
}

// Hook for getting a specific tender by ID using SWR
export function useTender(tenderId: string | null) {
  const {
    data: tenderData,
    error,
    mutate,
    isLoading,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse;
  }>(tenderId ? `tender/${tenderId}` : null, fetcher);

  return {
    tender: tenderData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}

// Hook for getting a tender by OCID using SWR
export function useTenderByOcid(ocid: string | null) {
  const {
    data: tenderData,
    error,
    mutate,
    isLoading,
  } = useSWR<{
    success: boolean;
    error: boolean;
    message: string;
    data: TenderResponse;
  }>(ocid ? `tender/ocid/${ocid}` : null, fetcher);

  return {
    tender: tenderData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}
