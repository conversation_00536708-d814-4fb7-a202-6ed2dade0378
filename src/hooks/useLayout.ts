"use client";

import { useCallback, useMemo } from "react";
import { usePreference } from "./usePreference";

export type LayoutType = "basic" | "advanced";

export interface LayoutSettings {
  layout: LayoutType;
  // Add other layout-related settings here
  viewMode?: "table" | "grid" | "list";
  showToolbar?: boolean;
  enableSearch?: boolean;
  enableFilters?: boolean;
}

export const useLayout = () => {
  const {
    layout,
    preferences,
    setPreferenceLayout,
    updatePreferences,
    isLoading,
    isSaving,
    error,
  } = usePreference();

  // Get current layout type
  const layoutType: LayoutType = layout || "basic";

  // Check if current layout is basic
  const isBasic = layoutType === "basic";

  // Check if current layout is advanced
  const isAdvanced = useMemo(() => {
    return layoutType === "advanced";
  }, [layoutType]);

  // Switch to basic layout
  const switchToBasic = useCallback(() => {
    setPreferenceLayout("basic");
  }, [setPreferenceLayout]);

  // Switch to advanced layout
  const switchToAdvanced = useCallback(() => {
    setPreferenceLayout("advanced");
  }, [setPreferenceLayout]);

  // Toggle between layouts
  const toggleLayout = useCallback(() => {
    const newLayout = layoutType === "basic" ? "advanced" : "basic";
    setPreferenceLayout(newLayout);
  }, [layoutType, setPreferenceLayout]);

  // Set layout type
  const setLayout = useCallback(
    (layout: LayoutType) => {
      setPreferenceLayout(layout);
    },
    [setPreferenceLayout]
  );

  // Update layout settings
  const updateLayoutSettings = useCallback(
    (newSettings: Partial<LayoutSettings>) => {
      updatePreferences(newSettings);
    },
    [updatePreferences]
  );

  // Get layout-specific capabilities
  const capabilities = {
    hasToolbar: isAdvanced,
    hasDynamicIcons: isAdvanced,
    hasAdvancedFilters: isAdvanced,
    hasViewModeToggle: isAdvanced,
    hasHeader: isBasic,
    hasFilters: isBasic,
    hasControls: isBasic,
  };

  return {
    // Current state
    layoutType,
    isBasic,
    isAdvanced,
    preferences: preferences as LayoutSettings,
    settings: preferences as LayoutSettings, // Alias for backward compatibility
    capabilities,

    // Loading and error states
    isLoading,
    isSaving,
    error,

    // Actions
    switchToBasic,
    switchToAdvanced,
    toggleLayout,
    setLayout,
    updateLayoutSettings,
  };
};

export default useLayout;
