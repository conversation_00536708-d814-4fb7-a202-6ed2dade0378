import { useDispatch, useSelector } from "react-redux";
import { useCallback, useMemo } from "react";
import { RootState } from "@/store";
import {
  setPagination,
  setPage,
  setLimit,
  setOrderBy,
  setCursor,
  setLoading,
  updateTotal,
  nextPage,
  previousPage,
  firstPage,
  lastPage,
  resetPagination,
  selectPaginationContext,
  PaginationState,
} from "@/store/slices/pagination";

export interface UsePaginationOptions {
  tableId?: string; // Optional since it's handled by the hook parameter
  initialPage?: number;
  initialLimit?: number;
  initialOrderBy?: Record<string, "asc" | "desc">;
  autoFetch?: boolean;
  service?: any; // Service instance for automatic data fetching
}

export interface UsePaginationReturn {
  // Current pagination state
  pagination: PaginationState;

  // Initialization
  initPagination: (options: UsePaginationOptions) => void;

  // Actions
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setOrderBy: (orderBy: Record<string, "asc" | "desc">) => void;
  setCursor: (cursor: string | null) => void;
  setLoading: (loading: boolean) => void;
  updateTotal: (total: number) => void;

  // Navigation actions
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
  goToPage: (page: number) => void;

  // Utility actions
  reset: () => void;

  // Computed values for TableView compatibility
  tableViewPagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
  };
}

export const usePagination = (tableId: string): UsePaginationReturn => {
  const dispatch = useDispatch();

  const pagination = useSelector((state: RootState) =>
    selectPaginationContext(state, tableId)
  );

  // Initialize pagination function
  const initPagination = useCallback(
    (options: UsePaginationOptions) => {
      const {
        initialPage = 1,
        initialLimit = 10,
        initialOrderBy = { createdAt: "desc" },
      } = options;

      if (pagination) return;

      dispatch(
        setPagination({
          tableId,
          pagination: {
            page: initialPage,
            limit: initialLimit,
            orderBy: initialOrderBy,
          },
        })
      );
    },
    [tableId, dispatch]
  );

  // Action creators bound to the specific table ID
  const actions = {
    setPage: useCallback(
      (page: number) => {
        dispatch(setPage({ tableId, page }));
      },
      [dispatch, tableId]
    ),

    setLimit: useCallback(
      (limit: number) => {
        dispatch(setLimit({ tableId, limit }));
      },
      [dispatch, tableId]
    ),

    setOrderBy: useCallback(
      (orderBy: Record<string, "asc" | "desc">) => {
        dispatch(setOrderBy({ tableId, orderBy }));
      },
      [dispatch, tableId]
    ),

    setCursor: useCallback(
      (cursor: string | null) => {
        dispatch(setCursor({ tableId, cursor }));
      },
      [dispatch, tableId]
    ),

    setLoading: useCallback(
      (loading: boolean) => {
        dispatch(setLoading({ tableId, loading }));
      },
      [dispatch, tableId]
    ),

    updateTotal: useCallback(
      (total: number) => {
        dispatch(updateTotal({ tableId, total }));
      },
      [dispatch, tableId]
    ),

    goToNextPage: useCallback(() => {
      dispatch(nextPage({ tableId }));
    }, [dispatch, tableId]),

    goToPreviousPage: useCallback(() => {
      dispatch(previousPage({ tableId }));
    }, [dispatch, tableId]),

    goToFirstPage: useCallback(() => {
      dispatch(firstPage({ tableId }));
    }, [dispatch, tableId]),

    goToLastPage: useCallback(() => {
      dispatch(lastPage({ tableId }));
    }, [dispatch, tableId]),

    goToPage: useCallback(
      (page: number) => {
        dispatch(setPage({ tableId, page }));
      },
      [dispatch, tableId]
    ),

    reset: useCallback(() => {
      dispatch(resetPagination({ tableId }));
    }, [dispatch, tableId]),
  };

  // Create TableView-compatible pagination object
  const tableViewPagination = useMemo(
    () => ({
      currentPage: pagination.page,
      totalPages: pagination.totalPages,
      totalItems: pagination.total,
      itemsPerPage: pagination.limit,
      onPageChange: actions.goToPage,
    }),
    [
      pagination.page,
      pagination.totalPages,
      pagination.total,
      pagination.limit,
      actions.goToPage,
    ]
  );

  return {
    pagination,
    initPagination,
    ...actions,
    tableViewPagination,
  };
};
