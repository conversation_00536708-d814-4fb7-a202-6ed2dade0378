"use client";

import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectSettings,
  selectPreferences,
  selectLayout,
  selectTheme,
  selectLanguage,
  selectTimezone,
  selectDateFormat,
  selectIsLoading,
  selectIsSaving,
  selectError,
  setSettings,
  setPreferenceSettings,
  updatePreferenceSettings,
  resetPreferenceSettings,
  setLoading,
  setSaving,
  setError,
  clearError,
  resetSettings,
  type SettingsState,
  type MiscellaneousSettings,
} from "@/store/slices/settings";
import type { RootState } from "@/store";
import { useTheme } from "@/providers/theme";

export const usePreference = () => {
  const dispatch = useDispatch();
  const { setTheme } = useTheme();

  const settings = useSelector((state: RootState) => selectSettings(state));
  const preferences = useSelector((state: RootState) =>
    selectPreferences(state)
  );

  const layout = useSelector((state: RootState) => selectLayout(state));
  const theme = useSelector((state: RootState) => selectTheme(state));
  const language = useSelector((state: RootState) => selectLanguage(state));
  const timezone = useSelector((state: RootState) => selectTimezone(state));
  const dateFormat = useSelector((state: RootState) => selectDateFormat(state));
  const isLoading = useSelector((state: RootState) => selectIsLoading(state));
  const isSaving = useSelector((state: RootState) => selectIsSaving(state));
  const error = useSelector((state: RootState) => selectError(state));

  useEffect(() => {
    setTheme(theme);
  }, [theme, setTheme]);

  // Settings management
  const setAllSettings = useCallback(
    (settings: Partial<SettingsState>) => {
      dispatch(setSettings(settings));
    },
    [dispatch]
  );

  // Preference management
  const setAllPreferences = useCallback(
    (preferences: MiscellaneousSettings) => {
      dispatch(setPreferenceSettings(preferences));
    },
    [dispatch]
  );

  const updatePreferences = useCallback(
    (preferences: Partial<MiscellaneousSettings>) => {
      dispatch(updatePreferenceSettings(preferences));
    },
    [dispatch]
  );

  const setPreferenceLayout = useCallback(
    (layout: "basic" | "advanced") => {
      dispatch(updatePreferenceSettings({ layout }));
    },
    [dispatch]
  );

  const setPreferenceTheme = useCallback(
    (theme: "light" | "dark") => {
      dispatch(updatePreferenceSettings({ theme }));
    },
    [dispatch]
  );

  const setPreferenceLanguage = useCallback(
    (language: string) => {
      dispatch(updatePreferenceSettings({ language }));
    },
    [dispatch]
  );

  const setPreferenceTimezone = useCallback(
    (timezone: string) => {
      dispatch(updatePreferenceSettings({ timezone }));
    },
    [dispatch]
  );

  const setPreferenceDateFormat = useCallback(
    (dateFormat: string) => {
      dispatch(updatePreferenceSettings({ dateFormat }));
    },
    [dispatch]
  );

  const resetPreferences = useCallback(() => {
    dispatch(resetPreferenceSettings());
  }, [dispatch]);

  // Notification management removed - use useNotifications hook instead

  // Loading and error management
  const setLoadingState = useCallback(
    (loading: boolean) => {
      dispatch(setLoading(loading));
    },
    [dispatch]
  );

  const setSavingState = useCallback(
    (saving: boolean) => {
      dispatch(setSaving(saving));
    },
    [dispatch]
  );

  const setErrorState = useCallback(
    (error: string | null) => {
      dispatch(setError(error));
    },
    [dispatch]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const resetAllSettings = useCallback(() => {
    dispatch(resetSettings());
  }, [dispatch]);

  return {
    // Full settings object
    settings,
    preferences,

    // Individual preference settings
    layout,
    theme,
    language,
    timezone,
    dateFormat,

    // Loading and error states
    isLoading,
    isSaving,
    error,

    // Settings management
    setAllSettings,
    resetAllSettings,

    // Preference management
    setAllPreferences,
    updatePreferences,
    setPreferenceLayout,
    setPreferenceTheme,
    setPreferenceLanguage,
    setPreferenceTimezone,
    setPreferenceDateFormat,
    resetPreferences,

    // Loading and error management
    setLoadingState,
    setSavingState,
    setErrorState,
    clearErrorState,
  };
};
