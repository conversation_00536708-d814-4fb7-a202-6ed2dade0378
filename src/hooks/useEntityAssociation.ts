"use client";

import { useState, useCallback } from "react";
import {
  fetchEntityInstances,
  type EntityInstance,
} from "@/components/common/entity-association/entity-types";

export interface EntityAssociationResult {
  association_table: string | null;
  association_id: string | null;
  association_ids: string[] | null;
}

export interface EntityAssociationState {
  selectedTable: string;
  selectedId: string;
  selectedIds: string[];
  entities: any[];
  loading: boolean;
  error: string | null;
}

export interface UseEntityAssociationReturn {
  state: EntityAssociationState;
  result: EntityAssociationResult;
  handleTableChange: (table: string) => void;
  handleIdChange: (id: string) => void;
  handleIdsChange: (ids: string[]) => void;
  reset: () => void;
  getEntityDisplayName: (entity: any, entityType: string) => string;
  setCustomEntities: (entities: any[]) => void;
}

export function useEntityAssociation(): UseEntityAssociationReturn {
  const [state, setState] = useState<EntityAssociationState>({
    selectedTable: "",
    selectedId: "",
    selectedIds: [],
    entities: [],
    loading: false,
    error: null,
  });

  // Get display name for entity based on type
  const getEntityDisplayName = useCallback(
    (entity: any, entityType: string): string => {
      switch (entityType) {
        case "contracts":
          return `${entity.name || entity.id} - ${entity.status || "Contract"}`;
        case "documents":
          return `${entity.name || entity.id} - ${entity.type || "Document"}`;
        case "proposals":
          return `${entity.name || entity.id} - ${entity.status || "Proposal"}`;
        case "constituante":
          return `${entity.name || entity.id} - ${
            entity.type || "Constituante"
          }`;
        default:
          return entity.name || entity.id || "Unknown";
      }
    },
    []
  );

  // Fetch entities based on selected entity type
  const fetchEntities = useCallback(async (entityType: string) => {
    if (!entityType) {
      setState((prev) => ({ ...prev, entities: [], error: null }));
      return;
    }

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetchEntityInstances(entityType, { limit: 100 });

      if (response.success) {
        // Convert EntityInstance[] to the format expected by the component
        const formattedEntities = response.data.map(
          (entity: EntityInstance) => ({
            id: entity.id,
            name: entity.name,
          })
        );

        setState((prev) => ({
          ...prev,
          entities: formattedEntities,
          loading: false,
        }));
      } else {
        setState((prev) => ({
          ...prev,
          entities: [],
          loading: false,
          error: response.error || "Failed to fetch entities",
        }));
      }
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        entities: [],
        loading: false,
        error: error.message || "Failed to fetch entities",
      }));
    }
  }, []);

  // Handle entity type change
  const handleTableChange = useCallback(
    (table: string) => {
      // Ensure only serializable string values are stored
      const serializedTable =
        typeof table === "string" ? table : String(table || "");

      setState((prev) => ({
        ...prev,
        selectedTable: serializedTable,
        selectedId: "",
        selectedIds: [],
        entities: [],
        error: null,
      }));

      if (serializedTable) {
        fetchEntities(serializedTable);
      }
    },
    [fetchEntities]
  );

  // Handle entity ID change (single selection)
  const handleIdChange = useCallback((id: string) => {
    // Ensure only serializable string values are stored
    const serializedId = typeof id === "string" ? id : String(id || "");
    setState((prev) => ({ ...prev, selectedId: serializedId }));
  }, []);

  // Handle entity IDs change (multiple selection)
  const handleIdsChange = useCallback((ids: string[]) => {
    setState((prev) => ({ ...prev, selectedIds: ids }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      selectedTable: "",
      selectedId: "",
      selectedIds: [],
      entities: [],
      loading: false,
      error: null,
    });
  }, []);

  // Set custom entities (for special cases like invoices)
  const setCustomEntities = useCallback((entities: any[]) => {
    setState((prev) => ({ ...prev, entities, loading: false, error: null }));
  }, []);

  // Compute result
  const result: EntityAssociationResult = {
    association_table: state.selectedTable || null,
    association_id: state.selectedId || null,
    association_ids: state.selectedIds.length > 0 ? state.selectedIds : null,
  };

  return {
    state,
    result,
    handleTableChange,
    handleIdChange,
    handleIdsChange,
    reset,
    getEntityDisplayName,
    setCustomEntities,
  };
}
