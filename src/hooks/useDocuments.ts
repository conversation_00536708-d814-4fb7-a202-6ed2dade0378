"use client";

import { useCallback, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { toast } from "sonner";
import type { RootState, AppDispatch } from "@/store";
import { fetcher, api } from "@/lib/common/requests";
import type { Document as ApiDocument } from "@/lib/api/validators/schemas/document";
import {
  fetchSignedURL,
  bulkCreateDocuments,
  createDocument,
  searchDocuments,
} from "@/store/actions/documents";
import {
  clearError,
  setCurrentDocument,
  clearCurrentDocument,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
} from "@/store/slices/document";
import type { UpdateDocument } from "@/lib/api/validators/schemas/document";
import type { Document } from "@/components/common/types";
import { usePagination } from "./usePagination";
import { createSearchFunction, type SearchConfig } from "@/lib/search";

const adaptApiDocumentToUI = (apiDocument: ApiDocument): Document => {
  return {
    id: apiDocument.id,
    name: apiDocument.name,
    path: apiDocument.path,
    file_type: apiDocument.file_type,
    size: apiDocument.size, // Both API and UI use string for size
    status: apiDocument.status as Document["status"],
    category: apiDocument.category,
    association_entity: apiDocument.association_entity,
    association_id: apiDocument.association_id,
    proposalId: apiDocument.proposalId,
    createdAt: new Date(apiDocument.createdAt).toISOString(),
    updatedAt: new Date(apiDocument.updatedAt).toISOString(),
  };
};

// Contrary function: adapt UI Document to API Document object shape
const adaptUIDocumentToAPI = (uiDocument: Document): Partial<ApiDocument> => {
  return {
    id: uiDocument.id,
    name: uiDocument.name,
    path: uiDocument.path,
    file_type: uiDocument.file_type,
    size: uiDocument.size, // Both API and UI use string for size
    status: uiDocument.status,
    category: uiDocument.category,
    association_entity: uiDocument.association_entity,
    association_id: uiDocument.association_id,
    proposalId: uiDocument.proposalId,
    // Note: createdAt and updatedAt are handled by the API
  };
};

export function useDocuments() {
  const dispatch = useDispatch<AppDispatch>();

  const [searchTerm, setSearchTerm] = useState("");
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<ApiDocument[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Initialize pagination hook for documents table
  const paginationHook = usePagination("documents-table");

  // Use SWR for data fetching instead of Redux selectors
  const {
    data: documentsData,
    error: documentsError,
    mutate: mutateDocuments,
    isLoading: isLoadingDocuments,
  } = useSWR("document", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    mutate: mutateStatistics,
    isLoading: isLoadingStatistics,
  } = useSWR("document/statistics", fetcher);

  // Only use Redux for current document and operation states
  const {
    currentDocument,
    isCreating,
    isUpdating,
    isDeleting,
    error: reduxError,
  } = useSelector((state: RootState) => state.documents);

  // Extract data from SWR responses (API response format)
  const serverPagination = documentsData?.pagination;
  const statistics = statisticsData?.data || {
    total: 0,
    created: 0,
    submitted: 0,
    received: 0,
    negotiating: 0,
    agreed: 0,
    inprogress: 0,
    reviewing: 0,
    completed: 0,
    totalSize: 0,
    averageSize: 0,
  };

  function setUiDocuments(documents: ApiDocument[]) {
    if (!documents || documents.length === 0) return;
    const uiDocuments = documents.map(adaptApiDocumentToUI);
    setDocuments(uiDocuments);
  }

  useEffect(() => {
    // Reset Documents Data on search term clear
    if (!searchTerm) {
      setUiDocuments(documentsData?.data);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (filteredDocuments) {
      setUiDocuments(filteredDocuments);
    }
  }, [filteredDocuments]);

  useEffect(() => {
    if (documentsData) {
      setUiDocuments(documentsData.data);
    }
  }, [documentsData]);

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (documentsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 15,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [documentsData, serverPagination]);

  // Combine errors from SWR and Redux
  const error = reduxError || documentsError || statisticsError;
  const isLoading =
    isLoadingDocuments ||
    isLoadingStatistics ||
    isCreating ||
    isUpdating ||
    isDeleting;

  // Refresh data functions using SWR mutate
  const refreshDocuments = useCallback(() => {
    mutateDocuments();
  }, [mutateDocuments]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutateDocuments();
    mutateStatistics();
  }, [mutateDocuments, mutateStatistics]);

  // Search configuration for the composable search function
  const searchConfig: SearchConfig<Document, ApiDocument> = {
    adaptApiToUI: adaptApiDocumentToUI,
    searchFields: (document: ApiDocument) => [
      document.name || "",
      document.file_type || "",
      document.category || "",
      document.path || "",
      document.size?.toString() || "",
    ],
    setFilteredData: setFilteredDocuments,
    setSearchTerm,
    setIsSearching,
    searchAction: searchDocuments,
    currentData: documents,
    loadedApiData: documentsData?.data,
    debounceDelay: 1400,
  };

  // Create the composable search function
  const { searchFunction, cleanup } = createSearchFunction(
    dispatch,
    searchConfig
  );

  // Search Documents (using composable function)
  const searchDocumentsFunction = useCallback(
    async (searchParams: { query?: string; page?: number; limit?: number }) => {
      return await searchFunction(searchParams);
    },
    [searchFunction]
  );

  // Create new document
  const createNewDocument = useCallback(
    async (documentData: FormData) => {
      try {
        const result = await dispatch(createDocument(documentData as any));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          toast.success("Document created successfully");
          mutateDocuments(); // Refresh documents data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to create document";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateDocuments, mutateStatistics]
  );

  // Update existing document
  const updateExistingDocument = useCallback(
    async (documentId: string, documentData: Partial<UpdateDocument>) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await api.put(`document/${documentId}`, {
          id: documentId,
          ...documentData,
        });

        // Handle response
        if (result.success) {
          toast.success("Document updated successfully");

          // Update current document if it's the one being updated
          if (currentDocument?.id === documentId) {
            dispatch(setCurrentDocument(result.data));
          }

          // Refresh data after update
          mutateDocuments();
          mutateStatistics();
          return { success: true, data: result.data };
        } else {
          const errorMessage = result.message || "Failed to update document";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update document";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  // Delete document
  const deleteExistingDocument = useCallback(
    async (documentId: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        const result = await api.delete(`document/${documentId}`);

        // Handle response
        if (result.success) {
          toast.success("Document deleted successfully");

          // Clear current document if it's the one being deleted
          if (currentDocument?.id === documentId) {
            dispatch(setCurrentDocument(null));
          }

          // Refresh data after deletion
          mutateDocuments();
          mutateStatistics();
          return { success: true, data: result.data };
        } else {
          const errorMessage = result.message || "Failed to delete document";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete document";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  // Fetch single document by ID
  const fetchSingleDocument = useCallback(async (documentId: string) => {
    try {
      const response = await api.get(`document/${documentId}`);
      if (response.success) {
        dispatch(setCurrentDocument(response.data));
        return { success: true, data: response.data };
      } else {
        toast.error(response.message || "Failed to fetch document");
        return { success: false, error: response.message };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch document";
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  const bulkDocuments = useCallback((data: any) => {
    try {
      bulkCreateDocuments(data);
    } catch (error: any) {
      console.error("Failed to bulk create documents:", error);
      dispatch(setError(error.message));
      throw error;
    }
  }, []);

  // Clear error
  const clearDocumentError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set current document (expects UI document, converts to API for Redux)
  const setDocument = useCallback(
    (document: Document | null) => {
      // Convert UI document to API document for Redux storage
      const apiDocument = document
        ? (adaptUIDocumentToAPI(document) as ApiDocument)
        : null;
      dispatch(setCurrentDocument(apiDocument));
    },
    [dispatch]
  );

  // Clear current document
  const clearDocument = useCallback(() => {
    dispatch(clearCurrentDocument());
  }, [dispatch]);

  // Initialize documents data using SWR
  const initializeDocuments = useCallback(() => {
    refreshData();
  }, [refreshData]);

  const fetchSignedDocumentURL = async (path: string) => {
    try {
      return await dispatch(fetchSignedURL(path)).unwrap();
    } catch (error: any) {
      return error;
    }
  };

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  // Download document using fetch API and blob
  const downloadDocument = useCallback(async (doc: any) => {
    try {
      if (!doc || !doc.path) {
        throw new Error("Document file URL not available");
      }

      // Fetch the document as a blob
      const response = await fetch(doc.path);
      if (!response.ok) {
        throw new Error(`Failed to fetch document: ${response.statusText}`);
      }

      const blob = await response.blob();

      // Create a URL object for the blob
      const url = URL.createObjectURL(blob);

      // Create a temporary anchor element to trigger download
      const link = document.createElement("a");
      link.href = url;
      link.download = doc.name || `document.${getFileExtension(doc.name)}`;
      link.target = "_blank";

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Failed to download document:", error);
      throw error;
    }
  }, []);

  // Make a copy of document by downloading and re-uploading with FormData
  const copyDocument = useCallback(
    async (doc: Document) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        // Step 1: Download the original document as a blob
        const response = await fetch(doc.path);
        if (!response.ok) {
          throw new Error(`Failed to fetch document: ${response.statusText}`);
        }

        const blob = await response.blob();

        // Step 2: Create a new file name with "Copy" suffix
        const originalName = doc.name;
        const fileExtension = originalName.split(".").pop();
        const nameWithoutExtension = originalName.replace(
          `.${fileExtension}`,
          ""
        );
        const newFileName = `${nameWithoutExtension} - Copy.${fileExtension}`;

        // Step 3: Create a File object from the blob
        const file = new File([blob], newFileName, { type: blob.type });

        // Step 4: Create FormData and append the file and metadata
        const formData = new FormData();
        formData.append("file", file);
        formData.append("name", newFileName);
        formData.append("category", doc.category || "");
        formData.append("status", doc.status);
        formData.append("association_entity", doc.association_entity || "");
        formData.append("association_id", doc.association_id || "");
        if (doc.proposalId) {
          formData.append("proposalId", doc.proposalId);
        }

        // Step 5: Use the Redux createDocument action with FormData
        const result = await dispatch(createDocument(formData as any)).unwrap();

        // Refresh data after creation
        await mutateDocuments();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to copy document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateDocuments, mutateStatistics]
  );

  // Trigger search - the hook handles debouncing and API calls
  useEffect(() => {
    searchDocumentsFunction({ query: searchTerm }).catch((error) => {
      toast.error("Search failed", error);
    });
  }, [searchTerm]);

  // Cleanup search function on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // State (from SWR)
    documents,
    currentDocument,
    statistics,
    searchTerm,
    isLoading,
    isSearching,
    error,

    // Pagination
    pagination: paginationHook,

    // Data refresh actions (SWR-based)
    refreshDocuments,
    refreshStatistics,
    refreshData,

    // CRUD actions (Redux-based with SWR refresh)
    fetchDocument: fetchSingleDocument,
    createDocument: createNewDocument,
    updateDocument: updateExistingDocument,
    deleteDocument: deleteExistingDocument,
    searchDocuments: searchDocumentsFunction,
    initializeDocuments,

    // Legacy API operations (for backward compatibility)
    create: createNewDocument,
    fetchById: fetchSingleDocument,
    update: updateExistingDocument,
    remove: deleteExistingDocument,
    bulkDocuments,
    downloadDocument,
    copyDocument,
    fetchSignedURL: fetchSignedDocumentURL,

    // Utility actions
    clearError: clearDocumentError,
    setCurrentDocument: setDocument,
    clearCurrentDocument: clearDocument,
    setSearchTerm,

    // SWR utilities
    mutateDocuments,
    mutateStatistics,

    // Data transformation utilities
    adaptUIDocumentToAPI,

    // Computed values
    hasDocuments: documents.length > 0,
    documentsCount: documents.length,
    isAnyLoading:
      isLoading ||
      isCreating ||
      isUpdating ||
      isDeleting ||
      isLoadingStatistics,
  };
}

// Hook for getting a specific document using SWR
export function useDocument(documentId: string) {
  const {
    data: documentData,
    error,
    mutate,
    isLoading,
  } = useSWR(documentId ? `document/${documentId}` : null, fetcher);

  return {
    document: documentData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}

// Hook for document statistics using SWR
export function useDocumentStatistics() {
  const {
    data: statisticsData,
    error,
    mutate,
    isLoading,
  } = useSWR("document/statistics", fetcher);

  return {
    statistics: statisticsData?.data || {
      total: 0,
      created: 0,
      submitted: 0,
      received: 0,
      negotiating: 0,
      agreed: 0,
      inprogress: 0,
      reviewing: 0,
      completed: 0,
      totalSize: 0,
      averageSize: 0,
    },
    error,
    isLoading,
    refresh: mutate,
  };
}
