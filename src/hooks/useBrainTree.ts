import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/common/requests";
import {
  initializeBraintree,
  createTransaction,
  createCustomer,
  createPaymentMethod,
  voidTransaction,
  refundTransaction,
  testConnection,
  type BraintreeTransaction,
  type BraintreeCustomer,
  type BraintreePaymentMethod,
} from "@/store/actions/braintree";
import {
  type CreateTransactionRequest,
  type CreateCustomerRequest,
  type CreatePaymentMethodRequest,
  type VoidTransactionRequest,
  type RefundTransactionRequest,
} from "@/lib/api/validators/schemas/braintree";
import {
  clearError,
  resetState,
  setClientToken,
  addTransaction,
  updateTransactionStatus,
} from "@/store/slices/braintree";

// Braintree hook interface
export interface UseBraintreeReturn {
  // State
  isReady: boolean;
  clientToken: string | null;
  isConnected: boolean;
  customer: BraintreeCustomer | null;
  paymentMethods: BraintreePaymentMethod[];
  transactions: BraintreeTransaction[];

  // Loading states
  isLoading: boolean;
  isInitializing: boolean;
  isProcessingTransaction: boolean;
  isCreatingCustomer: boolean;
  isCreatingPaymentMethod: boolean;
  isVoidingTransaction: boolean;
  isRefundingTransaction: boolean;
  isTesting: boolean;

  // Error state
  error: string | null;

  // Actions
  initialize: (customerId?: string) => Promise<void>;
  processTransaction: (request: CreateTransactionRequest) => Promise<void>;
  createBraintreeCustomer: (request: CreateCustomerRequest) => Promise<void>;
  addPaymentMethod: (request: CreatePaymentMethodRequest) => Promise<void>;
  voidBraintreeTransaction: (request: VoidTransactionRequest) => Promise<void>;
  refundBraintreeTransaction: (
    request: RefundTransactionRequest
  ) => Promise<void>;
  testBraintreeConnection: () => Promise<void>;

  // Utility actions
  clearBraintreeError: () => void;
  resetBraintreeState: () => void;
  setClientTokenManually: (token: string) => void;
  addTransactionManually: (transaction: BraintreeTransaction) => void;
  updateTransactionStatusManually: (
    id: string,
    status: BraintreeTransaction["status"]
  ) => void;
  refreshData: () => void;
  refreshStatus: () => Promise<{ success: boolean; error?: string }>;

  // Utilities
  formatAmount: (amount: number) => string;
  getTransactionStatusColor: (status: BraintreeTransaction["status"]) => string;
  getTransactionStatusIcon: (
    status: BraintreeTransaction["status"]
  ) => React.ReactNode;
  isTransactionSuccessful: (status: BraintreeTransaction["status"]) => boolean;
  isTransactionPending: (status: BraintreeTransaction["status"]) => boolean;
  isTransactionFailed: (status: BraintreeTransaction["status"]) => boolean;
}

/**
 * Custom hook for Braintree integration using Redux and SWR
 * Provides centralized access to Braintree data, loading states, and actions
 */
export const useBraintree = (): UseBraintreeReturn => {
  const dispatch = useDispatch<AppDispatch>();

  // Redux state selectors
  const braintreeState = useSelector((state: RootState) => state.braintree);

  // SWR hooks for data fetching (optional - for server-side data)
  const {
    error: braintreeError,
    isLoading: isLoadingBraintree,
    mutate: mutateBraintree,
  } = useSWR("braintree/status", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  // Action creators using dispatch
  const initialize = useCallback(
    async (customerId?: string) => {
      let response = await dispatch(initializeBraintree(customerId)).unwrap();
      dispatch(setClientToken(response.clientToken));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const processTransaction = useCallback(
    async (request: CreateTransactionRequest) => {
      await dispatch(createTransaction(request));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const createBraintreeCustomer = useCallback(
    async (request: CreateCustomerRequest) => {
      await dispatch(createCustomer(request));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const addPaymentMethod = useCallback(
    async (request: CreatePaymentMethodRequest) => {
      await dispatch(createPaymentMethod(request));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const voidBraintreeTransaction = useCallback(
    async (request: VoidTransactionRequest) => {
      await dispatch(voidTransaction(request));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const refundBraintreeTransaction = useCallback(
    async (request: RefundTransactionRequest) => {
      await dispatch(refundTransaction(request));
      mutateBraintree();
    },
    [dispatch, mutateBraintree]
  );

  const testBraintreeConnection = useCallback(async () => {
    await dispatch(testConnection());
  }, [dispatch]);

  // Utility actions
  const clearBraintreeError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const resetBraintreeState = useCallback(() => {
    dispatch(resetState());
  }, [dispatch]);

  const setClientTokenManually = useCallback(
    (token: string) => {
      dispatch(setClientToken(token));
    },
    [dispatch]
  );

  const addTransactionManually = useCallback(
    (transaction: BraintreeTransaction) => {
      dispatch(addTransaction(transaction));
    },
    [dispatch]
  );

  const updateTransactionStatusManually = useCallback(
    (id: string, status: BraintreeTransaction["status"]) => {
      dispatch(updateTransactionStatus({ id, status }));
    },
    [dispatch]
  );

  const refreshData = useCallback(() => {
    mutateBraintree();
  }, [mutateBraintree]);

  const refreshStatus = useCallback(async () => {
    try {
      // Force refresh status from server
      await mutateBraintree();
      return { success: true };
    } catch (error) {
      console.error("Failed to refresh Braintree status:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }, [mutateBraintree]);

  // Utility functions
  const formatAmount = useCallback((amount: number): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  }, []);

  const getTransactionStatusColor = useCallback(
    (status: BraintreeTransaction["status"]): string => {
      switch (status) {
        case "settled":
          return "text-green-500";
        case "submitted_for_settlement":
        case "authorized":
          return "text-blue-500";
        case "pending":
          return "text-yellow-500";
        case "voided":
          return "text-gray-500";
        case "failed":
          return "text-red-500";
        default:
          return "text-gray-400";
      }
    },
    []
  );

  const getTransactionStatusIcon = useCallback(
    (status: BraintreeTransaction["status"]): React.ReactNode => {
      switch (status) {
        case "settled":
          return "✅";
        case "submitted_for_settlement":
        case "authorized":
          return "⏳";
        case "pending":
          return "🔄";
        case "voided":
          return "❌";
        case "failed":
          return "⚠️";
        default:
          return "❓";
      }
    },
    []
  );

  const isTransactionSuccessful = useCallback(
    (status: BraintreeTransaction["status"]): boolean => {
      return status === "settled" || status === "submitted_for_settlement";
    },
    []
  );

  const isTransactionPending = useCallback(
    (status: BraintreeTransaction["status"]): boolean => {
      return status === "pending" || status === "authorized";
    },
    []
  );

  const isTransactionFailed = useCallback(
    (status: BraintreeTransaction["status"]): boolean => {
      return status === "failed" || status === "voided";
    },
    []
  );

  // Combine loading states
  const isLoading = braintreeState.isLoading || isLoadingBraintree;
  const error = braintreeState.error || braintreeError?.message || null;

  return {
    // State
    isReady: braintreeState.isReady,
    clientToken: braintreeState.clientToken,
    isConnected: braintreeState.isConnected,
    customer: braintreeState.customer,
    paymentMethods: braintreeState.paymentMethods,
    transactions: braintreeState.transactions,

    // Loading states
    isLoading,
    isInitializing: braintreeState.isInitializing,
    isProcessingTransaction: braintreeState.isProcessingTransaction,
    isCreatingCustomer: braintreeState.isCreatingCustomer,
    isCreatingPaymentMethod: braintreeState.isCreatingPaymentMethod,
    isVoidingTransaction: braintreeState.isVoidingTransaction,
    isRefundingTransaction: braintreeState.isRefundingTransaction,
    isTesting: braintreeState.isTesting,

    // Error state
    error,

    // Actions
    initialize,
    processTransaction,
    createBraintreeCustomer,
    addPaymentMethod,
    voidBraintreeTransaction,
    refundBraintreeTransaction,
    testBraintreeConnection,

    // Utility actions
    clearBraintreeError,
    resetBraintreeState,
    setClientTokenManually,
    addTransactionManually,
    updateTransactionStatusManually,
    refreshData,
    refreshStatus,

    // Utilities
    formatAmount,
    getTransactionStatusColor,
    getTransactionStatusIcon,
    isTransactionSuccessful,
    isTransactionPending,
    isTransactionFailed,
  };
};

// Additional utility hooks for specific Braintree sections
export const useBraintreeTransactions = () => {
  const { transactions, isLoading, error, refreshData } = useBraintree();

  return {
    transactions,
    isLoading,
    error,
    refresh: refreshData,
  };
};

export const useBraintreeStatus = () => {
  const {
    isReady,
    isConnected,
    isLoading,
    error,
    initialize,
    testBraintreeConnection,
  } = useBraintree();

  return {
    isReady,
    isConnected,
    isLoading,
    error,
    initialize,
    testConnection: testBraintreeConnection,
  };
};

export const useBraintreePaymentMethods = () => {
  const { paymentMethods, isLoading, error, addPaymentMethod, refreshData } =
    useBraintree();

  return {
    paymentMethods,
    isLoading,
    error,
    addPaymentMethod,
    refresh: refreshData,
  };
};
