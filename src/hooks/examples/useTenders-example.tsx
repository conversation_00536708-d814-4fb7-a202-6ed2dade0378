/**
 * Example usage of useTenders hook
 *
 * This file demonstrates how to use the new tender actions and SWR-based hooks
 * for managing tender data in your React components.
 */

"use client";

import React, { useState } from "react";
import { useTenders, useTender, useTenderByOcid } from "@/hooks/useTenders";
import type {
  TenderCreateData,
  TenderUpdateData,
} from "@/store/actions/tender";

// Example component showing how to use the useTenders hook
export function TendersListExample() {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  // Use the hook with query parameters
  const {
    tenders,
    pagination,
    searchResults,
    isLoading,
    isSearching,
    error,
    refreshTenders,
    createTender,
    updateTender,
    deleteTender,
    upsertTender,
    searchTenders,
    seedTenders,
    bulkUpsertTenders,
    clearSearchResults,
  } = useTenders({
    page: currentPage,
    limit: 10,
    search: searchQuery,
  });

  // Handle creating a new tender
  const handleCreateTender = async () => {
    const newTender: TenderCreateData = {
      ocid: "OCID-123456",
      description: "New tender for construction services",
      status: "active",
      category: "construction",
      procuring_entity: "City Council",
      procuring_method: "open",
    };

    try {
      const result = await createTender(newTender);
      console.log("Tender created:", result);
    } catch (error) {
      console.error("Failed to create tender:", error);
    }
  };

  // Handle updating an existing tender
  const handleUpdateTender = async (tenderId: string) => {
    const updateData: TenderUpdateData = {
      id: tenderId,
      description: "Updated tender description",
      status: "completed",
    };

    try {
      const result = await updateTender(updateData);
      console.log("Tender updated:", result);
    } catch (error) {
      console.error("Failed to update tender:", error);
    }
  };

  // Handle deleting a tender
  const handleDeleteTender = async (tenderId: string) => {
    try {
      await deleteTender(tenderId);
      console.log("Tender deleted successfully");
    } catch (error) {
      console.error("Failed to delete tender:", error);
    }
  };

  // Handle searching tenders
  const handleSearchTenders = async () => {
    try {
      const results = await searchTenders({
        search: searchQuery,
        status: "active",
        category: "construction",
      });
      console.log("Search results:", results);
    } catch (error) {
      console.error("Failed to search tenders:", error);
    }
  };

  // Handle seeding tenders from external API
  const handleSeedTenders = async () => {
    try {
      const result = await seedTenders();
      console.log("Seeding result:", result);
    } catch (error) {
      console.error("Failed to seed tenders:", error);
    }
  };

  // Handle bulk upsert
  const handleBulkUpsert = async () => {
    const bulkTenders: TenderCreateData[] = [
      {
        ocid: "BULK-001",
        description: "Bulk tender 1",
        status: "active",
      },
      {
        ocid: "BULK-002",
        description: "Bulk tender 2",
        status: "pending",
      },
    ];

    try {
      const result = await bulkUpsertTenders(bulkTenders);
      console.log("Bulk operation result:", result);
    } catch (error) {
      console.error("Failed to perform bulk operation:", error);
    }
  };

  if (isLoading) return <div>Loading tenders...</div>;
  if (error) return <div>Error loading tenders: {error.message}</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Tenders Management</h1>

      {/* Search Section */}
      <div className="mb-4 flex gap-2">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search tenders..."
          className="border p-2 rounded"
        />
        <button
          onClick={handleSearchTenders}
          disabled={isSearching}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {isSearching ? "Searching..." : "Search"}
        </button>
        <button
          onClick={clearSearchResults}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Clear Search
        </button>
      </div>

      {/* Action Buttons */}
      <div className="mb-4 flex gap-2">
        <button
          onClick={handleCreateTender}
          className="bg-green-500 text-white px-4 py-2 rounded"
        >
          Create Tender
        </button>
        <button
          onClick={handleSeedTenders}
          className="bg-purple-500 text-white px-4 py-2 rounded"
        >
          Seed Tenders
        </button>
        <button
          onClick={handleBulkUpsert}
          className="bg-orange-500 text-white px-4 py-2 rounded"
        >
          Bulk Upsert
        </button>
        <button
          onClick={refreshTenders}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Refresh
        </button>
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">Search Results</h2>
          <div className="grid gap-2">
            {searchResults.map((tender) => (
              <div key={tender.id} className="border p-3 rounded">
                <h3 className="font-medium">{tender.ocid}</h3>
                <p className="text-sm text-gray-600">{tender.description}</p>
                <span className="text-xs bg-blue-100 px-2 py-1 rounded">
                  {tender.status}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tenders List */}
      <div className="grid gap-4">
        {tenders.map((tender) => (
          <div key={tender.id} className="border p-4 rounded-lg">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{tender.ocid}</h3>
                <p className="text-gray-600">{tender.description}</p>
                <div className="flex gap-2 mt-2">
                  <span className="text-xs bg-blue-100 px-2 py-1 rounded">
                    {tender.status}
                  </span>
                  {tender.category && (
                    <span className="text-xs bg-green-100 px-2 py-1 rounded">
                      {tender.category}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleUpdateTender(tender.id)}
                  className="bg-yellow-500 text-white px-3 py-1 rounded text-sm"
                >
                  Update
                </button>
                <button
                  onClick={() => handleDeleteTender(tender.id)}
                  className="bg-red-500 text-white px-3 py-1 rounded text-sm"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="mt-4 flex justify-between items-center">
          <span className="text-sm text-gray-600">
            Showing {tenders.length} of {pagination.total} tenders
          </span>
          <div className="flex gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="bg-gray-300 px-3 py-1 rounded disabled:opacity-50"
            >
              Previous
            </button>
            <span className="px-3 py-1">
              Page {currentPage} of {pagination.totalPages}
            </span>
            <button
              onClick={() =>
                setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))
              }
              disabled={currentPage === pagination.totalPages}
              className="bg-gray-300 px-3 py-1 rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// Example component showing how to use individual tender hooks
export function SingleTenderExample({ tenderId }: { tenderId: string }) {
  const { tender, error, isLoading, refresh } = useTender(tenderId);

  if (isLoading) return <div>Loading tender...</div>;
  if (error) return <div>Error loading tender: {error.message}</div>;
  if (!tender) return <div>Tender not found</div>;

  return (
    <div className="p-4 border rounded">
      <h2 className="text-xl font-bold">{tender.ocid}</h2>
      <p className="text-gray-600">{tender.description}</p>
      <div className="mt-2">
        <span className="text-sm bg-blue-100 px-2 py-1 rounded">
          {tender.status}
        </span>
      </div>
      <button
        onClick={() => refresh()}
        className="mt-2 bg-blue-500 text-white px-3 py-1 rounded"
      >
        Refresh
      </button>
    </div>
  );
}

// Example component showing how to use OCID-based lookup
export function TenderByOcidExample({ ocid }: { ocid: string }) {
  const { tender, error, isLoading, refresh } = useTenderByOcid(ocid);

  if (isLoading) return <div>Loading tender by OCID...</div>;
  if (error) return <div>Error loading tender: {error.message}</div>;
  if (!tender) return <div>Tender with OCID {ocid} not found</div>;

  return (
    <div className="p-4 border rounded">
      <h2 className="text-xl font-bold">Found by OCID: {tender.ocid}</h2>
      <p className="text-gray-600">{tender.description}</p>
      <button
        onClick={() => refresh()}
        className="mt-2 bg-blue-500 text-white px-3 py-1 rounded"
      >
        Refresh
      </button>
    </div>
  );
}
