"use client";

import { useCallback, useMemo } from "react";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/common/requests";

// Types for dashboard data
export interface DashboardSummary {
  contractsCount: number;
  proposalsCount: number;
  recentContracts: any[];
  recentProposals: any[];
  contractStatistics: any;
  proposalStatistics: any;
}

// Dashboard hook interface
export interface UseDashboardReturn {
  // Data
  summary: DashboardSummary | null;
  recentContracts: any[];
  recentProposals: any[];
  contractStatistics: any;
  proposalStatistics: any;

  // Loading states
  isLoading: boolean;
  isLoadingContracts: boolean;
  isLoadingProposals: boolean;
  isLoadingStatistics: boolean;

  // Error states
  error: string | null;

  // Actions
  refreshDashboard: () => void;
  refreshContracts: () => void;
  refreshProposals: () => void;
  refreshStatistics: () => void;
}

/**
 * Custom hook for dashboard data management using SWR
 * Provides centralized access to dashboard data, loading states, and actions
 */
export const useDashboard = (): UseDashboardReturn => {
  // SWR hooks for data fetching
  const {
    data: contractsData,
    error: contractsError,
    isLoading: isLoadingContracts,
    mutate: mutateContracts,
  } = useSWR("contract", fetcher);

  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
    mutate: mutateProposals,
  } = useSWR("proposal", fetcher);

  const {
    data: contractStatsData,
    error: contractStatsError,
    isLoading: isLoadingContractStats,
    mutate: mutateContractStats,
  } = useSWR("contract/statistics", fetcher);

  const {
    data: proposalStatsData,
    error: proposalStatsError,
    isLoading: isLoadingProposalStats,
    mutate: mutateProposalStats,
  } = useSWR("proposal/statistics", fetcher);

  // Extract data from SWR responses
  const contracts = contractsData?.data || [];
  const proposals = proposalsData?.data?.proposals || [];
  const contractStatistics = contractStatsData?.data || null;
  const proposalStatistics = proposalStatsData?.data || null;

  // Process recent items (last 5, sorted by creation date)
  const recentContracts = useMemo(() => {
    return contracts
      .sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 5);
  }, [contracts]);

  const recentProposals = useMemo(() => {
    return proposals
      .sort(
        (a: any, b: any) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 5);
  }, [proposals]);

  // Create summary object
  const summary: DashboardSummary = useMemo(
    () => ({
      contractsCount: contracts.length,
      proposalsCount: proposals.length,
      recentContracts,
      recentProposals,
      contractStatistics,
      proposalStatistics,
    }),
    [
      contracts.length,
      proposals.length,
      recentContracts,
      recentProposals,
      contractStatistics,
      proposalStatistics,
    ]
  );

  // Loading states
  const isLoadingStatistics = isLoadingContractStats || isLoadingProposalStats;
  const isLoading =
    isLoadingContracts || isLoadingProposals || isLoadingStatistics;

  // Error handling
  const error =
    contractsError ||
    proposalsError ||
    contractStatsError ||
    proposalStatsError;

  // Action creators using SWR mutate
  const refreshContracts = useCallback(() => {
    mutateContracts();
  }, [mutateContracts]);

  const refreshProposals = useCallback(() => {
    mutateProposals();
  }, [mutateProposals]);

  const refreshStatistics = useCallback(() => {
    mutateContractStats();
    mutateProposalStats();
  }, [mutateContractStats, mutateProposalStats]);

  const refreshDashboard = useCallback(() => {
    mutateContracts();
    mutateProposals();
    mutateContractStats();
    mutateProposalStats();
  }, [
    mutateContracts,
    mutateProposals,
    mutateContractStats,
    mutateProposalStats,
  ]);

  return {
    // Data
    summary,
    recentContracts,
    recentProposals,
    contractStatistics,
    proposalStatistics,

    // Loading states
    isLoading,
    isLoadingContracts,
    isLoadingProposals,
    isLoadingStatistics,

    // Error states
    error: error?.message || null,

    // Actions
    refreshDashboard,
    refreshContracts,
    refreshProposals,
    refreshStatistics,
  };
};

// Additional utility hooks for specific dashboard sections
export const useDashboardSummary = () => {
  const { summary, isLoading, error, refreshDashboard } = useDashboard();

  return {
    summary,
    isLoading,
    error,
    refresh: refreshDashboard,
  };
};

export const useRecentContracts = () => {
  const { recentContracts, isLoadingContracts, error, refreshContracts } =
    useDashboard();

  return {
    contracts: recentContracts,
    isLoading: isLoadingContracts,
    error,
    refresh: refreshContracts,
  };
};

export const useRecentProposals = () => {
  const { recentProposals, isLoadingProposals, error, refreshProposals } =
    useDashboard();

  return {
    proposals: recentProposals,
    isLoading: isLoadingProposals,
    error,
    refresh: refreshProposals,
  };
};

export const useDashboardStatistics = () => {
  const {
    contractStatistics,
    proposalStatistics,
    isLoadingStatistics,
    error,
    refreshStatistics,
  } = useDashboard();

  return {
    contractStatistics,
    proposalStatistics,
    isLoading: isLoadingStatistics,
    error,
    refresh: refreshStatistics,
  };
};
