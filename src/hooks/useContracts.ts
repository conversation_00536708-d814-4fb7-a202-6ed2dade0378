"use client";

import { useCallback, useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { toast } from "sonner";
import type { RootState, AppDispatch } from "@/store";
import { fetcher, api } from "@/lib/common/requests";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";
import {
  createContract,
  updateContract,
  deleteContract,
  searchContracts,
} from "@/store/actions/contracts";
import {
  clearError,
  setCurrentContract,
  clearCurrentContract,
} from "@/store/slices/contract";
import type {
  CreateContract,
  UpdateContract,
} from "@/lib/api/validators/schemas/contract";
import type { Contract } from "@/data/contracts-mock";
import { usePagination } from "./usePagination";
import { createSearchFunction, type SearchConfig } from "@/lib/search";

const adaptApiContractToUI = (apiContract: ApiContract): Contract => {
  const statusMap: Record<string, Contract["status"]> = {
    draft: "draft",
    active: "active",
    completed: "completed",
    terminated: "terminated",
    expired: "expired",
  };

  return {
    id: apiContract.id,
    title: `Contract ${apiContract?.proposal?.name}`,
    description: "",
    status: statusMap[apiContract.status] || "draft",
    clientName: apiContract.client?.name || "",
    contractValue: apiContract.total_value || 0,
    startDate: new Date(apiContract.start_date)?.toISOString(),
    endDate: new Date(apiContract.end_date)?.toISOString(),
    createdDate: new Date(apiContract.createdAt)?.toISOString(),
    lastModified: new Date(apiContract.updatedAt)?.toISOString(),
    proposalId: apiContract.proposal_id,
  };
};

// Contrary function: adapt UI Contract to API Contract object shape
const adaptUIContractToAPI = (uiContract: Contract): Partial<ApiContract> => {
  const statusMap: Record<Contract["status"], ApiContract["status"]> = {
    draft: "draft",
    active: "active",
    completed: "completed",
    terminated: "terminated",
    expired: "expired",
  };

  return {
    id: uiContract.id,
    status: statusMap[uiContract.status] || "draft",
    total_value: uiContract.contractValue || 0,
    start_date: uiContract.startDate
      ? new Date(uiContract.startDate).toISOString()
      : new Date().toISOString(),
    end_date: uiContract.endDate
      ? new Date(uiContract.endDate).toISOString()
      : new Date().toISOString(),
    proposal_id: uiContract.proposalId,
    // Note: Some fields like client, proposal details are read-only from API perspective
    // and should not be included in update operations
  };
};

export function useContracts() {
  const router = useRouter();
  const { slug } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const [searchTerm, setSearchTerm] = useState("");
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [filteredContracts, setFilteredContracts] = useState<ApiContract[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Initialize pagination hook for contracts table
  const paginationHook = usePagination("contracts-table");

  // Use SWR for data fetching instead of Redux selectors
  const {
    data: contractsData,
    error: contractsError,
    mutate: mutateContracts,
    isLoading: isLoadingContracts,
  } = useSWR("contract", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    mutate: mutateStatistics,
    isLoading: isLoadingStatistics,
  } = useSWR("contract/statistics", fetcher);

  // Only use Redux for current contract and operation states
  const {
    currentContract,
    isLoading: operationLoading,
    error: reduxError,
  } = useSelector((state: RootState) => state.contracts);

  // Extract data from SWR responses (API response format)
  const serverPagination = contractsData?.pagination;
  const statistics = statisticsData?.data || {
    total: 0,
    active: 0,
    completed: 0,
    draft: 0,
    terminated: 0,
    expired: 0,
    totalValue: 0,
    averageValue: 0,
  };

  function setUiContracts(contracts: ApiContract[]) {
    if (!contracts || contracts.length === 0) return;
    const uiContracts = contracts.map(adaptApiContractToUI);
    setContracts(uiContracts);
  }

  useEffect(() => {
    // Reset Proposals Data on search term clear
    if (!searchTerm) {
      setUiContracts(contractsData?.data);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (filteredContracts) {
      setUiContracts(filteredContracts);
    }
  }, [filteredContracts]);

  useEffect(() => {
    if (contractsData) {
      setUiContracts(contractsData.data);
    }
  }, [contractsData]);

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (contractsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 10,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [contractsData, serverPagination]);

  // Combine errors from SWR and Redux
  const error = reduxError || contractsError || statisticsError;
  const isLoading =
    isLoadingContracts || isLoadingStatistics || operationLoading;

  // Refresh data functions using SWR mutate
  const refreshContracts = useCallback(() => {
    mutateContracts();
  }, [mutateContracts]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutateContracts();
    mutateStatistics();
  }, [mutateContracts, mutateStatistics]);

  // Search configuration for the composable search function
  const searchConfig: SearchConfig<Contract, ApiContract> = {
    adaptApiToUI: adaptApiContractToUI,
    searchFields: (contract: ApiContract) => [
      contract.proposal?.name || "",
      contract.client?.name || "",
      contract.client?.email || "",
      contract.status || "",
      contract.total_value?.toString() || "",
    ],
    setFilteredData: setFilteredContracts,
    setSearchTerm,
    setIsSearching,
    searchAction: searchContracts,
    currentData: contracts,
    loadedApiData: contractsData?.data,
    debounceDelay: 1400,
  };

  // Create the composable search function
  const { searchFunction, cleanup } = createSearchFunction(
    dispatch,
    searchConfig
  );

  // Search Contracts (using composable function)
  const searchContractsFunction = useCallback(
    async (searchParams: { query?: string; page?: number; limit?: number }) => {
      return await searchFunction(searchParams);
    },
    [searchFunction]
  );

  // Create new contract
  const createNewContract = useCallback(
    async (contractData: CreateContract) => {
      try {
        const result = await dispatch(createContract(contractData));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          toast.success("Contract created successfully");
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to create contract";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Update existing contract
  const updateExistingContract = useCallback(
    async (contractId: string, contractData: Partial<UpdateContract>) => {
      try {
        const result = await dispatch(
          updateContract({
            id: contractId,
            data: contractData,
          })
        );

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          toast.success("Contract updated successfully");
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to update contract";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Delete contract
  const deleteExistingContract = useCallback(
    async (contractId: string) => {
      try {
        const result = await dispatch(deleteContract(contractId));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          toast.success("Contract deleted successfully");
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          router.push(`/${slug}/contracts`);
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to delete contract";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Fetch single contract by ID
  const fetchSingleContract = useCallback(async (contractId: string) => {
    try {
      const response = await api.get(`contract/${contractId}`);
      if (response.success) {
        dispatch(setCurrentContract(response.data));
        return { success: true, data: response.data };
      } else {
        toast.error(response.message || "Failed to fetch contract");
        return { success: false, error: response.message };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch contract";
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  // Clear error
  const clearContractError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set current contract (expects UI contract, converts to API for Redux)
  const setContract = useCallback(
    (contract: Contract | null) => {
      // Convert UI contract to API contract for Redux storage
      const apiContract = contract
        ? (adaptUIContractToAPI(contract) as ApiContract)
        : null;
      dispatch(setCurrentContract(apiContract));
    },
    [dispatch]
  );

  // Clear current contract
  const clearContract = useCallback(() => {
    dispatch(clearCurrentContract());
  }, [dispatch]);

  // Initialize contracts data using SWR
  const initializeContracts = useCallback(() => {
    refreshData();
  }, [refreshData]);

  // Trigger search - the hook handles debouncing and API calls
  useEffect(() => {
    searchContractsFunction({ query: searchTerm }).catch((error) => {
      toast.error("Search failed", error);
    });
  }, [searchTerm]);

  // Cleanup search function on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // State (from SWR)
    contracts,
    currentContract,
    statistics,
    searchTerm,
    isLoading,
    isSearching,
    error,

    // Pagination
    pagination: paginationHook,

    // Data refresh actions (SWR-based)
    refreshContracts,
    refreshStatistics,
    refreshData,

    // CRUD actions (Redux-based with SWR refresh)
    fetchContract: fetchSingleContract,
    createContract: createNewContract,
    updateContract: updateExistingContract,
    deleteContract: deleteExistingContract,
    searchContracts: searchContractsFunction,
    initializeContracts,

    // Utility actions
    clearError: clearContractError,
    setCurrentContract: setContract,
    clearCurrentContract: clearContract,
    setSearchTerm,

    // Data transformation utilities
    adaptUIContractToAPI,
  };
}

// Hook for getting a specific contract using SWR
export function useContract(contractId: string) {
  const {
    data: contractData,
    error,
    mutate,
    isLoading,
  } = useSWR(contractId ? `contract/${contractId}` : null, fetcher);

  return {
    contract: contractData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}

// Hook for contract statistics using SWR
export function useContractStatistics() {
  const {
    data: statisticsData,
    error,
    mutate,
    isLoading,
  } = useSWR("contract/statistics", fetcher);

  return {
    statistics: statisticsData?.data || {
      total: 0,
      active: 0,
      completed: 0,
      draft: 0,
      terminated: 0,
      expired: 0,
      totalValue: 0,
      averageValue: 0,
    },
    error,
    isLoading,
    refresh: mutate,
  };
}
