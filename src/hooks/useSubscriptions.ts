"use client";

import use<PERSON><PERSON> from "swr";
import { useRouter } from "next/navigation";
import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  searchSubscriptions,
  getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  performBulkAction,
  getSubscriptionStatistics,
  type CreateSubscription,
  type UpdateSubscription,
  type BulkSubscription,
  type SubscriptionSearchParams,
} from "@/store/actions/subscription";
import {
  selectSubscriptions,
  selectCurrentSubscription,
  selectSubscriptionStatistics,
  selectIsLoading,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectIsBulkActionLoading,
  selectIsStatisticsLoading,
  selectError,
  selectSearchQuery,
  selectPagination,
  selectFilters,
  selectSelectedSubscriptionIds,
  clearError,
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setError,
  setCurrentSubscription,
  setSearchQuery,
  setFilters,
  clearFilters,
  resetState,
  setSelectedSubscriptionIds,
  toggleSubscriptionSelection,
  clearSelection,
} from "@/store/slices/subscription";
import type { Subscription } from "@/lib/api/validators/schemas/subscription";
import { usePagination } from "./usePagination";

/**
 * useSubscriptions Hook
 *
 * Provides comprehensive subscription management functionality including:
 * - CRUD operations with Redux state management
 * - SWR integration for data fetching and caching
 * - Search and filtering capabilities
 * - Bulk operations
 * - Statistics and analytics
 * - Error handling and loading states
 */
export function useSubscriptions() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  // Local state
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Redux selectors
  const reduxSubscriptions = useSelector(selectSubscriptions);
  const currentSubscription = useSelector(selectCurrentSubscription);
  const statistics = useSelector(selectSubscriptionStatistics);
  const isLoading = useSelector(selectIsLoading);
  const isCreating = useSelector(selectIsCreating);
  const isUpdating = useSelector(selectIsUpdating);
  const isDeleting = useSelector(selectIsDeleting);
  const isBulkActionLoading = useSelector(selectIsBulkActionLoading);
  const isStatisticsLoading = useSelector(selectIsStatisticsLoading);
  const error = useSelector(selectError);
  const searchQuery = useSelector(selectSearchQuery);
  const pagination = useSelector(selectPagination);
  const filters = useSelector(selectFilters);
  const selectedSubscriptionIds = useSelector(selectSelectedSubscriptionIds);

  // SWR for data fetching
  const {
    data: swrSubscriptions,
    error: swrError,
    mutate: mutateSubscriptions,
    isLoading: swrLoading,
  } = useSWR<any>("subscription", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  const {
    data: swrStatistics,
    error: swrStatsError,
    mutate: mutateStatistics,
    isLoading: swrStatsLoading,
  } = useSWR<any>("subscription/statistics", fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 30000, // Cache stats for 30 seconds
  });

  // Pagination hook
  const paginationHook = usePagination({
    initialPage: 1,
    initialLimit: 10,
  });

  // Update local subscriptions when Redux or SWR data changes
  useEffect(() => {
    const dataSource = reduxSubscriptions.length > 0 ? reduxSubscriptions : swrSubscriptions?.data || [];
    setSubscriptions(dataSource);
  }, [reduxSubscriptions, swrSubscriptions]);

  // Filter subscriptions based on search term and filters
  useEffect(() => {
    let filtered = subscriptions;

    // Apply search term
    if (searchTerm) {
      filtered = filtered.filter(
        (subscription) =>
          subscription.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          subscription.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply filters
    if (filters.status) {
      filtered = filtered.filter((subscription) => subscription.status === filters.status);
    }

    if (filters.minPrice !== undefined) {
      filtered = filtered.filter((subscription) => subscription.price >= filters.minPrice!);
    }

    if (filters.maxPrice !== undefined) {
      filtered = filtered.filter((subscription) => subscription.price <= filters.maxPrice!);
    }

    if (filters.minSubscribers !== undefined) {
      filtered = filtered.filter((subscription) => subscription.subscribers >= filters.minSubscribers!);
    }

    if (filters.maxSubscribers !== undefined) {
      filtered = filtered.filter((subscription) => subscription.subscribers <= filters.maxSubscribers!);
    }

    setFilteredSubscriptions(filtered);
  }, [subscriptions, searchTerm, filters]);

  // CRUD operations
  const handleCreateSubscription = useCallback(
    async (subscriptionData: CreateSubscription) => {
      try {
        dispatch(setCreating(true));
        const result = await dispatch(createSubscription(subscriptionData));
        
        if (createSubscription.fulfilled.match(result)) {
          await mutateSubscriptions(); // Refresh SWR data
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to create subscription");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to create subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleUpdateSubscription = useCallback(
    async (id: string, subscriptionData: Partial<UpdateSubscription>) => {
      try {
        dispatch(setUpdating(true));
        const result = await dispatch(updateSubscription({ id, data: subscriptionData }));
        
        if (updateSubscription.fulfilled.match(result)) {
          await mutateSubscriptions(); // Refresh SWR data
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to update subscription");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to update subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleDeleteSubscription = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        const result = await dispatch(deleteSubscription(id));
        
        if (deleteSubscription.fulfilled.match(result)) {
          await mutateSubscriptions(); // Refresh SWR data
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to delete subscription");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to delete subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  const handleGetSubscription = useCallback(
    async (id: string) => {
      try {
        dispatch(setLoading(true));
        const result = await dispatch(getSubscription(id));
        
        if (getSubscription.fulfilled.match(result)) {
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to get subscription");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to get subscription";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  // Search and filtering
  const handleSearchSubscriptions = useCallback(
    async (params: SubscriptionSearchParams) => {
      try {
        dispatch(setLoading(true));
        const result = await dispatch(searchSubscriptions(params));
        
        if (searchSubscriptions.fulfilled.match(result)) {
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to search subscriptions");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to search subscriptions";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setLoading(false));
      }
    },
    [dispatch]
  );

  const updateSearchTerm = useCallback(
    (term: string) => {
      setSearchTerm(term);
      dispatch(setSearchQuery(term));
    },
    [dispatch]
  );

  const updateFilters = useCallback(
    (newFilters: Partial<typeof filters>) => {
      dispatch(setFilters(newFilters));
    },
    [dispatch]
  );

  const clearAllFilters = useCallback(() => {
    setSearchTerm("");
    dispatch(clearFilters());
  }, [dispatch]);

  // Bulk operations
  const handleBulkAction = useCallback(
    async (bulkData: BulkSubscription) => {
      try {
        dispatch(setBulkActionLoading(true));
        const result = await dispatch(performBulkAction(bulkData));
        
        if (performBulkAction.fulfilled.match(result)) {
          await mutateSubscriptions(); // Refresh SWR data
          dispatch(clearSelection()); // Clear selection after successful bulk action
          return result.payload;
        } else {
          throw new Error(result.payload?.message || "Failed to perform bulk action");
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to perform bulk action";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setBulkActionLoading(false));
      }
    },
    [dispatch, mutateSubscriptions]
  );

  // Statistics
  const handleGetStatistics = useCallback(async () => {
    try {
      const result = await dispatch(getSubscriptionStatistics());
      
      if (getSubscriptionStatistics.fulfilled.match(result)) {
        return result.payload;
      } else {
        throw new Error(result.payload?.message || "Failed to get statistics");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to get statistics";
      dispatch(setError(errorMessage));
      throw error;
    }
  }, [dispatch]);

  // Utility functions
  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const resetSubscriptionState = useCallback(() => {
    dispatch(resetState());
  }, [dispatch]);

  // Selection management
  const handleToggleSelection = useCallback(
    (id: string) => {
      dispatch(toggleSubscriptionSelection(id));
    },
    [dispatch]
  );

  const handleSetSelection = useCallback(
    (ids: string[]) => {
      dispatch(setSelectedSubscriptionIds(ids));
    },
    [dispatch]
  );

  const handleClearSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  return {
    // Data
    subscriptions: filteredSubscriptions,
    allSubscriptions: subscriptions,
    currentSubscription,
    statistics: statistics || swrStatistics?.data,

    // Loading states
    isLoading: isLoading || swrLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkActionLoading,
    isStatisticsLoading: isStatisticsLoading || swrStatsLoading,

    // Error states
    error: error || swrError?.message || swrStatsError?.message,

    // Search and filtering
    searchTerm,
    searchQuery,
    filters,
    pagination,

    // Selection
    selectedSubscriptionIds,

    // Pagination
    ...paginationHook,

    // CRUD operations
    createSubscription: handleCreateSubscription,
    updateSubscription: handleUpdateSubscription,
    deleteSubscription: handleDeleteSubscription,
    getSubscription: handleGetSubscription,

    // Search and filtering
    searchSubscriptions: handleSearchSubscriptions,
    setSearchTerm: updateSearchTerm,
    setFilters: updateFilters,
    clearFilters: clearAllFilters,

    // Bulk operations
    performBulkAction: handleBulkAction,

    // Statistics
    getStatistics: handleGetStatistics,

    // Selection management
    toggleSelection: handleToggleSelection,
    setSelection: handleSetSelection,
    clearSelection: handleClearSelection,

    // Utility functions
    clearError: clearErrorState,
    resetState: resetSubscriptionState,
    refreshData: mutateSubscriptions,
    refreshStatistics: mutateStatistics,

    // Redux actions for direct use
    setCurrentSubscription: (subscription: Subscription | null) =>
      dispatch(setCurrentSubscription(subscription)),
  };
}
