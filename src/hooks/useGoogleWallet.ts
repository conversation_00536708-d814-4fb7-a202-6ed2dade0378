import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import useSWR from "swr";
import { fetcher } from "@/lib/common/requests";
import {
  initializeWallet,
  processPayment,
  processDeposit,
  createEscrow,
  releaseEscrow,
} from "@/store/actions/googleWallet";

// Types for Google Wallet
export interface Transaction {
  id: string | number;
  type: "payment" | "deposit" | "escrow";
  amount: number;
  recipient?: string;
  source?: string;
  condition?: string;
  status: "pending" | "completed" | "failed";
  timestamp: string;
  description: string;
  releaseCondition?: string;
}

export interface PaymentRequest {
  amount: number;
  recipient: string;
  description?: string;
}

export interface DepositRequest {
  amount: number;
  source: string;
}

export interface EscrowRequest {
  amount: number;
  recipient: string;
  condition: string;
}

export interface GoogleWalletState {
  isWalletReady: boolean;
  balance: number;
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;
}

// Google Wallet hook interface
export interface UseGoogleWalletReturn {
  // State
  isWalletReady: boolean;
  balance: number;
  transactions: Transaction[];
  isLoading: boolean;
  error: string | null;

  // Actions
  initWallet: () => Promise<void>;
  makePayment: (request: PaymentRequest) => Promise<void>;
  addFunds: (request: DepositRequest) => Promise<void>;
  createEscrowPayment: (request: EscrowRequest) => Promise<void>;
  releaseEscrowFunds: (transactionId: string | number) => Promise<void>;
  refreshData: () => void;

  // Utilities
  getStatusIcon: (status: string) => React.ReactNode;
  getAmountColor: (type: string, amount: number) => string;
  formatAmount: (amount: number) => string;
  createPaymentRequestConfig: (amount: number, description: string) => any;
}

/**
 * Custom hook for Google Wallet integration using SWR
 * Provides centralized access to wallet data, loading states, and actions
 */
export const useGoogleWallet = (): UseGoogleWalletReturn => {
  const dispatch = useDispatch<AppDispatch>();

  // SWR hooks for data fetching
  const {
    data: walletData,
    error: walletError,
    isLoading: isLoadingWallet,
    mutate: mutateWallet,
  } = useSWR("wallet/status", fetcher);

  const {
    data: balanceData,
    error: balanceError,
    isLoading: isLoadingBalance,
    mutate: mutateBalance,
  } = useSWR("wallet/balance", fetcher);

  const {
    data: transactionsData,
    error: transactionsError,
    isLoading: isLoadingTransactions,
    mutate: mutateTransactions,
  } = useSWR("wallet/transactions", fetcher);

  // Extract data from SWR responses
  const isWalletReady = walletData?.isReady || false;
  const balance = balanceData?.balance || 0;
  const transactions = transactionsData?.transactions || [];

  // Loading and error states
  const isLoading =
    isLoadingWallet || isLoadingBalance || isLoadingTransactions;
  const error = walletError || balanceError || transactionsError;

  // Action creators using dispatch
  const initWallet = useCallback(async () => {
    await dispatch(initializeWallet());
    mutateWallet();
  }, [dispatch, mutateWallet]);

  const makePayment = useCallback(
    async (request: PaymentRequest) => {
      await dispatch(processPayment(request));
      mutateBalance();
      mutateTransactions();
    },
    [dispatch, mutateBalance, mutateTransactions]
  );

  const addFunds = useCallback(
    async (request: DepositRequest) => {
      await dispatch(processDeposit(request));
      mutateBalance();
      mutateTransactions();
    },
    [dispatch, mutateBalance, mutateTransactions]
  );

  const createEscrowPayment = useCallback(
    async (request: EscrowRequest) => {
      await dispatch(createEscrow(request));
      mutateBalance();
      mutateTransactions();
    },
    [dispatch, mutateBalance, mutateTransactions]
  );

  const releaseEscrowFunds = useCallback(
    async (transactionId: string | number) => {
      await dispatch(releaseEscrow(transactionId));
      mutateTransactions();
    },
    [dispatch, mutateTransactions]
  );

  const refreshData = useCallback(() => {
    mutateWallet();
    mutateBalance();
    mutateTransactions();
  }, [mutateWallet, mutateBalance, mutateTransactions]);

  // Utility functions
  const getStatusIcon = useCallback((status: string) => {
    // This will be implemented in the component with proper icons
    return status;
  }, []);

  const getAmountColor = useCallback((type: string, _amount: number) => {
    if (type === "deposit") return "text-green-600";
    if (type === "payment" || type === "escrow") return "text-red-600";
    return "text-gray-600";
  }, []);

  const formatAmount = useCallback((amount: number) => {
    return `$${Math.abs(amount).toFixed(2)}`;
  }, []);

  // Google Pay payment request configuration
  const createPaymentRequestConfig = useCallback(
    (amount: number, description: string) => ({
      apiVersion: 2,
      apiVersionMinor: 0,
      allowedPaymentMethods: [
        {
          type: "CARD",
          parameters: {
            allowedAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
            allowedCardNetworks: ["MASTERCARD", "VISA"],
          },
          tokenizationSpecification: {
            type: "PAYMENT_GATEWAY",
            parameters: {
              gateway: "example",
              gatewayMerchantId: "exampleGatewayMerchantId",
            },
          },
        },
      ],
      merchantInfo: {
        merchantId: "your-merchant-id",
        merchantName: "Your App Name",
      },
      transactionInfo: {
        totalPriceStatus: "FINAL",
        totalPriceLabel: "Total",
        totalPrice: amount.toString(),
        currencyCode: "USD",
        displayItems: [
          {
            label: description,
            type: "LINE_ITEM",
            price: amount.toString(),
          },
        ],
      },
    }),
    []
  );

  return {
    // State
    isWalletReady,
    balance,
    transactions,
    isLoading,
    error: error?.message || null,

    // Actions
    initWallet,
    makePayment,
    addFunds,
    createEscrowPayment,
    releaseEscrowFunds,
    refreshData,

    // Utilities
    getStatusIcon,
    getAmountColor,
    formatAmount,
    createPaymentRequestConfig,
  };
};

// Additional utility hooks for specific wallet sections
export const useWalletBalance = () => {
  const { balance, isLoading, error, refreshData } = useGoogleWallet();

  return {
    balance,
    isLoading,
    error,
    refresh: refreshData,
  };
};

export const useWalletTransactions = () => {
  const { transactions, isLoading, error, refreshData } = useGoogleWallet();

  return {
    transactions,
    isLoading,
    error,
    refresh: refreshData,
  };
};

export const useWalletStatus = () => {
  const { isWalletReady, isLoading, error, initWallet } = useGoogleWallet();

  return {
    isWalletReady,
    isLoading,
    error,
    initialize: initWallet,
  };
};
