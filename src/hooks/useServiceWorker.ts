import { serviceWorkerManager } from "@/lib/service-worker";
import { NotificationPayload } from "@/lib/service-worker";

// React hook for service worker functionality
export function useServiceWorker() {
  const registerServiceWorker = async () => {
    return await serviceWorkerManager.registerServiceWorker();
  };

  const requestNotificationPermission = async () => {
    return await serviceWorkerManager.requestNotificationPermission();
  };

  const subscribeToPush = async (vapidPublicKey: string) => {
    const subscription = await serviceWorkerManager.subscribeToPush(
      vapidPublicKey
    );
    if (subscription) {
      await serviceWorkerManager.sendSubscriptionToServer(subscription);
    }
    return subscription;
  };

  const unsubscribeFromPush = async () => {
    return await serviceWorkerManager.unsubscribeFromPush();
  };

  const showLocalNotification = async (payload: NotificationPayload) => {
    return await serviceWorkerManager.showLocalNotification(payload);
  };

  const updateServiceWorker = async () => {
    return await serviceWorkerManager.updateServiceWorker();
  };

  const triggerNotificationSync = async () => {
    return await serviceWorkerManager.triggerNotificationSync();
  };

  return {
    registerServiceWorker,
    requestNotificationPermission,
    subscribeToPush,
    unsubscribeFromPush,
    showLocalNotification,
    updateServiceWorker,
    triggerNotificationSync,
    isSupported: serviceWorkerManager.isServiceWorkerSupported(),
    isPushSupported: serviceWorkerManager.isPushSupported(),
    notificationPermission: serviceWorkerManager.getNotificationPermission(),
  };
}
