"use client";

import { useCallback, useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "@/store";
import { useSocket } from "./useSocket";
import { useAuth } from "./useAuth";
import { toast } from "sonner";
import {
  notify,
  notificationManager,
  type NotificationChannels,
} from "@/lib/notifications";

import {
  // Preferences
  selectRoomNotifications,
  selectChatNotifications,
  selectContractNotifications,
  selectProposalNotifications,
  selectSystemAlertsNotifications,
  selectWeeklyDigestNotifications,
  selectRoleChangesNotifications,
  //
  selectNotificationPreferences,
  selectIsLoading,
  selectIsSaving,
  selectError,
  setNotificationSettings,
  updateNotificationSettings,
  updateContextNotificationSettings,
  resetNotificationSettings,
  setLoading,
  setSaving,
  setError,
  clearError,
  type NotificationPreferences,
  type CRUDOperations,
} from "@/store/slices/settings";

import useS<PERSON> from "swr";
import fetcher from "@/lib/common/requests";
import {
  fetchNotifications,
  fetchNotification,
  createNotification,
  updateNotification as updateNotificationThunk,
  deleteNotification as deleteNotificationThunk,
  markAsRead,
  markAsUnread,
  performBulkAction,
  getNotificationCounts,
  markAllAsRead,
  markMultipleAsRead,
  markMultipleAsUnread,
  deleteMultiple,
  softDeleteMultiple,
  type NotificationQuery,
  type NotificationUpdate,
  type BulkActionRequest,
} from "@/store/actions/notification";

/**
 * Utility function to determine enabled channels based on user preferences
 */
function getEnabledChannelsForType(
  notificationType: string,
  preferences: NotificationPreferences | null,
  userEmail?: string
): NotificationChannels {
  if (!preferences) {
    return { email: false, push: false, inApp: true }; // Default: only in-app
  }

  const hasAnyCRUDEnabled = (contextPrefs: any) => {
    if (!contextPrefs || typeof contextPrefs !== "object") return false;
    return contextPrefs.create || contextPrefs.update || contextPrefs.delete;
  };

  let emailEnabled = false;
  let pushEnabled = false;
  let inAppEnabled = false;

  // For context-based notifications (room, chat, proposal, contract)
  if (["room", "chat", "proposal", "contract"].includes(notificationType)) {
    emailEnabled = hasAnyCRUDEnabled(preferences.email?.[notificationType]);
    pushEnabled = hasAnyCRUDEnabled(preferences.push?.[notificationType]);
    inAppEnabled = hasAnyCRUDEnabled(preferences.inApp?.[notificationType]);
  }
  // For direct boolean properties (systemAlerts, weeklyDigest, roleChanges)
  else {
    emailEnabled = preferences.email?.[notificationType] === true;
    pushEnabled = preferences.push?.[notificationType] === true;
    inAppEnabled = preferences.inApp?.[notificationType] === true;
  }

  // Require user email for email notifications
  emailEnabled = emailEnabled && !!userEmail;

  return { email: emailEnabled, push: pushEnabled, inApp: inAppEnabled };
}

/**
 * Hook for managing notification preferences
 * Decoupled from general preferences for focused notification management
 *
 * @param options.enableSocketHandling - Whether this instance should handle socket notifications (default: false)
 *                                      Only one instance should have this enabled to prevent duplicates
 */
export function useNotifications(
  options: { enableSocketHandling?: boolean } = {}
) {
  const { enableSocketHandling = false } = options;
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useAuth();
  const [realtimeNotifications, setRealtimeNotifications] = useState<any[]>([]);

  // Fetch notifications using SWR for real-time updates
  const { data: notificationsData, mutate: mutateNotifications } = useSWR(
    "notification",
    fetcher
  );

  const baseNotifications = notificationsData?.data || [];

  // Selectors
  const notificationPreferences: any = useSelector(
    selectNotificationPreferences
  );

  // Combine base notifications with real-time notifications
  const notifications = [...realtimeNotifications, ...baseNotifications].filter(
    (notification, index, self) =>
      index === self.findIndex((n) => n.id === notification.id)
  );

  // Memoized socket event handlers to prevent endless loops
  const handleNotification = useCallback(
    async (notification: any) => {
      console.log("Real-time notification received:", notification);

      // Add to real-time notifications (for UI display)
      setRealtimeNotifications((prev) => {
        // Check if notification already exists
        const exists = prev.some((n) => n.id === notification.id);
        if (exists) return prev;

        // Add new notification to the beginning
        return [notification, ...prev];
      });

      // Determine enabled channels based on user preferences
      const notificationType = notification.type || "chat";
      const enabledChannels = getEnabledChannelsForType(
        notificationType,
        notificationPreferences,
        user?.email
      );

      console.log(
        `🔧 [useNotifications] Channel preferences for ${notificationType}:`,
        {
          enabledChannels,
          hasUserEmail: !!user?.email,
          preferences: notificationPreferences,
        }
      );

      // Only call unified system if ANY channel is enabled
      if (
        enabledChannels.email ||
        enabledChannels.push ||
        enabledChannels.inApp
      ) {
        try {
          await notify(
            {
              type: notification.type || "chat",
              title: notification.title,
              message: notification.message,
              data: {
                ...notification.data,
                userId: user?.id, // Required for push notifications
              },
              emailData: {
                to: user?.email || "",
                templateData: {
                  title: notification.title,
                  message: notification.message,
                  actionUrl: notification.data?.actionUrl,
                  userName: user?.name,
                  notificationType: notification.type,
                },
              },
              pushData: {
                icon: "/favicons/android-chrome-192x192.png",
                badge: "/favicons/favicon-32x32.png",
                tag: `notification-${notification.type}-${Date.now()}`,
                actions: [
                  {
                    action: "view",
                    title: "View",
                    icon: "/favicons/favicon-16x16.png",
                  },
                  {
                    action: "dismiss",
                    title: "Dismiss",
                  },
                ],
                requireInteraction:
                  notification.priority === "high" ||
                  notification.priority === "urgent",
              },
              inAppData: {
                variant: "success",
                action: notification.data?.actionUrl
                  ? {
                      label: "View",
                      onClick: () =>
                        (window.location.href = notification.data.actionUrl),
                    }
                  : undefined,
              },
            },
            undefined, // operation
            enabledChannels // Only enabled channels
          );
        } catch (error) {
          console.error(
            "Failed to send notification via unified library:",
            error
          );
          // Fallback to direct toast if unified library fails (only if in-app is enabled)
          if (enabledChannels.inApp) {
            toast.success(notification.title, {
              description: notification.message,
              action: notification.data?.actionUrl
                ? {
                    label: "View",
                    onClick: () =>
                      (window.location.href = notification.data.actionUrl),
                  }
                : undefined,
            });
          }
        }
      } else {
        console.log(
          `🔕 [useNotifications] No channels enabled for ${notificationType}, skipping notification`
        );
      }

      // Refresh SWR cache to get updated data
      mutateNotifications();
    },
    [
      setRealtimeNotifications,
      mutateNotifications,
      user,
      notificationPreferences,
    ]
  );

  const handleConnect = useCallback(() => {
    console.log("Socket connected for notifications");
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log("Socket disconnected for notifications");
  }, []);

  const handleError = useCallback((error: any) => {
    console.error("Socket error in notifications:", error);
  }, []);

  // Socket integration for real-time notifications (only for designated handler)
  const socketResult = useSocket({
    autoConnect: enableSocketHandling,
    onNotification: enableSocketHandling ? handleNotification : undefined,
    onConnect: enableSocketHandling ? handleConnect : undefined,
    onDisconnect: enableSocketHandling ? handleDisconnect : undefined,
    onError: enableSocketHandling ? handleError : undefined,
  });

  const {
    isConnected,
    joinContext: joinSocketContext,
    leaveContext: leaveSocketContext,
  } = socketResult;

  // Note: Preference management is centralized in NotificationIntegrationProvider
  // to prevent multiple sync points and ensure consistency

  // Preference Settings by Context
  const roomNotifications = useSelector(selectRoomNotifications);
  const chatNotifications = useSelector(selectChatNotifications);
  const contractNotifications = useSelector(selectContractNotifications);
  const proposalNotifications = useSelector(selectProposalNotifications);
  const systemAlertsNotifications = useSelector(
    selectSystemAlertsNotifications
  );
  const weeklyDigestNotifications = useSelector(
    selectWeeklyDigestNotifications
  );
  const roleChangesNotifications = useSelector(selectRoleChangesNotifications);

  const isLoading = useSelector(selectIsLoading);
  const isSaving = useSelector(selectIsSaving);
  const error = useSelector(selectError);

  // Set all notification preferences
  const setAllNotifications = useCallback(
    async (notifications: NotificationPreferences) => {
      try {
        dispatch(setSaving(true));
        dispatch(clearError());
        dispatch(setNotificationSettings(notifications));
        toast.success("Notification preferences updated successfully");
        return { success: true };
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update notifications";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setSaving(false));
      }
    },
    [dispatch]
  );

  // Update specific notification preference
  const updateNotification = useCallback(
    async (
      category: keyof NotificationPreferences,
      key: string,
      value: boolean
    ) => {
      try {
        dispatch(updateNotificationSettings({ category, key, value }));
        return { success: true };
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update notification";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [dispatch]
  );

  // Update context-based notification preference (CRUD operations)
  const updateContextNotification = useCallback(
    async (
      category: keyof NotificationPreferences,
      context: string,
      operation: keyof CRUDOperations,
      value: boolean
    ) => {
      try {
        dispatch(
          updateNotificationSettings({
            category,
            context,
            operation,
            key: "",
            value,
          })
        );
        return { success: true };
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update notification";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [dispatch]
  );

  // Update entire context CRUD operations
  const updateContextOperations = useCallback(
    async (
      category: keyof NotificationPreferences,
      context: string,
      operations: Partial<CRUDOperations>
    ) => {
      try {
        dispatch(
          updateContextNotificationSettings({ category, context, operations })
        );
        return { success: true };
      } catch (error: any) {
        const errorMessage =
          error?.message || "Failed to update context operations";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [dispatch]
  );

  // Update multiple notifications in a category
  const updateCategoryNotifications = useCallback(
    async (
      category: keyof NotificationPreferences,
      updates: Record<string, boolean>
    ) => {
      try {
        dispatch(setSaving(true));
        dispatch(clearError());

        // Update each notification in the category
        Object.entries(updates).forEach(([key, value]) => {
          dispatch(updateNotificationSettings({ category, key, value }));
        });

        toast.success(`${category} notifications updated successfully`);
        return { success: true };
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update notifications";
        dispatch(setError(errorMessage));
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        dispatch(setSaving(false));
      }
    },
    [dispatch]
  );

  // Reset all notifications to defaults
  const resetNotifications = useCallback(async () => {
    try {
      dispatch(setSaving(true));
      dispatch(clearError());
      dispatch(resetNotificationSettings());
      toast.success("Notification preferences reset to defaults");
      return { success: true };
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to reset notifications";
      dispatch(setError(errorMessage));
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      dispatch(setSaving(false));
    }
  }, [dispatch]);

  // Toggle all notifications in a category
  const toggleCategoryNotifications = useCallback(
    async (category: keyof NotificationPreferences, enabled: boolean) => {
      try {
        const categorySettings = notificationPreferences[category];
        if (!categorySettings)
          return { success: false, error: "Invalid category" };

        const updates: Record<string, boolean> = {};
        Object.keys(categorySettings).forEach((key) => {
          updates[key] = enabled;
        });

        return await updateCategoryNotifications(category, updates);
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to toggle notifications";
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    [notificationPreferences, updateCategoryNotifications]
  );

  // Get notification summary
  const getNotificationSummary = useCallback(() => {
    if (!notificationPreferences) return { total: 0, enabled: 0, disabled: 0 };

    let total = 0;
    let enabled = 0;

    Object.values(notificationPreferences).forEach((category) => {
      Object.values(category).forEach((value) => {
        total++;
        if (value) enabled++;
      });
    });

    return {
      total,
      enabled,
      disabled: total - enabled,
      enabledPercentage: total > 0 ? Math.round((enabled / total) * 100) : 0,
    };
  }, [notificationPreferences]);

  // Check if a specific notification is enabled
  const isNotificationEnabled = useCallback(
    (
      category: keyof NotificationPreferences,
      context: string,
      operation?: keyof CRUDOperations
    ): boolean => {
      const categorySettings = notificationPreferences?.[category];
      if (!categorySettings) return false;

      const contextSettings = (categorySettings as any)[context];

      // Handle CRUD operations
      if (operation && typeof contextSettings === "object") {
        return contextSettings[operation] || false;
      }

      // Handle direct boolean properties (systemAlerts, weeklyDigest, roleChanges)
      if (typeof contextSettings === "boolean") {
        return contextSettings;
      }

      return false;
    },
    [notificationPreferences]
  );

  // Loading and error management
  const setLoadingState = useCallback(
    (loading: boolean) => {
      dispatch(setLoading(loading));
    },
    [dispatch]
  );

  const setSavingState = useCallback(
    (saving: boolean) => {
      dispatch(setSaving(saving));
    },
    [dispatch]
  );

  const setErrorState = useCallback(
    (error: string | null) => {
      dispatch(setError(error));
    },
    [dispatch]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Helper function to send notifications via unified library
  const sendUnifiedNotification = useCallback(
    async (
      type: string,
      title: string,
      message: string,
      options?: {
        operation?: "create" | "update" | "delete";
        data?: any;
        emailData?: any;
        pushData?: any;
        inAppData?: any;
        channelOverride?: { email?: boolean; inApp?: boolean; push?: boolean };
      }
    ) => {
      try {
        const result = await notify(
          {
            type: type as any,
            title,
            message,
            data: options?.data,
            emailData: options?.emailData,
            pushData: options?.pushData,
            inAppData: {
              variant: "default",
              ...options?.inAppData,
            },
          },
          options?.operation,
          options?.channelOverride
        );
        return result;
      } catch (error) {
        console.error("Failed to send unified notification:", error);
        // Fallback to direct toast
        toast(title, { description: message });
        return {
          success: false,
          results: { email: false, inApp: true, push: false },
        };
      }
    },
    []
  );

  return {
    // Data
    notifications,
    // State
    notificationPreferences,
    isLoading,
    isSaving,
    error,

    // Context-specific notifications
    roomNotifications,
    chatNotifications,
    contractNotifications,
    proposalNotifications,
    systemAlertsNotifications,
    weeklyDigestNotifications,
    roleChangesNotifications,

    // Actions
    setAllNotifications,
    updateNotification,
    updateContextNotification,
    updateContextOperations,
    updateCategoryNotifications,
    resetNotifications,
    toggleCategoryNotifications,

    // Utilities
    getNotificationSummary,
    isNotificationEnabled,

    // State management
    setLoadingState,
    setSavingState,
    setErrorState,
    clearErrorState,

    // Aliases for backward compatibility
    setPreferenceNotifications: setAllNotifications,
    updatePreferenceNotification: updateNotification,
    resetPreferenceNotifications: resetNotifications,

    // Notification CRUD actions (thunk-based with SWR integration)
    fetchUserNotifications: useCallback(
      async (query: NotificationQuery = {}) => {
        const result = await dispatch(fetchNotifications(query));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    fetchSingleNotification: useCallback(
      async (id: string) => {
        return await dispatch(fetchNotification(id));
      },
      [dispatch]
    ),

    createNewNotification: useCallback(
      async (notificationData: any) => {
        const result = await dispatch(createNotification(notificationData));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    updateSingleNotification: useCallback(
      async (id: string, updates: NotificationUpdate) => {
        const result = await dispatch(updateNotificationThunk({ id, updates }));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    deleteSingleNotification: useCallback(
      async (id: string) => {
        const result = await dispatch(deleteNotificationThunk(id));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    markNotificationAsRead: useCallback(
      async (id: string) => {
        const result = await dispatch(markAsRead(id));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    markNotificationAsUnread: useCallback(
      async (id: string) => {
        const result = await dispatch(markAsUnread(id));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    performNotificationBulkAction: useCallback(
      async (request: BulkActionRequest) => {
        const result = await dispatch(performBulkAction(request));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    getNotificationCountsData: useCallback(async () => {
      return await dispatch(getNotificationCounts());
    }, [dispatch]),

    markAllNotificationsAsRead: useCallback(async () => {
      const result = await dispatch(markAllAsRead());
      mutateNotifications(); // Refresh SWR cache
      return result;
    }, [dispatch, mutateNotifications]),

    markMultipleNotificationsAsRead: useCallback(
      async (notificationIds: string[]) => {
        const result = await dispatch(markMultipleAsRead(notificationIds));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    markMultipleNotificationsAsUnread: useCallback(
      async (notificationIds: string[]) => {
        const result = await dispatch(markMultipleAsUnread(notificationIds));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    deleteMultipleNotifications: useCallback(
      async (notificationIds: string[]) => {
        const result = await dispatch(deleteMultiple(notificationIds));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    softDeleteMultipleNotifications: useCallback(
      async (notificationIds: string[]) => {
        const result = await dispatch(softDeleteMultiple(notificationIds));
        mutateNotifications(); // Refresh SWR cache
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    // Convenient aliases for component usage
    markAsRead: useCallback(
      async (id: string) => {
        const result = await dispatch(markAsRead(id));
        mutateNotifications();
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    markAsUnread: useCallback(
      async (id: string) => {
        const result = await dispatch(markAsUnread(id));
        mutateNotifications();
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    deleteNotification: useCallback(
      async (id: string) => {
        const result = await dispatch(deleteNotificationThunk(id));
        mutateNotifications();
        return result;
      },
      [dispatch, mutateNotifications]
    ),

    markAllAsRead: useCallback(async () => {
      const result = await dispatch(markAllAsRead());
      mutateNotifications();
      return result;
    }, [dispatch, mutateNotifications]),

    clearAll: useCallback(async () => {
      // Get all notification IDs
      const notificationIds = notifications.map((n: any) => n.id);
      if (notificationIds.length > 0) {
        const result = await dispatch(deleteMultiple(notificationIds));
        mutateNotifications();
        return result;
      }
    }, [dispatch, mutateNotifications, notifications]),

    // Socket connection management
    isSocketConnected: isConnected,
    joinNotificationContext: joinSocketContext,
    leaveNotificationContext: leaveSocketContext,

    // Real-time notification management
    clearRealtimeNotifications: useCallback(() => {
      setRealtimeNotifications([]);
    }, []),

    // Unified notification library integration
    sendUnifiedNotification,
  };
}
