import cn from "clsx";

const Listing = ({
  grid = 0,
  request,
  className,
  Component,
}: ListingProps): React.ReactNode => {
  const { data, isLoading, error }: RequestParams = request ?? {};

  if (error) return <>Error</>;
  if (isLoading) return <>Loading...</>;
  if (!Component) return <>Please pass a component</>;
  if (!data?.length) return <></>;

  return (
    <div
      className={cn(
        "w-full",
        grid > 0 ? `grid grid-cols-${grid} gap-8` : "",
        className
      )}
    >
      {data?.map((item: unknown, index: number) => {
        return <Component key={index} {...(item as object)} />;
      })}
    </div>
  );
};

interface ListingProps {
  grid?: number;
  className?: string;
  request: RequestParams;
  Component: React.FC<any>;
}

interface RequestParams {
  data: unknown[];
  isLoading: boolean;
  error: unknown;
}

export default Listing;
