import cn from "clsx";

export default function Section({
  title,
  position = "start",
  children,
}: SectionProps) {
  const positions: Record<string, string> = {
    start: "items-start",
    center: "items-center",
    end: "items-end",
  };

  return (
    <section className={cn("w-full flex flex-col p-16")}>
      {/* Section content */}
      <span className={cn("w-full flex flex-col gap-12", positions[position])}>
        <h6 className="text-lg md:text-xl lg:text-2xl font-light capitalize text-white/50">
          {title}
        </h6>
        <span>{children}</span>
      </span>
    </section>
  );
}

interface SectionProps {
  title: string;
  position?: string;
  children: React.ReactNode;
}
