export default function DataWaveTheme({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={`w-full relative`}>
      {/* <CTA /> */}
      <div className="absolute w-full h-max z-30">{children}</div>
      {/* Wallpaper */}
      <video
        className="w-screen"
        src="/video/spherewave.mp4"
        loop
        muted
        autoPlay
        playsInline
      >
        <source src="/video/spherewave.mp4" type="video/mp4" />
      </video>

      {/* Overlay */}
      <div className="w-full h-screen flex bg-linear-to-t from-black via-black/80 to-black/50 absolute top-0 left-0 z-10" />
    </div>
  );
}
