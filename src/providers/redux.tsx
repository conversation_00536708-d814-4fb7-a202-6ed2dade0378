"use client";

import { ReactNode, useEffect } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { useSession } from "next-auth/react";
import { store, persistor } from "@/store";
import { Loader } from "@/components/common/loader";
import { setSession, setLoading } from "@/store/slices/auth";

// Component to sync next-auth session with Redux
function SessionSync({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Update Redux store when next-auth session changes
    store.dispatch(setSession(session));
    store.dispatch(setLoading(status === "loading"));
  }, [session, status]);

  return <>{children}</>;
}

// Loading component for PersistGate
function PersistLoading() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <Loader active={true} />
    </div>
  );
}

// Main Redux provider component
export function ReduxProvider({ children }: { children: ReactNode }) {
  return (
    <Provider store={store}>
      <PersistGate loading={<PersistLoading />} persistor={persistor}>
        <SessionSync>{children}</SessionSync>
      </PersistGate>
    </Provider>
  );
}
