"use client";

import React, { ReactNode, useEffect } from "react";
import { useNotifications } from "@/hooks/useNotifications";
import { convertPreferencesToLibraryFormat } from "@/lib/notifications/integration";
import { notificationManager } from "@/lib/notifications";

interface NotificationIntegrationProviderProps {
  children: ReactNode;
}

/**
 * NotificationIntegrationProvider
 *
 * This provider automatically syncs notification preferences from the useNotifications hook
 * with the unified notification library, ensuring that all notifications respect user settings.
 *
 * Features:
 * - Automatic preference syncing between Redux store and notification library
 * - Format conversion between hook preferences and library preferences
 * - Real-time updates when preferences change
 * - Seamless integration without affecting existing components
 */
export function NotificationIntegrationProvider({
  children,
}: NotificationIntegrationProviderProps) {
  // Enable socket handling in the main provider to prevent duplicate connections
  const { notificationPreferences } = useNotifications({
    enableSocketHandling: true,
  });

  // Single source of truth for preference syncing
  useEffect(() => {
    if (notificationPreferences) {
      try {
        // Convert preferences to library format if needed
        const libraryPreferences = convertPreferencesToLibraryFormat(
          notificationPreferences
        );

        // Set preferences in the notification manager (single sync point)
        notificationManager.setPreferences(libraryPreferences);

        console.log(
          "✅ [NotificationIntegrationProvider] Preferences synced:",
          {
            originalPreferences: notificationPreferences,
            libraryPreferences,
          }
        );
      } catch (error) {
        console.error(
          "❌ [NotificationIntegrationProvider] Sync failed:",
          error
        );
      }
    }
  }, [notificationPreferences]);

  return <>{children}</>;
}

/**
 * Hook to access notification integration status
 * Useful for debugging and monitoring the integration
 */
export function useNotificationIntegration() {
  // Don't enable socket handling here since it's handled by the provider
  const { notificationPreferences, isLoading } = useNotifications({
    enableSocketHandling: false,
  });

  const isIntegrated = React.useMemo(() => {
    // Check if preferences are loaded and synced
    return !isLoading && notificationPreferences !== null;
  }, [isLoading, notificationPreferences]);

  const getIntegrationStatus = React.useCallback(() => {
    return {
      hasPreferences: !!notificationPreferences,
      isLoading,
      isIntegrated,
      libraryHasPreferences: notificationManager.getPreferences?.() !== null,
    };
  }, [notificationPreferences, isLoading, isIntegrated]);

  return {
    isIntegrated,
    getIntegrationStatus,
    preferences: notificationPreferences,
  };
}
