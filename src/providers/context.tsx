"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface UserInterface {
  id: string;
  component: string;
  active: boolean;
}

interface UserInterfaceContextType {
  userInterface: UserInterface;
  setUserInterfaceState: React.Dispatch<React.SetStateAction<UserInterface>>;
}

export const UserInterfaceContext =
  createContext<UserInterfaceContextType | null>(null);

export const UserInterfaceProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [userInterface, setUserInterfaceState] = useState<UserInterface>({
    id: "",
    component: "",
    active: false,
  });

  return (
    <UserInterfaceContext.Provider
      value={{ userInterface, setUserInterfaceState }}
    >
      {children}
    </UserInterfaceContext.Provider>
  );
};

export const useUserInterface = () => {
  const context = useContext(UserInterfaceContext);
  if (!context) {
    throw new Error(
      "useUserInterface must be used within a UserInterfaceProvider"
    );
  }
  return context;
};
