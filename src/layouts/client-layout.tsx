"use client";

import React from "react";
import { Providers } from "@/providers";
import { Toaster } from "@/components/common/ui/sonner";
import { IntlProvider } from "@/components/providers/intl-provider";

interface ClientLayoutProps {
  children: React.ReactNode;
  lang: string;
  messages: any;
}

export function ClientLayout({ children, lang, messages }: ClientLayoutProps) {
  return (
    <>
      <Toaster position="top-center" expand />
      <IntlProvider messages={messages} locale={lang}>
        <Providers>{children}</Providers>
      </IntlProvider>
    </>
  );
}
