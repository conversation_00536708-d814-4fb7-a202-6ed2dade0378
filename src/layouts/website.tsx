"use client";

import { useState, useEffect } from "react";

import { motion, useScroll, useSpring } from "framer-motion";
import { Navbar, <PERSON>ubar, Footer } from "@/components/common";

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [menubar, setMenuBar] = useState(false);

  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });

  useEffect(() => {
    const scrolling = () =>
      window.scrollY > 120 ? setMenuBar(true) : setMenuBar(false);

    window.addEventListener("scroll", scrolling, { passive: true });

    return () => {
      window.removeEventListener("scroll", scrolling);
    };
  }, []);

  return (
    <div className="w-full">
      <motion.div className="progress-bar" style={{ scaleX }} />
      {/* Content */}
      <Navbar />
      <Menubar active={menubar} />
      {children}
      <Footer />
    </div>
  );
}
