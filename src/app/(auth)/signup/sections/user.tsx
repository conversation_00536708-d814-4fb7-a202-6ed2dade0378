"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Eye, EyeOff, Mail } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { FcGoogle as GoogleLogo } from "react-icons/fc";

import { Loader } from "@/components/common/loader";
import { SimpleThemeToggle } from "@/components/common/ui/theme-toggle";

export default function RegisterUserDetails() {
  const router = useRouter();
  const { registerUser, isAuthenticated, isLoading, setRegistrationState } =
    useAuth();

  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    password: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push("/");
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCredentialsSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      await registerUser(
        formData.firstName,
        formData.lastName,
        formData.phone,
        formData.email,
        formData.password
      );
      // Set registration state to true to proceed to company details
      setRegistrationState(true);
      // Success notification is handled by Redux action
    } catch (error: any) {
      console.error("Registration error:", error);
      // Error notification is handled by Redux action
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <Loader active />;
  }

  return (
    <div className="min-h-screen w-full flex justify-center items-center bg-background relative">
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4">
        <SimpleThemeToggle />
      </div>

      <Card variant="ghost" className="w-full max-w-xl">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold text-center text-card-foreground">
            Create Account
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            Sign up to TenderBank for a New Account
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
              {error}
            </div>
          )}

          {/* Email/Password Form */}
          <form onSubmit={handleCredentialsSignUp} className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-5">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="firstName" className="text-card-foreground">
                    First Name
                  </Label>
                </div>

                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  placeholder="Enter your first name"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="lastName" className="text-card-foreground">
                    Last Name
                  </Label>
                </div>

                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  placeholder="Enter your last name"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="phone" className="text-card-foreground">
                    Phone
                  </Label>
                </div>

                <Input
                  id="phone"
                  name="phone"
                  type="phone"
                  placeholder="Enter your phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="email" className="text-card-foreground">
                    Email
                  </Label>
                </div>

                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="password" className="text-card-foreground">
                    Password
                  </Label>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pr-10 bg-background border-border text-foreground placeholder:text-muted-foreground"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-card-foreground">
                  <Label htmlFor="password" className="text-card-foreground">
                    Confirm Password
                  </Label>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pr-10 bg-background border-border text-foreground placeholder:text-muted-foreground"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Link
                href="/forgot"
                className="text-xs text-primary hover:underline transition-colors"
              >
                Forgot password?
              </Link>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={
                isSubmitting ||
                !formData.firstName ||
                !formData.lastName ||
                !formData.phone ||
                !formData.email ||
                !formData.password
              }
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing Up...
                </>
              ) : (
                "Sign Up"
              )}
            </Button>

            <Button type="submit" className="w-full">
              <GoogleLogo className="h-4 w-4 mr-2" />
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing Up...
                </>
              ) : (
                "Sign Up with Google"
              )}
            </Button>
          </form>

          <div className="text-center text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/"
              className="text-primary hover:underline transition-colors"
            >
              Sign In
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
