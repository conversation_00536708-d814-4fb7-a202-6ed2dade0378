"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Info } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useCompany } from "@/hooks/useCompany";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";

import { Loader } from "@/components/common/loader";
import { SimpleThemeToggle } from "@/components/common/ui/theme-toggle";

export default function RegisterCompanyDetails() {
  const router = useRouter();
  const { isAuthenticated, isLoading, setRegistrationState, redirectToHome } =
    useAuth();
  const { createCompany, isLoading: isCompanyLoading } = useCompany();

  const [formData, setFormData] = useState({
    name: "",
    tin: "",
    category: "",
    address: "",
    phone: "",
    email: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCompanyRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      await createCompany(formData);
      // Reset registration state and redirect to signin
      setRegistrationState(false);
      router.push("/");
    } catch (error: any) {
      console.error("Company registration error:", error);
      setError(error.message || "Failed to register company");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || isCompanyLoading) {
    return <Loader active />;
  }

  return (
    <div className="min-h-screen w-full flex justify-center items-center bg-background relative">
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4">
        <SimpleThemeToggle />
      </div>

      <Card variant="ghost" className="w-full max-w-xl">
        <CardHeader className="space-y-2">
          <CardTitle className="text-2xl font-bold text-center text-card-foreground">
            Company Information
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            Please provide your company details
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
              {error}
            </div>
          )}

          {/* Company Registration Form */}
          <form
            onSubmit={handleCompanyRegistration}
            className="w-full space-y-6"
          >
            {/* Company Name */}
            <div className="space-y-3">
              <Label htmlFor="name" className="text-card-foreground">
                Company Name
              </Label>
              <Input
                id="name"
                name="name"
                type="text"
                placeholder="Full Legal Name"
                value={formData.name}
                onChange={handleInputChange}
                className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                required
              />
            </div>

            {/* TIN */}
            <div className="space-y-3">
              <Label htmlFor="tin" className="text-card-foreground">
                TIN
              </Label>
              <div className="relative">
                <Input
                  id="tin"
                  name="tin"
                  type="text"
                  placeholder="--- --- ---"
                  value={formData.tin}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground pr-10"
                  required
                />
                <Info className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            {/* Category */}
            <div className="space-y-3">
              <Label htmlFor="category" className="text-card-foreground">
                Category
              </Label>
              <Select
                onValueChange={(value) => handleSelectChange("category", value)}
              >
                <SelectTrigger className="bg-background border-border text-foreground">
                  <SelectValue placeholder="What kind of company?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="consulting">Consulting</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Company Address */}
            <div className="space-y-3">
              <Label htmlFor="address" className="text-card-foreground">
                Company Address
              </Label>
              <Select
                onValueChange={(value) => handleSelectChange("address", value)}
              >
                <SelectTrigger className="bg-background border-border text-foreground">
                  <SelectValue placeholder="Type or Select Address" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kampala">Kampala, Uganda</SelectItem>
                  <SelectItem value="entebbe">Entebbe, Uganda</SelectItem>
                  <SelectItem value="jinja">Jinja, Uganda</SelectItem>
                  <SelectItem value="mbarara">Mbarara, Uganda</SelectItem>
                  <SelectItem value="gulu">Gulu, Uganda</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Phone and Email Row */}
            <div className="grid lg:grid-cols-2 gap-5">
              <div className="space-y-3">
                <Label htmlFor="phone" className="text-card-foreground">
                  Company Phone Number
                </Label>
                <div className="flex">
                  <Select defaultValue="+255">
                    <SelectTrigger className="w-20 bg-background border-border text-foreground">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="+255">+255</SelectItem>
                      <SelectItem value="+256">+256</SelectItem>
                      <SelectItem value="+254">+254</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="--- --- ---"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="flex-1 ml-2 bg-background border-border text-foreground placeholder:text-muted-foreground"
                    required
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="email" className="text-card-foreground">
                  Company Email Address
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="bg-background border-border text-foreground placeholder:text-muted-foreground"
                  required
                />
              </div>
            </div>

            <div className="grid lg:grid-cols-8 gap-5">
              <Button
                type="button"
                variant="default"
                className="col-span-2"
                onClick={() => redirectToHome()}
              >
                Skip
              </Button>
              <Button
                type="submit"
                className="col-span-6"
                disabled={
                  isSubmitting ||
                  !formData.name ||
                  !formData.tin ||
                  !formData.category ||
                  !formData.address ||
                  !formData.phone ||
                  !formData.email
                }
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Finishing...
                  </>
                ) : (
                  "Finish"
                )}
              </Button>
            </div>
          </form>

          <div className="text-center text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/"
              className="text-primary hover:underline transition-colors"
            >
              Sign In
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
