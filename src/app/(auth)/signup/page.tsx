"use client";

import { useAuth } from "@/hooks/useAuth";
import { Loader } from "@/components/common/loader";

import RegisterUserDetails from "./sections/user";
import RegisterCompanyDetails from "./sections/company";

export default function Page() {
  const { isLoading, isUserRegistered } = useAuth();

  if (isLoading) {
    return <Loader active />;
  }

  // Multi-step registration logic
  if (!isUserRegistered) {
    // Step 1: User personal details registration
    return <RegisterUserDetails />;
  } else {
    // Step 2: Company details registration
    return <RegisterCompanyDetails />;
  }
}
