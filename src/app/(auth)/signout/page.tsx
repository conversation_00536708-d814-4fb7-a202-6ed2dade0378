"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/common/ui/button";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { glassTheme } from "@/components/common/navbar";

export default function SignUpPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className={`w-full max-w-md ${glassTheme}`}>
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            You&apos;ve signed out
          </CardTitle>
          <CardDescription className="text-center">
            Hope to see you soon, someday
          </CardDescription>
        </CardHeader>

        <CardContent className="flex items-center justify-center space-y-4">
          <Button onClick={() => router.push("/")}>Go back home</Button>
        </CardContent>
      </Card>
    </div>
  );
}
