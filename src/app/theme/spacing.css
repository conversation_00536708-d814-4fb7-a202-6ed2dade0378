/* 3.-spacing-mode-1.css */
:root {
	--spacing-10xl: var(--Spacing-32-128px);
	--spacing-11xl: var(--Spacing-40-160px);
	--spacing-2xl: var(--Spacing-5-20px);
	--spacing-3xl: var(--Spacing-6-24px);
	--spacing-4xl: var(--Spacing-8-32px);
	--spacing-5xl: var(--Spacing-10-40px);
	--spacing-6xl: var(--Spacing-12-48px);
	--spacing-7xl: var(--Spacing-16-64px);
	--spacing-8xl: var(--Spacing-20-80px);
	--spacing-9xl: var(--Spacing-24-96px);
	--spacing-lg: var(--Spacing-3-12px);
	--spacing-md: var(--Spacing-2-8px);
	--spacing-none: var(--Spacing-0-0px);
	--spacing-sm: var(--Spacing-1-5-6px);
	--spacing-xl: var(--Spacing-4-16px);
	--spacing-xs: var(--Spacing-1-4px);
	--spacing-xxs: var(--Spacing-0-5-2px);
}
