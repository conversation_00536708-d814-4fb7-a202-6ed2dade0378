import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/api/services/role";

/**
 * GET /api/roles/[id]
 * Get a specific role by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get session for authentication
    const session: any = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    // Use the service to get the specific role
    const result = await roleService.getRoles({ id: param.id });

    // Return standardized response format
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Role GET error:", error);
    return NextResponse.json(error, { status: 500 });
  }
}

/**
 * PUT /api/roles/[id]
 * Update a specific role by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get session for authentication
    const session: any = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params and parse request body
    let param = await params;
    const body = await request.json();
    const updateData = { ...body, id: param.id };

    // Use the service to update the role
    const result = await roleService.updateRole(updateData);

    // Return standardized response format
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Role update error:", error);
    return NextResponse.json(error, { status: 500 });
  }
}

/**
 * DELETE /api/roles/[id]
 * Delete a specific role by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get session for authentication
    const session: any = await auth();

    // Initialize role service
    const roleService = new RoleService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    roleService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    // Use the service to delete the role
    const result = await roleService.deleteRole(param.id);

    // Return standardized response format
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Role deletion error:", error);
    return NextResponse.json(error, { status: 500 });
  }
}
