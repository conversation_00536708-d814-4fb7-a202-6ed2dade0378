import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { emailService } from "@/lib/mail/service";

/**
 * POST /api/notifications/send-email
 * Send generic notification email
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { to, type, title, message, actionUrl, priority, templateData } =
      body;

    if (!to || !type || !title || !message) {
      return NextResponse.json(
        { error: "Missing required fields: to, type, title, message" },
        { status: 400 }
      );
    }

    // Generate HTML content for the email
    const html = generateGenericEmailTemplate({
      title,
      message,
      type,
      actionUrl,
      priority,
    });

    // Generate plain text version
    const text = `${title}\n\n${message}${
      actionUrl
        ? `\n\nView details: ${process.env.NEXT_PUBLIC_APP_URL}${actionUrl}`
        : ""
    }`;

    // Send email using the email service
    await emailService.sendEmail({
      to,
      subject: `${type}: ${title}`,
      html,
      text,
    });

    return NextResponse.json({
      success: true,
      message: "Email sent successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error sending notification email:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Failed to send email",
      },
      { status: 500 }
    );
  }
}

/**
 * Generate HTML template for generic notification emails
 */
function generateGenericEmailTemplate(data: {
  title: string;
  message: string;
  type: string;
  actionUrl?: string;
  priority?: string;
}): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://tenderbank.com";
  const actionButton = data.actionUrl
    ? `<a href="${baseUrl}${data.actionUrl}" style="display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 16px 0;">View Details</a>`
    : "";

  const priorityBadge =
    data.priority === "high" || data.priority === "urgent"
      ? `<div style="background-color: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; display: inline-block; margin-bottom: 10px;">🚨 ${data.priority.toUpperCase()}</div>`
      : "";

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.type} - ${data.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          ${priorityBadge}
          <h1 style="color: #007bff; margin: 0 0 10px 0; font-size: 24px;">${data.title}</h1>
          <p style="margin: 0; color: #6c757d; font-size: 14px;">${data.type}</p>
        </div>
        
        <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
          <p style="margin: 0 0 16px 0; font-size: 16px;">${data.message}</p>
          ${actionButton}
        </div>
        
        <div style="margin-top: 20px; padding: 16px; background-color: #f8f9fa; border-radius: 4px; font-size: 12px; color: #6c757d;">
          <p style="margin: 0;">This is an automated notification from tenderbank International.</p>
          <p style="margin: 8px 0 0 0;">You can manage your notification preferences in your <a href="${baseUrl}/settings/notifications" style="color: #007bff;">account settings</a>.</p>
        </div>
      </body>
    </html>
  `;
}
