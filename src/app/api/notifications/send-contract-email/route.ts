import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { sendContractNotification } from "@/lib/mail";

/**
 * POST /api/notifications/send-contract-email
 * Send contract notification email
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { to, contractData } = body;

    if (!to || !contractData) {
      return NextResponse.json(
        { error: "Missing required fields: to, contractData" },
        { status: 400 }
      );
    }

    // Validate contract data structure
    if (!contractData.contractTitle || !contractData.clientName || 
        contractData.totalValue === undefined || !contractData.status) {
      return NextResponse.json(
        { error: "Invalid contractData: missing contractTitle, clientName, totalValue, or status" },
        { status: 400 }
      );
    }

    // Send contract notification email using the mail service
    await sendContractNotification(to, contractData);

    return NextResponse.json({
      success: true,
      message: "Contract email sent successfully",
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error sending contract notification email:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        message: "Failed to send contract email"
      },
      { status: 500 }
    );
  }
}
