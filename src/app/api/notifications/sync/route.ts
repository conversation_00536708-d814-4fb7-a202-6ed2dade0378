import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";
import { notificationManager } from "@/lib/notifications";

/**
 * POST /api/notifications/sync
 * Sync high-priority notifications via email and push channels
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { notifications } = body;

    if (!notifications || !Array.isArray(notifications)) {
      return NextResponse.json(
        { error: "Invalid notifications data" },
        { status: 400 }
      );
    }

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    const results = {
      processed: 0,
      emailsSent: 0,
      pushSent: 0,
      errors: [] as string[],
    };

    // Process each high-priority notification
    for (const notificationData of notifications) {
      try {
        // Get full notification details from database
        const notificationResponse = await notificationService.getNotification(
          notificationData.id,
          session.user.id
        );

        if (!notificationResponse.success || !notificationResponse.data) {
          results.errors.push(`Notification ${notificationData.id} not found`);
          continue;
        }

        const notification = notificationResponse.data;

        // Create notification payload for the unified library
        const payload = {
          type: notification.type as any,
          title: notification.title,
          message: notification.message,
          data: {
            userId: session.user.id,
            notificationId: notification.id,
            priority: notification.priority,
            ...notification.data,
          },
          emailData: session.user.email
            ? {
                to: session.user.email,
                templateData: {
                  actionUrl: getNotificationUrl(notification),
                  userName: session.user.name || session.user.email,
                  priority: notification.priority,
                  ...notification.data,
                },
              }
            : undefined,
          pushData: {
            icon: "/favicons/android-chrome-192x192.png",
            badge: "/favicons/favicon-32x32.png",
            tag: `sync-${notification.id}`,
            requireInteraction: notification.priority === "urgent",
            actions: [
              {
                action: "view",
                title: "View",
                icon: "/favicons/favicon-16x16.png",
              },
              {
                action: "dismiss",
                title: "Dismiss",
              },
            ],
          },
        };

        // Send notification through unified library
        // Force email and push for high-priority notifications
        const channelOverride = {
          email: !!session.user.email,
          push: true,
          inApp: false, // Skip in-app since this is for sync
        };

        const notifyResult = await notificationManager.notify(
          payload,
          notification.type,
          "read", // Operation type for CRUD-based preferences
          channelOverride
        );

        if (notifyResult.success) {
          results.processed++;
          if (notifyResult.results.email) results.emailsSent++;
          if (notifyResult.results.push) results.pushSent++;
        } else {
          results.errors.push(`Failed to sync notification ${notification.id}`);
        }
      } catch (error) {
        console.error(
          `Error processing notification ${notificationData.id}:`,
          error
        );
        results.errors.push(
          `Error processing notification ${notificationData.id}: ${error}`
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${results.processed} notifications`,
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error syncing notifications:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Failed to sync notifications",
      },
      { status: 500 }
    );
  }
}

/**
 * Generate appropriate URL for notification based on type
 */
function getNotificationUrl(notification: any): string {
  const baseUrl = "/";

  switch (notification.type) {
    case "chat":
      return notification.data?.roomId
        ? `${baseUrl}/chat/${notification.data.roomId}`
        : `${baseUrl}/chat`;

    case "contract":
      return notification.data?.contractId
        ? `${baseUrl}/contracts/${notification.data.contractId}`
        : `${baseUrl}/contracts`;

    case "proposal":
      return notification.data?.proposalId
        ? `${baseUrl}/proposals/${notification.data.proposalId}`
        : `${baseUrl}/proposals`;

    case "room":
      return notification.data?.roomId
        ? `${baseUrl}/rooms/${notification.data.roomId}`
        : `${baseUrl}/rooms`;

    case "systemAlerts":
      return `${baseUrl}/settings/notifications`;

    case "roleChanges":
      return `${baseUrl}/settings/account`;

    default:
      return baseUrl;
  }
}
