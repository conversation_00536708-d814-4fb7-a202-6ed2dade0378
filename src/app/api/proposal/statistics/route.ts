"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { ProposalService } from "@/lib/api/services/proposal";

export async function GET(request: NextRequest) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Get all proposals to calculate statistics
    const result: any = await proposalService.getProposals({});
    const proposals = result.proposals || [];

    // Calculate statistics
    const statistics = {
      total: proposals.length,
      pending: proposals.filter((p: any) =>
        [
          "created",
          "submitted",
          "received",
          "negotiating",
          "reviewing",
        ].includes(p.status)
      ).length,
      approved: proposals.filter((p: any) =>
        ["agreed", "inprogress"].includes(p.status)
      ).length,
      rejected: proposals.filter((p: any) => p.status === "rejected").length,
      totalValue: proposals.reduce(
        (sum: number, p: any) => sum + (p.total_budget || 0),
        0
      ),
    };

    return NextResponse.json(statistics, { status: 200 });
  } catch (error) {
    console.error("Proposal statistics error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
