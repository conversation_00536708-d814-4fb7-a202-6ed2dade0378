"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RequestService } from "@/lib/api/services/request";

// Initialize request service
const requestService = new RequestService();

/**
 * GET /api/request
 * Get requests with optional filtering and pagination
 * Query parameters: page, limit, status, userId, tender_id, search, sortBy, sortOrder
 */
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query: any = {};

    if (searchParams.get("page")) {
      query.page = parseInt(searchParams.get("page")!);
    }
    if (searchParams.get("limit")) {
      query.limit = parseInt(searchParams.get("limit")!);
    }
    if (searchParams.get("status")) {
      query.status = searchParams.get("status");
    }
    if (searchParams.get("userId")) {
      query.userId = searchParams.get("userId");
    }
    if (searchParams.get("tender_id")) {
      query.tender_id = searchParams.get("tender_id");
    }
    if (searchParams.get("search")) {
      query.search = searchParams.get("search");
    }
    if (searchParams.get("sortBy")) {
      query.sortBy = searchParams.get("sortBy");
    }
    if (searchParams.get("sortOrder")) {
      query.sortOrder = searchParams.get("sortOrder");
    }

    // Check for single request by ID
    const id = searchParams.get("id");
    if (id) {
      const result = await requestService.getRequest(id);
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 500,
      });
    }

    // Get multiple requests with filtering
    const result = await requestService.getRequests(query);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("GET /api/request error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/request
 * Create a new request
 */
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Add userId from session if not provided and user is authenticated
    if (!body.userId && session?.user?.id) {
      body.userId = session.user.id;
    }

    // Use the service to create request
    const result = await requestService.createRequest(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("POST /api/request error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/request
 * Update an existing request
 */
export async function PUT(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to update request
    const result = await requestService.updateRequest(body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("PUT /api/request error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/request
 * Delete a request by ID
 */
export async function DELETE(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Request ID is required",
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to delete request
    const result = await requestService.deleteRequest(id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("DELETE /api/request error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
