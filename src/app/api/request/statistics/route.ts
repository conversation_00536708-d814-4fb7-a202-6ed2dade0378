"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RequestService } from "@/lib/api/services/request";

// Initialize request service
const requestService = new RequestService();

/**
 * GET /api/request/statistics
 * Get request statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get statistics
    const result = await requestService.getRequestStats();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("GET /api/request/statistics error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
