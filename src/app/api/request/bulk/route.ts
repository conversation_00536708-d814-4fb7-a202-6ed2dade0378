"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RequestService } from "@/lib/api/services/request";

// Initialize request service
const requestService = new RequestService();

/**
 * POST /api/request/bulk
 * Perform bulk operations on requests
 */
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    // Set the service context with session and request
    await requestService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to perform bulk action
    const result = await requestService.bulkAction(body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("POST /api/request/bulk error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error instanceof Error ? error.message : "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
