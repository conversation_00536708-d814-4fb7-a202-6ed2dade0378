import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";

/**
 * POST /api/notification/actions
 * Perform bulk actions on notifications
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize notification service
    const session: any = await auth();
    
    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();
    const { action, notificationIds } = body;

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Authentication required",
          timestamp: new Date().toISOString(),
        },
        { status: 401 }
      );
    }

    let result;

    switch (action) {
      case "markAllAsRead":
        result = await notificationService.markAllAsRead(session.user.id);
        break;

      case "markAsRead":
        if (!notificationIds || !Array.isArray(notificationIds)) {
          return NextResponse.json(
            {
              success: false,
              error: true,
              message: "notificationIds array is required for markAsRead action",
              timestamp: new Date().toISOString(),
            },
            { status: 400 }
          );
        }
        
        // Mark multiple notifications as read
        const readResults = await Promise.all(
          notificationIds.map((id: string) =>
            notificationService.markAsRead(id, session.user.id)
          )
        );
        
        result = {
          success: true,
          data: readResults,
          message: `Marked ${readResults.filter(r => r.success).length} notifications as read`,
          timestamp: new Date().toISOString(),
        };
        break;

      case "markAsUnread":
        if (!notificationIds || !Array.isArray(notificationIds)) {
          return NextResponse.json(
            {
              success: false,
              error: true,
              message: "notificationIds array is required for markAsUnread action",
              timestamp: new Date().toISOString(),
            },
            { status: 400 }
          );
        }
        
        // Mark multiple notifications as unread
        const unreadResults = await Promise.all(
          notificationIds.map((id: string) =>
            notificationService.markAsUnread(id, session.user.id)
          )
        );
        
        result = {
          success: true,
          data: unreadResults,
          message: `Marked ${unreadResults.filter(r => r.success).length} notifications as unread`,
          timestamp: new Date().toISOString(),
        };
        break;

      case "delete":
        if (!notificationIds || !Array.isArray(notificationIds)) {
          return NextResponse.json(
            {
              success: false,
              error: true,
              message: "notificationIds array is required for delete action",
              timestamp: new Date().toISOString(),
            },
            { status: 400 }
          );
        }
        
        // Delete multiple notifications
        const deleteResults = await Promise.all(
          notificationIds.map((id: string) =>
            notificationService.deleteNotification(id, session.user.id)
          )
        );
        
        result = {
          success: true,
          data: deleteResults,
          message: `Deleted ${deleteResults.filter(r => r.success).length} notifications`,
          timestamp: new Date().toISOString(),
        };
        break;

      case "softDelete":
        if (!notificationIds || !Array.isArray(notificationIds)) {
          return NextResponse.json(
            {
              success: false,
              error: true,
              message: "notificationIds array is required for softDelete action",
              timestamp: new Date().toISOString(),
            },
            { status: 400 }
          );
        }
        
        // Soft delete multiple notifications
        const softDeleteResults = await Promise.all(
          notificationIds.map((id: string) =>
            notificationService.softDeleteNotification(id, session.user.id)
          )
        );
        
        result = {
          success: true,
          data: softDeleteResults,
          message: `Soft deleted ${softDeleteResults.filter(r => r.success).length} notifications`,
          timestamp: new Date().toISOString(),
        };
        break;

      case "getCounts":
        result = await notificationService.getNotificationCounts(session.user.id);
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            error: true,
            message: `Unknown action: ${action}`,
            timestamp: new Date().toISOString(),
          },
          { status: 400 }
        );
    }

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Notification actions error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
