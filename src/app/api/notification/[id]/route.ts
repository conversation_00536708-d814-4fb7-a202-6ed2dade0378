import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";

/**
 * GET /api/notification/[id]
 * Get a specific notification by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize notification service
    const session: any = await auth();

    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    // Use the service to get notification
    const result = await notificationService.getNotification(
      param.id,
      session?.user?.id
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 404,
    });
  } catch (error) {
    console.error("Notification GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/notification/[id]
 * Update a notification (mark as read, unread, delete, etc.)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize notification service
    const session: any = await auth();

    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params and request body
    let param = await params;
    const body = await request.json();

    // Use the service to update notification
    const result = await notificationService.updateNotification(
      param.id,
      body,
      session?.user?.id
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Notification PATCH error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/notification/[id]
 * Delete a notification permanently
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize notification service
    const session: any = await auth();

    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    // Use the service to delete notification
    const result = await notificationService.deleteNotification(
      param.id,
      session?.user?.id
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Notification DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
