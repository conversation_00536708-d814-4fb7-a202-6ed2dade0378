import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";

/**
 * GET /api/notification
 * Get notifications for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Initialize notification service
    const session: any = await auth();

    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);

    // Build query parameters with proper types
    const queryParams: any = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "20"),
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
    };

    // Add userId from session if authenticated
    if (session?.user?.id) {
      queryParams.userId = session.user.id;
    }

    // Add optional filters
    if (searchParams.get("type")) queryParams.type = searchParams.get("type");
    if (searchParams.get("category"))
      queryParams.category = searchParams.get("category");
    if (searchParams.get("priority"))
      queryParams.priority = searchParams.get("priority");
    if (searchParams.get("isRead"))
      queryParams.isRead = searchParams.get("isRead") === "true";
    if (searchParams.get("isDeleted"))
      queryParams.isDeleted = searchParams.get("isDeleted") === "true";

    // Use the service to get notifications
    const result = await notificationService.getNotifications(queryParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Notification GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/notification
 * Create a new notification
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize notification service
    const session: any = await auth();

    // Set the service context with session and request
    await notificationService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Add userId from session if not provided and user is authenticated
    if (!body.userId && session?.user?.id) {
      body.userId = session.user.id;
    }

    // Use the service to create notification
    const result = await notificationService.createNotification(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Notification POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
