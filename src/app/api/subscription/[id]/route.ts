import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { SubscriptionService } from "@/lib/api/services/subscription";

/**
 * Individual Subscription API Route Handler
 *
 * Handles operations for specific subscriptions using the [id] slug:
 * - GET: Get subscription by ID
 * - PUT: Update subscription by ID
 * - DELETE: Delete subscription by ID
 */

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/subscription/[id] - Get subscription by ID
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const resolvedParams = await params;

    // Use the service to get subscription by ID
    const result = await subscriptionService.getSubscriptionById(resolvedParams.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription GET by ID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/subscription/[id] - Update subscription by ID
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const resolvedParams = await params;

    // Parse request body
    const body = await request.json();

    // Use the service to update subscription
    const result = await subscriptionService.updateSubscription(resolvedParams.id, body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/subscription/[id] - Delete subscription by ID
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const resolvedParams = await params;

    // Use the service to delete subscription
    const result = await subscriptionService.deleteSubscription(resolvedParams.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST method not allowed for individual subscription operations
export async function POST(request: NextRequest, { params }: RouteParams) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST /api/subscription to create a new subscription.",
    },
    { status: 405 }
  );
}
