import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { SubscriptionService } from "@/lib/api/services/subscription";

/**
 * Subscription API Route Handler
 *
 * Handles CRUD operations for subscriptions:
 * - GET: Get all subscriptions with optional filtering and pagination
 * - POST: Create new subscription
 */

// GET /api/subscription - Get all subscriptions with optional filtering
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);

    // Build query parameters with proper types
    const queryParams: any = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
    };

    // Add optional filters
    if (searchParams.get("id")) queryParams.id = searchParams.get("id");
    if (searchParams.get("name")) queryParams.name = searchParams.get("name");
    if (searchParams.get("status")) queryParams.status = searchParams.get("status");
    if (searchParams.get("search")) queryParams.search = searchParams.get("search");
    if (searchParams.get("minPrice")) queryParams.minPrice = parseFloat(searchParams.get("minPrice")!);
    if (searchParams.get("maxPrice")) queryParams.maxPrice = parseFloat(searchParams.get("maxPrice")!);
    if (searchParams.get("minSubscribers")) queryParams.minSubscribers = parseInt(searchParams.get("minSubscribers")!);
    if (searchParams.get("maxSubscribers")) queryParams.maxSubscribers = parseInt(searchParams.get("maxSubscribers")!);
    if (searchParams.get("includeFeatures")) queryParams.includeFeatures = searchParams.get("includeFeatures") === "true";

    // Use the service to get subscriptions
    const result = await subscriptionService.getAllSubscriptions(queryParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/subscription - Create new subscription
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to create subscription
    const result = await subscriptionService.createSubscription(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT method not allowed for collection endpoint
export async function PUT(request: NextRequest) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use PUT /api/subscription/[id] to update a specific subscription.",
    },
    { status: 405 }
  );
}

// DELETE method not allowed for collection endpoint
export async function DELETE(request: NextRequest) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use DELETE /api/subscription/[id] to delete a specific subscription.",
    },
    { status: 405 }
  );
}
