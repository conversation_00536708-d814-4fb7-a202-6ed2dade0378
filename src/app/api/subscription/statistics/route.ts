import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { SubscriptionService } from "@/lib/api/services/subscription";

/**
 * GET /api/subscription/statistics
 * Get subscription statistics and analytics
 */
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get subscription statistics
    const result = await subscriptionService.getSubscriptionStatistics();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription statistics error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for statistics endpoint
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to retrieve subscription statistics.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to retrieve subscription statistics.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to retrieve subscription statistics.",
    },
    { status: 405 }
  );
}
