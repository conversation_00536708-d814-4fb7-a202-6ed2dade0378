import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { SubscriptionService } from "@/lib/api/services/subscription";

/**
 * POST /api/subscription/bulk
 * Perform bulk operations on subscriptions
 * Body should contain:
 * - action: Type of bulk operation (create, update, delete, activate, deactivate, etc.)
 * - subscriptionIds: Array of subscription IDs (for update/delete operations)
 * - subscriptions: Array of subscription data (for create operations)
 * - updates: Update data (for bulk update operations)
 * - batchSize: Optional batch size for processing (default: 50)
 */
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Set the service context with session and request
    await subscriptionService.initialize({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to perform bulk action
    const result = await subscriptionService.bulkAction(body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Subscription bulk operation error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/subscription/bulk
 * Get information about bulk operations
 */
export async function GET() {
  try {
    return NextResponse.json(
      {
        success: true,
        data: {
          message: "Bulk operations endpoint is available",
          methods: {
            POST: "Perform bulk operations on subscriptions",
            GET: "Get bulk operations info (current endpoint)",
          },
          supportedActions: [
            "create",
            "update", 
            "delete",
            "activate",
            "deactivate",
            "updatePrice",
            "updateSubscribers",
          ],
          bodyFormat: {
            action: "Type of bulk operation",
            subscriptionIds: "Array of subscription IDs (for update/delete operations)",
            subscriptions: "Array of subscription objects (for create operations)",
            updates: "Update data object (for bulk update operations)",
            batchSize: "Optional batch size for processing (default: 50)",
          },
          examples: {
            bulkCreate: {
              action: "create",
              subscriptions: [
                {
                  name: "Basic Plan",
                  description: "Basic subscription plan",
                  price: 9.99,
                  features: ["feature1", "feature2"],
                  status: "active",
                },
                {
                  name: "Premium Plan", 
                  description: "Premium subscription plan",
                  price: 19.99,
                  features: ["feature1", "feature2", "feature3"],
                  status: "active",
                },
              ],
              batchSize: 25,
            },
            bulkUpdate: {
              action: "update",
              subscriptionIds: ["subscription-id-1", "subscription-id-2"],
              updates: {
                status: "active",
                price: 15.99,
              },
            },
            bulkActivate: {
              action: "activate",
              subscriptionIds: ["subscription-id-1", "subscription-id-2"],
            },
            bulkDelete: {
              action: "delete",
              subscriptionIds: ["subscription-id-1", "subscription-id-2"],
            },
          },
        },
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Subscription bulk info error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for bulk endpoint
export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST for bulk operations or GET for info.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST for bulk operations or GET for info.",
    },
    { status: 405 }
  );
}
