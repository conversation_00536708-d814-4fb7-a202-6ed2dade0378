"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ContractService } from "@/lib/api/services/contract";

export async function GET(request: NextRequest) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract search parameters from URL
    const { searchParams } = new URL(request.url);

    // Build simplified search parameters object
    const searchFilters = {
      query: searchParams.get("query") || undefined,
      page: searchParams.get("page") ? Number(searchParams.get("page")) : 1,
      limit: searchParams.get("limit") ? Number(searchParams.get("limit")) : 10,
    };

    // Remove undefined values to clean up the search parameters
    const cleanSearchParams = Object.fromEntries(
      Object.entries(searchFilters).filter(([_, value]) => value !== undefined)
    );

    // Use the service to search contracts
    const result = await contractService.searchContracts(cleanSearchParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contract search error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
