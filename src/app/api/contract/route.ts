"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ContractService } from "@/lib/api/services/contract";

export async function GET(request: NextRequest) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get contracts
    const result = await contractService.getContracts();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contracts GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Check if request includes attachments
    let { searchParams } = new URL(request.url);
    const withAttachments = searchParams.get("withAttachments") === "true";

    let contractData: any;

    if (withAttachments) {
      // Parse form data
      let formData = await request.formData();
      contractData = Object.fromEntries((formData as any).entries());
    } else {
      // Parse request body
      contractData = await request.json();
    }

    // Use the service to create contract
    const result = await contractService.createContract(contractData);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contract POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
