"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { ContractService } from "@/lib/api/services/contract";

export async function GET(request: NextRequest) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Get contract statistics directly from the service
    const result = await contractService.getContractStatistics();

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      throw new Error(result.message || "Failed to get contract statistics");
    }
  } catch (error) {
    console.error("Contract statistics error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
