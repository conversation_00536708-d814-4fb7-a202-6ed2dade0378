"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { PaymentMethodService } from "@/lib/api/services/payment";

// POST /api/payment/[id]/default - Set payment method as default
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    if (!param.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to set payment method as default
    const result = await paymentService.setDefaultPaymentMethod(param.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Set default payment method error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
