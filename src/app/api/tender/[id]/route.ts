import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderService } from "@/lib/api/services/tenders/nest";

/**
 * Tender ID-specific API Route Handler
 *
 * Handles operations for specific tenders:
 * - GET: Get tender by ID
 * - PUT: Update tender by ID
 * - DELETE: Delete tender by ID
 */

/**
 * GET /api/tender/[id]
 * Get a specific tender by ID
 * Query parameters:
 * - autoSeed: Enable/disable automatic data seeding if tender not found (default: true)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Tender ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Parse query parameters for autoSeed option
    const { searchParams } = new URL(request.url);
    const autoSeed = searchParams.get("autoSeed") !== "false"; // Default to true

    // Use the service to get tender by ID
    const result = await tenderService.getTenderById(id, autoSeed);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender GET by ID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tender/[id]
 * Update a specific tender by ID
 * Body should contain tender data matching UpdateNestTenderSchema
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params and parse request body
    const { id } = await params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Tender ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Add ID to the body for validation
    const updateData = { ...body, id };

    // Use the service to update tender
    const result = await tenderService.updateTender(updateData);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender PUT by ID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tender/[id]
 * Delete a specific tender by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Tender ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to delete tender
    const result = await tenderService.deleteTender(id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender DELETE by ID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
