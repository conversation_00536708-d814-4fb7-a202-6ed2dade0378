import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderService } from "@/lib/api/services/tenders/nest";

/**
 * Tender API Route Handler
 *
 * Handles CRUD operations for tenders:
 * - GET: Get all tenders with filtering, pagination, and search
 * - POST: Create new tender or upsert tender
 */

/**
 * GET /api/tender
 * Get all tenders with optional filtering, pagination, and search
 * Query parameters:
 * - id: Filter by specific tender ID
 * - ocid: Filter by OCID (partial match)
 * - language: Filter by language
 * - initiation_type: Filter by initiation type
 * - procuring_entity: Filter by procuring entity (partial match)
 * - procuring_method: Filter by procurement method
 * - category: Filter by category (partial match)
 * - status: Filter by status
 * - search: Search across multiple fields (ocid, description, procuring_entity, category)
 * - page: Page number for pagination (default: 1)
 * - limit: Number of items per page (default: 20, max: 100)
 * - sortBy: Sort field (createdAt, updatedAt, ocid, description)
 * - sortOrder: Sort order (asc, desc)
 * - autoSeed: Enable/disable automatic data seeding (default: true)
 */
export async function GET(request: NextRequest) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const rawParams = Object.fromEntries(searchParams.entries());

    // Convert and validate parameters
    const queryParams: any = { ...rawParams };

    // Convert numeric parameters
    if (rawParams.page) {
      queryParams.page = parseInt(rawParams.page, 10);
    }
    if (rawParams.limit) {
      queryParams.limit = parseInt(rawParams.limit, 10);
    }

    // Convert boolean parameters
    if (rawParams.autoSeed !== undefined) {
      queryParams.autoSeed = rawParams.autoSeed === "true";
    }

    // Use the service to get tenders
    const result = await tenderService.getTenders(queryParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tender
 * Create a new tender
 * Body should contain tender data matching NestTenderSchema
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to create tender
    const result = await tenderService.createTender(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tender
 * Upsert tender (create or update based on OCID)
 * Body should contain tender data matching NestTenderSchema
 */
export async function PUT(request: NextRequest) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to upsert tender
    const result = await tenderService.upsertTender(body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
