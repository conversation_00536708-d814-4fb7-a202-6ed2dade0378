import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderSeederService } from "@/lib/api/services/tenders/nest";

/**
 * Tender Bulk Operations API Route Handler
 *
 * Handles bulk operations for tenders:
 * - POST: Bulk upsert tenders
 */

/**
 * POST /api/tender/bulk
 * Bulk upsert tenders (create or update multiple tenders)
 * Body should contain:
 * - tenders: Array of tender data matching NestTenderSchema
 * - batchSize: Optional batch size for processing (default: 50)
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize tender seeder service (which has bulk operations)
    const session: any = await auth();
    const seederService = new NestTenderSeederService();

    // Set the service context with session and request
    seederService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    if (!body.tenders || !Array.isArray(body.tenders)) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Request body must contain 'tenders' array",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    if (body.tenders.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Tenders array cannot be empty",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Extract batch size from body or use default
    const batchSize = body.batchSize || 50;

    // Use the service to bulk upsert tenders
    const result = await seederService.bulkUpsertTenders(
      body.tenders,
      batchSize
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender bulk upsert error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/tender/bulk
 * Get information about bulk operations
 */
export async function GET() {
  try {
    return NextResponse.json(
      {
        success: true,
        data: {
          message: "Bulk operations endpoint is available",
          methods: {
            POST: "Bulk upsert tenders",
            GET: "Get bulk operations info (current endpoint)",
          },
          bodyFormat: {
            tenders: "Array of tender objects matching NestTenderSchema",
            batchSize: "Optional batch size for processing (default: 50)",
          },
          example: {
            tenders: [
              {
                ocid: "example-ocid-1",
                language: "en",
                description: "Example tender description",
                status: "active",
              },
            ],
            batchSize: 25,
          },
        },
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Tender bulk info error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for bulk endpoint
export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message:
        "Method not allowed. Use POST for bulk operations or GET for info.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message:
        "Method not allowed. Use POST for bulk operations or GET for info.",
    },
    { status: 405 }
  );
}
