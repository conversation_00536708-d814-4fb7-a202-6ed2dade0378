import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderSeederService } from "@/lib/api/services/tenders/nest";

/**
 * Tender Seeding API Route Handler
 *
 * Handles manual seeding operations for tenders:
 * - POST: Trigger manual seeding from external API
 */

/**
 * POST /api/tender/seed
 * Manually trigger seeding of tender data from external API
 * Body (optional):
 * - skipCronCheck: Skip the 12-hour interval check (default: false)
 * - useBulkSeeding: Use bulk seeding from external API (default: true)
 * - fallbackToSingleRecords: Fallback to single record processing (default: true)
 * - singleRecordData: Array of tender data for single record seeding
 * - batchSize: Batch size for processing (default: 50)
 * - maxRetries: Maximum retries for failed operations (default: 3)
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize tender seeder service
    const session: any = await auth();
    const seederService = new NestTenderSeederService();

    // Set the service context with session and request
    seederService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body for seeding options
    let seedingOptions = {};
    try {
      const body = await request.json();
      seedingOptions = body || {};
    } catch (error) {
      // If no body or invalid JSON, use default options
      seedingOptions = {};
    }

    // Use the unified seeding method
    const result = await seederService.seedTenders(seedingOptions);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender seeding error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/tender/seed
 * Get information about the seeding process and last run status
 */
export async function GET() {
  try {
    // Initialize tender seeder service
    const session: any = await auth();
    const seederService = new NestTenderSeederService();

    // Set the service context with session and request
    seederService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // For now, return basic information about seeding
    // In the future, this could return cron log information, last run status, etc.
    return NextResponse.json(
      {
        success: true,
        data: {
          message: "Seeding endpoint is available",
          methods: {
            POST: "Trigger manual seeding",
            GET: "Get seeding status (current endpoint)",
          },
          options: {
            skipCronCheck: "Skip the 12-hour interval check (default: false)",
            useBulkSeeding:
              "Use bulk seeding from external API (default: true)",
            fallbackToSingleRecords:
              "Fallback to single record processing (default: true)",
            singleRecordData: "Array of tender data for single record seeding",
            batchSize: "Batch size for processing (default: 50)",
            maxRetries: "Maximum retries for failed operations (default: 3)",
          },
        },
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Tender seeding status error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for seeding endpoint
export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message:
        "Method not allowed. Use POST to trigger seeding or GET for status.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message:
        "Method not allowed. Use POST to trigger seeding or GET for status.",
    },
    { status: 405 }
  );
}
