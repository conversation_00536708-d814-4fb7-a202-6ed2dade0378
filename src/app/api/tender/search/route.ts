import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderService } from "@/lib/api/services/tenders/nest";

/**
 * Tender Search API Route Handler
 *
 * Handles search operations for tenders:
 * - GET: Search tenders with advanced filtering
 */

/**
 * GET /api/tender/search
 * Search tenders with advanced filtering and pagination
 * Query parameters:
 * - query: Search term (searches across ocid, description, procuring_entity, category)
 * - ocid: Filter by OCID (partial match)
 * - language: Filter by language
 * - initiation_type: Filter by initiation type
 * - procuring_entity: Filter by procuring entity (partial match)
 * - procuring_method: Filter by procurement method
 * - category: Filter by category (partial match)
 * - status: Filter by status
 * - page: Page number for pagination (default: 1)
 * - limit: Number of items per page (default: 20, max: 100)
 * - sortBy: Sort field (createdAt, updatedAt, ocid, description)
 * - sortOrder: Sort order (asc, desc)
 * - autoSeed: Enable/disable automatic data seeding (default: false for search)
 */
export async function GET(request: NextRequest) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract search parameters from URL
    const { searchParams } = new URL(request.url);

    // Build search parameters object
    const searchFilters: any = {
      search: searchParams.get("query") || searchParams.get("search") || undefined,
      ocid: searchParams.get("ocid") || undefined,
      language: searchParams.get("language") || undefined,
      initiation_type: searchParams.get("initiation_type") || undefined,
      procuring_entity: searchParams.get("procuring_entity") || undefined,
      procuring_method: searchParams.get("procuring_method") || undefined,
      category: searchParams.get("category") || undefined,
      status: searchParams.get("status") || undefined,
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!, 10) : 1,
      limit: searchParams.get("limit") ? parseInt(searchParams.get("limit")!, 10) : 20,
      sortBy: searchParams.get("sortBy") || undefined,
      sortOrder: searchParams.get("sortOrder") || undefined,
      autoSeed: searchParams.get("autoSeed") === "true", // Default to false for search
    };

    // Remove undefined values to clean up the search parameters
    const cleanSearchParams = Object.fromEntries(
      Object.entries(searchFilters).filter(([_, value]) => value !== undefined)
    );

    // Use the service to get tenders with search parameters
    const result = await tenderService.getTenders(cleanSearchParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender search error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for search endpoint
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to search tenders.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to search tenders.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use GET to search tenders.",
    },
    { status: 405 }
  );
}
