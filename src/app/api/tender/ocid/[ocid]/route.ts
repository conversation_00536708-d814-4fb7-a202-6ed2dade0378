import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { NestTenderService } from "@/lib/api/services/tenders/nest";

/**
 * Tender OCID-specific API Route Handler
 *
 * Handles operations for tenders by OCID:
 * - GET: Get tender by OCID
 */

/**
 * GET /api/tender/ocid/[ocid]
 * Get a specific tender by OCID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ocid: string }> }
) {
  try {
    // Initialize tender service
    const session: any = await auth();
    const tenderService = new NestTenderService();

    // Set the service context with session and request
    tenderService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    const { ocid } = await params;

    if (!ocid) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Tender OCID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to get tender by OCID
    const result = await tenderService.getTenderByOcid(decodeURIComponent(ocid));

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Tender GET by OCID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for OCID lookup
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST /api/tender to create a tender.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use PUT /api/tender/[id] to update a tender.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use DELETE /api/tender/[id] to delete a tender.",
    },
    { status: 405 }
  );
}
