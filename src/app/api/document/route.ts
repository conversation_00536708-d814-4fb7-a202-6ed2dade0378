"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(request: NextRequest) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract query parameters from URL
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Check if requesting a specific document by ID
    if (queryParams.id) {
      const result = await documentService.getDocument(queryParams.id);
      return NextResponse.json(result, { status: result.success ? 200 : 404 });
    }

    // Use the service to get all documents
    const result = await documentService.getDocuments();

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Documents GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const location = formData.get("location") as string;

    // Extract metadata from form data
    const metadata = {
      status: formData.get("status") as string,
      category: formData.get("category") as string,
      association_entity: formData.get("association_entity") as string,
      association_id: formData.get("association_id") as string,
      proposalId: formData.get("proposalId") as string,
    };

    // Use the service to upload document
    const result = await documentService.uploadDocument(
      file,
      location,
      metadata
    );

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Document upload error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
