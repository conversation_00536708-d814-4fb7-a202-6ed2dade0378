import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    // Check if requesting a specific document by ID
    if (!param.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    const result = await documentService.getDocument(param.id);
    return NextResponse.json(result, { status: result.success ? 200 : 404 });
  } catch (error) {
    console.error("Documents GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract params and request body
    let param = await params;
    const body = await request.json();

    if (!param.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Use the service to update document
    const result = await documentService.updateDocument(param.id, body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Document update error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract params
    let param = await params;

    if (!param.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Use the service to delete document
    const result = await documentService.deleteDocument(param.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Document delete error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
