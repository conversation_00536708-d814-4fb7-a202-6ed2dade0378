import { NextRequest, NextResponse } from "next/server";
import { DocumentService } from "@/lib/api/services";

export async function POST(request: NextRequest) {
  try {
    // Initialize document service
    const documentService = new DocumentService();

    // Parse request body
    const body = await request.json();
    const formData = await request.formData();

    // Extract files from form data
    const files = Array.from(formData.getAll("files")) as File[];

    // Use the service to bulk create documents
    const result = await documentService.bulkCreateDocuments(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Documents BULK CREATE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
