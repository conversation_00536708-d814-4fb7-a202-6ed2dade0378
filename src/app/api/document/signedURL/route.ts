import { NextRequest, NextResponse } from "next/server";
import { getSignedURL } from "@/lib/common/s3";
import { auth } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    // Initialize document service
    const session: any = await auth();

    let body = await request.json();

    const result = await getSignedURL(body.path);

    return NextResponse.json(
      {
        success: true,
        data: result,
        message: "Signed URL generated successfully",
        timestamp: new Date().toISOString(),
      },
      { status: result ? 200 : 404 }
    );
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
