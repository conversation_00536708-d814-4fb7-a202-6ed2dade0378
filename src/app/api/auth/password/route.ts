import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { AuthService } from "@/lib/api/services/auth";
import { z } from "zod";

/**
 * Password Update API Route Handler
 *
 * POST /api/auth/password - Update user password
 */

// Validation schema for password update
const UpdatePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(
      /[!@#$%^&*(),.?":{}|<>]/,
      "Password must contain at least one special character"
    ),
});

export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request data
    const validationResult = UpdatePasswordSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = validationResult.error.issues.map(
        (err: any) => err.message
      );
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Validation failed",
          details: errors,
        },
        { status: 400 }
      );
    }

    const { currentPassword, newPassword } = validationResult.data;

    // Check if new password is different from current
    if (currentPassword === newPassword) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "New password must be different from current password",
        },
        { status: 400 }
      );
    }

    const authService = new AuthService();
    authService.setContext({ session, user: session.user, request });

    const result = await authService.updatePassword(
      session.user.id,
      currentPassword,
      newPassword
    );

    if (result.success) {
      return NextResponse.json(
        {
          success: true,
          error: false,
          message: "Password updated successfully",
          data: null,
        },
        { status: 200 }
      );
    } else {
      return NextResponse.json(result, { status: result.statusCode || 400 });
    }
  } catch (error) {
    console.error("Password update error:", error);

    // Handle specific error messages
    let errorMessage = "Failed to update password";
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message === "Current password is incorrect") {
        errorMessage = error.message;
        statusCode = 400;
      } else if (
        error.message === "User not found" ||
        error.message === "User account not found"
      ) {
        errorMessage = error.message;
        statusCode = 404;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: errorMessage,
      },
      { status: statusCode }
    );
  }
}

// Other methods not allowed
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to update password.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to update password.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to update password.",
    },
    { status: 405 }
  );
}
