import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { AuthService } from "@/lib/api/services/auth";

/**
 * PUT /api/auth/user
 * Update user details (firstName, lastName, email, phone)
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session: any = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Unauthorized - Please sign in",
          statusCode: 401,
          timestamp: new Date().toISOString(),
        },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { firstName, lastName, email, phone } = body;

    // Validate that at least one field is provided
    if (!firstName && !lastName && !email && !phone) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message:
            "At least one field (firstName, lastName, email, phone) must be provided",
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Initialize auth service
    const authService = new AuthService();

    // Prepare update data - only include provided fields
    const updateData: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
    } = {};

    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;

    // Update user details
    const result = await authService.updateUser(session.user.id, updateData);

    return NextResponse.json(result, { status: result.statusCode });
  } catch (error: any) {
    console.error("User update API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error?.message || "Internal server error",
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
