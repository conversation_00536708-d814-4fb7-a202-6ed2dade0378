import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/api/services/auth";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const authService = new AuthService();

    const result = await authService.register(
      body.firstName,
      body.lastName,
      body.phone,
      body.email,
      body.password
    );

    return NextResponse.json(result, { status: result.statusCode });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : "Internal server error";
    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
        error: errorMessage,
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
      }
    );
  }
}
