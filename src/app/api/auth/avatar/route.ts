import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/api/services/auth";

export async function POST(request: NextRequest) {
  try {
    let authService = new AuthService();

    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { success: false, error: "User ID is required" },
        { status: 400 }
      );
    }

    let formData = await request.formData();
    let avatar: any = formData.get("avatar");
    let result = await authService.updateAvatar(id, avatar);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error.message || "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
