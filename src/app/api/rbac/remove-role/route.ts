import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RBACService } from "@/lib/api/services/rbac";

/**
 * POST /api/rbac/remove-role
 * Remove role from a user
 */
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Create RBAC service instance with context
    const rbac = new RBACService();
    rbac.setContext({
      session,
      user: session.user,
      request,
    });

    // Use service to remove role
    const result = await rbac.removeRole(body);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: "Role removed successfully",
        timestamp: result.timestamp,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: result.message || "Failed to remove role",
          errors: result.errors,
          timestamp: result.timestamp,
        },
        { status: result.statusCode || 500 }
      );
    }
  } catch (error) {
    console.error("Role removal error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
