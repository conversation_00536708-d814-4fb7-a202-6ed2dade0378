"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RBACService } from "@/lib/api/services/rbac";

/**
 * GET /api/rbac/permissions
 * Get available permissions in the system
 */
export async function GET(request: NextRequest) {
  try {
    // Get session for authentication
    const session: any = await auth();

    // Initialize RBAC service
    const rbac = new RBACService({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: false,
    });

    // Set the service context with session and request
    rbac.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get available permissions
    const result = await rbac.getAvailablePermissions();

    // Return standardized response format
    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("RBAC permissions GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
