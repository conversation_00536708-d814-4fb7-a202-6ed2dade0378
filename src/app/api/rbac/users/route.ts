import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RBACService } from "@/lib/api/services/rbac";

export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Create RBAC service instance with context
    const rbac = new RBACService();
    rbac.setContext({
      session,
      user: session.user,
      request,
    });

    // Parse query parameters from URL
    const { searchParams } = new URL(request.url);
    const query = Object.fromEntries(searchParams.entries());

    // Use service to get users
    const result = await rbac.getUsers(query);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(error, { status: 500 });
  }
}
