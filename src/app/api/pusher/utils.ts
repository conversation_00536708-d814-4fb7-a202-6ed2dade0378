import { NextRequest } from "next/server";

/**
 * Extract user ID from Authorization header
 * @param request - NextRequest object
 * @returns userId string or null if not found/invalid
 */
export function extractUserIdFromAuth(request: NextRequest): string | null {
  const authHeader = request.headers.get("Authorization");

  console.log("🔍 [Auth Extract] Header:", authHeader);

  if (!authHeader) {
    console.log("🔍 [Auth Extract] No auth header found");
    return null;
  }

  // Check if it's a Bearer token
  if (!authHeader.startsWith("Bearer ")) {
    console.log("🔍 [Auth Extract] Not a Bearer token");
    return null;
  }

  const userId = authHeader.replace("Bearer ", "").trim();

  // Basic validation - ensure it's not empty
  if (!userId) {
    console.log("🔍 [Auth Extract] Empty userId after extraction");
    return null;
  }

  console.log("🔍 [Auth Extract] Extracted userId:", userId);
  return userId;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use extractUserIdFromAuth instead
 */
export function getPusherHeaders(request: NextRequest) {
  const userId = extractUserIdFromAuth(request);
  return {
    userId,
  };
}

/**
 * Recursively check if a value is null, undefined, or empty
 * @param value - Value to check
 * @param allowEmptyStrings - Whether to allow empty strings (default: false)
 * @returns boolean indicating if value is valid
 */
function isValidValue(value: any, allowEmptyStrings: boolean = false): boolean {
  if (value === null || value === undefined) {
    return false;
  }

  if (typeof value === "string") {
    return allowEmptyStrings || value.trim().length > 0;
  }

  if (Array.isArray(value)) {
    return (
      value.length > 0 &&
      value.every((item) => isValidValue(item, allowEmptyStrings))
    );
  }

  if (typeof value === "object") {
    const keys = Object.keys(value);
    return (
      keys.length > 0 &&
      keys.every((key) => isValidValue(value[key], allowEmptyStrings))
    );
  }

  return true;
}

/**
 * Recursively validate object properties
 * @param obj - Object to validate
 * @param requiredProps - Array of required property paths (supports nested like 'user.id')
 * @param allowEmptyStrings - Whether to allow empty strings
 * @returns boolean indicating if all required properties are valid
 */
function validateObjectRecursively(
  obj: any,
  requiredProps: string[],
  allowEmptyStrings: boolean = false
): boolean {
  if (!obj || typeof obj !== "object") {
    return false;
  }

  for (const prop of requiredProps) {
    const propPath = prop.split(".");
    let current = obj;

    // Navigate through nested properties
    for (const key of propPath) {
      if (!current || typeof current !== "object" || !(key in current)) {
        return false;
      }
      current = current[key];
    }

    // Check if the final value is valid
    if (!isValidValue(current, allowEmptyStrings)) {
      return false;
    }
  }

  return true;
}

/**
 * Extract and validate required parameters from request with recursive null checks
 * @param request - NextRequest object
 * @param requiredParams - Array of required parameter names (supports nested like 'data.userId')
 * @param allowEmptyStrings - Whether to allow empty strings (default: false)
 * @returns Object with extracted parameters or null if validation fails
 */
export async function extractAndValidateParams(
  request: NextRequest,
  requiredParams: string[],
  allowEmptyStrings: boolean = false
): Promise<{ userId: string; [key: string]: any } | null> {
  try {
    // Extract user ID from auth header
    const userId = extractUserIdFromAuth(request);
    if (!userId) {
      return null;
    }

    // Parse request body
    const body = await request.json();

    // Validate that body is an object
    if (!body || typeof body !== "object") {
      return null;
    }

    // Check if all required parameters are present and valid using recursive validation
    if (!validateObjectRecursively(body, requiredParams, allowEmptyStrings)) {
      return null;
    }

    // Additional validation for common nested structures
    const result = {
      userId,
      ...body,
    };

    // Recursively validate the entire result object
    if (!isValidValue(result, allowEmptyStrings)) {
      return null;
    }

    return result;
  } catch (error) {
    // JSON parsing error or other issues
    console.error("Parameter extraction error:", error);
    return null;
  }
}

/**
 * Create standardized error response
 * @param message - Error message
 * @param status - HTTP status code
 * @returns Response object
 */
export function createErrorResponse(message: string, status: number = 400) {
  return Response.json({ error: message }, { status });
}

/**
 * Create standardized success response
 * @param data - Response data (optional)
 * @param status - HTTP status code
 * @returns Response object
 */
export function createSuccessResponse(data?: any, status: number = 200) {
  return Response.json({ success: true, ...data }, { status });
}

/**
 * Validate specific parameter types with recursive null checks
 * @param params - Parameters object to validate
 * @param validationRules - Object defining validation rules for each parameter
 * @returns boolean indicating if all parameters are valid
 */
export function validateParameterTypes(
  params: any,
  validationRules: Record<
    string,
    {
      type: "string" | "number" | "boolean" | "object" | "array";
      required?: boolean;
      allowEmpty?: boolean;
      minLength?: number;
      maxLength?: number;
      nested?: Record<string, any>;
    }
  >
): boolean {
  if (!params || typeof params !== "object") {
    return false;
  }

  for (const [key, rules] of Object.entries(validationRules)) {
    const value = params[key];

    // Check if required parameter is missing
    if (rules.required && !isValidValue(value, rules.allowEmpty)) {
      return false;
    }

    // Skip validation if parameter is not required and not present
    if (!rules.required && (value === undefined || value === null)) {
      continue;
    }

    // Type validation
    if (rules.type === "string" && typeof value !== "string") {
      return false;
    }
    if (rules.type === "number" && typeof value !== "number") {
      return false;
    }
    if (rules.type === "boolean" && typeof value !== "boolean") {
      return false;
    }
    if (rules.type === "array" && !Array.isArray(value)) {
      return false;
    }
    if (
      rules.type === "object" &&
      (typeof value !== "object" || Array.isArray(value))
    ) {
      return false;
    }

    // String length validation
    if (rules.type === "string" && typeof value === "string") {
      if (rules.minLength && value.length < rules.minLength) {
        return false;
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        return false;
      }
    }

    // Nested object validation
    if (rules.type === "object" && rules.nested && typeof value === "object") {
      if (!validateParameterTypes(value, rules.nested)) {
        return false;
      }
    }

    // Array element validation
    if (rules.type === "array" && Array.isArray(value)) {
      if (!value.every((item) => isValidValue(item, rules.allowEmpty))) {
        return false;
      }
    }
  }

  return true;
}

/**
 * Validate user authorization for specific actions with null checks
 * @param requestUserId - User ID from request
 * @param targetUserId - Target user ID for the action
 * @param allowSelfOnly - Whether the action is only allowed for the user themselves
 * @returns boolean indicating if the action is authorized
 */
export function validateUserAuthorization(
  requestUserId: string,
  targetUserId: string,
  allowSelfOnly: boolean = true
): boolean {
  // Null checks for user IDs
  if (!isValidValue(requestUserId) || !isValidValue(targetUserId)) {
    return false;
  }

  if (allowSelfOnly) {
    return requestUserId === targetUserId;
  }

  // For now, we'll allow any authenticated user
  // In a real implementation, you might check admin roles here
  return true;
}
