import { NextRequest } from "next/server";
import {
  extractAndValidateParams,
  validateParameterTypes,
  createErrorResponse,
  createSuccessResponse,
} from "../utils";
import { getPusherServer } from "@/lib/socket/server";

export async function POST(request: NextRequest) {
  try {
    const params = await extractAndValidateParams(request, [
      "action",
      "contextType",
      "contextId",
    ]);

    if (!params) {
      return createErrorResponse("Missing required parameters", 400);
    }

    // Additional recursive validation with type checking
    const validationRules = {
      action: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 50,
      },
      contextType: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 100,
      },
      contextId: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 255,
      },
    };

    if (!validateParameterTypes(params, validationRules)) {
      return createErrorResponse("Invalid parameter types or values", 400);
    }

    const { userId, action, contextType, contextId } = params;

    // Validate action is one of the allowed values
    if (!["join", "leave"].includes(action)) {
      return createErrorResponse(
        "Invalid action. Must be 'join' or 'leave'",
        400
      );
    }

    // Get Pusher server instance
    const pusher = getPusherServer();
    if (!pusher) {
      return createErrorResponse("Pusher server not available", 500);
    }

    const channelName = `private-${contextType}-${contextId}`;

    if (action === "join") {
      // Trigger join event to the channel
      await pusher.trigger(channelName, "joined_context", {
        contextType,
        contextId,
        room: channelName,
        userId,
        timestamp: new Date().toISOString(),
      });

      console.log(`User ${userId} joined context ${contextType}:${contextId}`);
    } else if (action === "leave") {
      // Trigger leave event to the channel
      await pusher.trigger(channelName, "left_context", {
        contextType,
        contextId,
        room: channelName,
        userId,
        timestamp: new Date().toISOString(),
      });

      console.log(`User ${userId} left context ${contextType}:${contextId}`);
    } else {
      return createErrorResponse("Invalid action", 400);
    }

    return createSuccessResponse();
  } catch (error) {
    console.error("Pusher context error:", error);
    return createErrorResponse("Context action failed", 500);
  }
}
