import { NextRequest } from "next/server";
import {
  extractAndValidateParams,
  validateParameterTypes,
  createErrorResponse,
  createSuccessResponse,
  validateUserAuthorization,
} from "../utils";
import { getPusherServer } from "@/lib/socket/server";

export async function POST(request: NextRequest) {
  try {
    const params = await extractAndValidateParams(request, ["userId"]);

    if (!params) {
      return createErrorResponse("Missing required parameters", 400);
    }

    // Additional validation with type checking
    const validationRules = {
      userId: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 255,
      },
    };

    if (!validateParameterTypes(params, validationRules)) {
      return createErrorResponse("Invalid parameter types or values", 400);
    }

    const { userId } = params;

    // The userId from the header and body are already validated to match by extractAndValidateParams
    if (!validateUserAuthorization(userId, userId, true)) {
      return createErrorResponse("Unauthorized", 401);
    }

    // Get Pusher server instance
    const pusher = getPusherServer();
    if (!pusher) {
      return createErrorResponse("Pusher server not available", 500);
    }

    // In a real implementation, you'd verify the user session here
    // For now, we'll just send a success response

    // Trigger authentication success event
    await pusher.trigger(`private-user-${userId}`, "authenticated", {
      success: true,
      userId,
      timestamp: new Date().toISOString(),
    });

    console.log(`User ${userId} authenticated successfully`);

    return createSuccessResponse();
  } catch (error) {
    console.error("Authentication error:", error);

    // Trigger authentication error event if possible
    const authHeader = request.headers.get("Authorization");
    const userId = authHeader?.replace("Bearer ", "");

    if (userId) {
      const pusher = getPusherServer();
      if (pusher) {
        try {
          await pusher.trigger(
            `private-user-${userId}`,
            "authentication_error",
            {
              error: "Authentication failed",
              timestamp: new Date().toISOString(),
            }
          );
        } catch (triggerError) {
          console.error(
            "Failed to trigger authentication error:",
            triggerError
          );
        }
      }
    }

    return createErrorResponse("Authentication failed", 500);
  }
}
