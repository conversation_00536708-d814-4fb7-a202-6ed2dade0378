import { NextRequest } from "next/server";
import {
  extractAndValidateParams,
  validateParameterTypes,
  createErrorResponse,
  createSuccessResponse,
} from "../utils";
import { getPusherServer } from "@/lib/socket/server";

export async function POST(request: NextRequest) {
  try {
    const params = await extractAndValidateParams(request, ["action"]);

    if (!params) {
      return createErrorResponse("Missing required parameters", 400);
    }

    // Basic validation for action parameter
    const basicValidationRules = {
      action: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 100,
      },
    };

    if (!validateParameterTypes(params, basicValidationRules)) {
      return createErrorResponse("Invalid action parameter", 400);
    }

    const { userId, action, data } = params;

    // Validate action is one of the allowed values
    const allowedActions = [
      "updateUserState",
      "requestRoomMemberStates",
      "userActivity",
      "forceUserOffline",
    ];

    if (!allowedActions.includes(action)) {
      return createErrorResponse(
        `Invalid action. Must be one of: ${allowedActions.join(", ")}`,
        400
      );
    }

    // Get Pusher server instance
    const pusher = getPusherServer();
    if (!pusher) {
      return createErrorResponse("Pusher server not available", 500);
    }

    const timestamp = new Date().toISOString();

    // Validate data parameter based on action type
    switch (action) {
      case "updateUserState":
        {
          // Validate data structure for updateUserState
          const updateUserStateRules = {
            userId: {
              type: "string" as const,
              required: true,
              minLength: 1,
            },
            state: {
              type: "string" as const,
              required: true,
              minLength: 1,
              maxLength: 50,
            },
            roomId: {
              type: "string" as const,
              required: false,
              minLength: 1,
            },
            userName: {
              type: "string" as const,
              required: false,
              minLength: 1,
              maxLength: 255,
            },
          };

          if (!data || !validateParameterTypes(data, updateUserStateRules)) {
            return createErrorResponse(
              "Invalid data for updateUserState action",
              400
            );
          }

          const { userId: targetUserId, state, roomId, userName } = data;

          // Validate state is one of the allowed values
          const allowedStates = ["online", "offline", "typing", "away", "busy"];
          if (!allowedStates.includes(state)) {
            return createErrorResponse(
              `Invalid state. Must be one of: ${allowedStates.join(", ")}`,
              400
            );
          }

          // Validate that user can only update their own state
          if (userId !== targetUserId) {
            return createErrorResponse(
              "Unauthorized: Can only update own state",
              403
            );
          }

          // Broadcast user state to relevant contexts
          if (roomId) {
            // Broadcast to room members
            const roomChannel = `private-chat-${roomId}`;
            await pusher.trigger(roomChannel, "user_state_changed", {
              userId: targetUserId,
              userName,
              state,
              roomId,
              timestamp,
            });
          } else {
            // Broadcast globally (you might want to limit this to user's contacts)
            await pusher.trigger(
              `private-user-${targetUserId}`,
              "user_state_changed",
              {
                userId: targetUserId,
                userName,
                state,
                timestamp,
              }
            );
          }

          // Acknowledge the state update
          await pusher.trigger(
            `private-user-${targetUserId}`,
            "user_state_updated",
            {
              success: true,
              userId: targetUserId,
              state,
              roomId,
              timestamp,
            }
          );
        }
        break;

      case "requestRoomMemberStates":
        {
          // Validate data structure for requestRoomMemberStates
          const requestRoomMemberStatesRules = {
            roomId: {
              type: "string" as const,
              required: true,
              minLength: 1,
              maxLength: 255,
            },
          };

          if (
            !data ||
            !validateParameterTypes(data, requestRoomMemberStatesRules)
          ) {
            return createErrorResponse(
              "Invalid data for requestRoomMemberStates action",
              400
            );
          }

          const { roomId } = data;

          // In a real implementation, you'd fetch actual room member states from database
          // For now, we'll send a mock response
          const memberStates = {
            [userId]: "online", // Current user is online
            // Add other room members here
          };

          await pusher.trigger(`private-user-${userId}`, "bulk_user_states", {
            roomId,
            states: memberStates,
            timestamp,
          });
        }
        break;

      case "userActivity":
        {
          // Validate data structure for userActivity
          const userActivityRules = {
            roomId: {
              type: "string" as const,
              required: false,
              minLength: 1,
              maxLength: 255,
            },
          };

          if (data && !validateParameterTypes(data, userActivityRules)) {
            return createErrorResponse(
              "Invalid data for userActivity action",
              400
            );
          }

          const { roomId } = data || {};

          const eventData = {
            userId,
            roomId,
            timestamp,
          };

          if (roomId) {
            await pusher.trigger(
              `private-chat-${roomId}`,
              "user_activity_detected",
              eventData
            );
          } else {
            // Broadcast to user's contacts or globally
            await pusher.trigger(
              `private-user-${userId}`,
              "user_activity_detected",
              eventData
            );
          }
        }
        break;

      case "forceUserOffline":
        {
          // Validate data structure for forceUserOffline
          const forceUserOfflineRules = {
            targetUserId: {
              type: "string" as const,
              required: true,
              minLength: 1,
              maxLength: 255,
            },
            reason: {
              type: "string" as const,
              required: false,
              minLength: 1,
              maxLength: 500,
            },
          };

          if (!data || !validateParameterTypes(data, forceUserOfflineRules)) {
            return createErrorResponse(
              "Invalid data for forceUserOffline action",
              400
            );
          }

          const { targetUserId, reason } = data;

          // Validate that targetUserId is not null/empty after extraction
          if (!targetUserId || targetUserId.trim().length === 0) {
            return createErrorResponse(
              "Target user ID is required and cannot be empty",
              400
            );
          }

          // In a real implementation, you'd check admin permissions here
          await pusher.trigger(
            `private-user-${targetUserId}`,
            "force_user_offline",
            {
              userId: targetUserId,
              reason: reason || "System action",
              timestamp,
            }
          );
        }
        break;

      default:
        return createErrorResponse("Unknown action", 400);
    }

    return createSuccessResponse();
  } catch (error) {
    console.error("Pusher action error:", error);
    return createErrorResponse("Action failed", 500);
  }
}
