import { NextRequest, NextResponse } from "next/server";
import { extractUserIdFromAuth, createErrorResponse } from "../utils";
import { getPusherServer } from "@/lib/socket/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const params = new URLSearchParams(body);
    const socketId = params.get("socket_id");
    const channelName = params.get("channel_name");

    // Get user ID from Authorization header using utility
    const userId = extractUserIdFromAuth(request);

    console.log("🔍 [Pusher Auth] Request details:", {
      socketId,
      channelName,
      userId,
      authHeader: request.headers.get("Authorization"),
    });

    if (!socketId || !channelName || !userId) {
      return createErrorResponse("Missing required parameters", 400);
    }

    // Get Pusher server instance
    const pusher = getPusherServer();
    if (!pusher) {
      return createErrorResponse("Pusher server not available", 500);
    }

    // Validate that the user can access this channel
    if (channelName.startsWith("private-user-")) {
      const expectedChannelName = `private-user-${userId}`;
      if (channelName !== expectedChannelName) {
        console.error("Channel access denied:", {
          requestedChannel: channelName,
          expectedChannel: expectedChannelName,
          userId,
        });
        return NextResponse.json(
          { error: "Unauthorized access to channel" },
          { status: 403 }
        );
      }
    }

    // For private channels, authenticate the user
    if (channelName.startsWith("private-")) {
      const authResponse = pusher.authorizeChannel(socketId, channelName, {
        user_id: userId,
        user_info: {
          id: userId,
          name: "User", // You can fetch actual user data here
        },
      });

      return NextResponse.json(authResponse);
    }

    // For presence channels
    if (channelName.startsWith("presence-")) {
      const authResponse = pusher.authorizeChannel(socketId, channelName, {
        user_id: userId,
        user_info: {
          id: userId,
          name: "User", // You can fetch actual user data here
        },
      });

      return NextResponse.json(authResponse);
    }

    return NextResponse.json(
      { error: "Invalid channel type" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Pusher auth error:", error);
    return NextResponse.json(
      { error: "Authentication failed" },
      { status: 500 }
    );
  }
}
