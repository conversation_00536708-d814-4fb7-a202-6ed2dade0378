import { NextRequest } from "next/server";
import {
  extractAndValidateParams,
  validateParameterTypes,
  createErrorResponse,
  createSuccessResponse,
} from "../utils";

export async function POST(request: NextRequest) {
  try {
    const params = await extractAndValidateParams(request, ["notificationId"]);

    if (!params) {
      return createErrorResponse("Missing required parameters", 400);
    }

    // Additional validation with type checking
    const validationRules = {
      notificationId: {
        type: "string" as const,
        required: true,
        minLength: 1,
        maxLength: 255,
      },
    };

    if (!validateParameterTypes(params, validationRules)) {
      return createErrorResponse("Invalid parameter types or values", 400);
    }

    const { userId, notificationId } = params;

    // In a real implementation, you'd mark the notification as read in your database
    console.log(
      `Notification ${notificationId} acknowledged by user ${userId}`
    );

    return createSuccessResponse();
  } catch (error) {
    console.error("Notification acknowledgment error:", error);
    return createErrorResponse("Acknowledgment failed", 500);
  }
}
