"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { BraintreeService } from "@/lib/api/services/braintree";

// GET /api/braintree - Get client token for frontend initialization
export async function GET(request: NextRequest) {
  try {
    // Initialize Braintree service
    const session: any = await auth();
    const braintreeService = new BraintreeService();

    // Set the service context with session and request
    braintreeService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Get client token
    const result = await braintreeService.getClientToken();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 400,
    });
  } catch (error) {
    console.error("Braintree client token error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Failed to generate client token",
      },
      { status: 500 }
    );
  }
}

// POST /api/braintree - Process transaction
export async function POST(request: NextRequest) {
  try {
    // Initialize Braintree service
    const session: any = await auth();
    const braintreeService = new BraintreeService();

    // Set the service context with session and request
    braintreeService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const transactionData = await request.json();

    // Check if this is a test connection request
    if (transactionData.action === "test") {
      const result = await braintreeService.testConnection();
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 400,
      });
    }

    // Check if this is a customer creation request
    if (transactionData.action === "create_customer") {
      const result = await braintreeService.createCustomer(transactionData);
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 400,
      });
    }

    // Check if this is a payment method creation request
    if (transactionData.action === "create_payment_method") {
      const result = await braintreeService.createPaymentMethod(
        transactionData
      );
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 400,
      });
    }

    // Check if this is a transaction search request
    if (transactionData.action === "search_transactions") {
      const result = await braintreeService.searchTransactions(
        transactionData.searchParams || {}
      );
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 400,
      });
    }

    // Default: Process transaction
    const result = await braintreeService.createTransaction(transactionData);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 400,
    });
  } catch (error) {
    console.error("Braintree transaction error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Failed to process transaction",
      },
      { status: 500 }
    );
  }
}
