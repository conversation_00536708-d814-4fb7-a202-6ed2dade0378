"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { BraintreeService } from "@/lib/api/services/braintree";

// GET /api/braintree/status - Get Braintree connection and service status
export async function GET(request: NextRequest) {
  try {
    // Initialize Braintree service
    const session: any = await auth();
    const braintreeService = new BraintreeService({
      requireAuth: false, // Status check doesn't require authentication
    });

    // Set the service context with session and request
    braintreeService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Test Braintree connection and get status
    const connectionResult = await braintreeService.testConnection();

    if (!connectionResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: connectionResult.message || "Braintree connection failed",
          status: {
            isConnected: false,
            environment: process.env.BRAINTREE_ENVIRONMENT || "sandbox",
            lastChecked: new Date().toISOString(),
            error: connectionResult.message,
          },
        },
        { status: 503 } // Service Unavailable
      );
    }

    // Get additional status information
    const statusData = {
      isConnected: true,
      environment:
        connectionResult.data?.environment ||
        process.env.BRAINTREE_ENVIRONMENT ||
        "sandbox",
      lastChecked: new Date().toISOString(),
      merchantId: process.env.BRAINTREE_MERCHANT_ID
        ? "configured"
        : "not_configured",
      publicKey: process.env.BRAINTREE_PUBLIC_KEY
        ? "configured"
        : "not_configured",
      privateKey: process.env.BRAINTREE_PRIVATE_KEY
        ? "configured"
        : "not_configured",
      webhookUrl: process.env.BRAINTREE_WEBHOOK_URL || null,
      companyMerchantAccount:
        process.env.BRAINTREE_COMPANY_MERCHANT_ACCOUNT_ID || "default",
      serviceHealth: "operational",
      features: {
        transactions: true,
        customers: true,
        paymentMethods: true,
        webhooks: !!process.env.BRAINTREE_WEBHOOK_URL,
        voidRefund: true,
      },
    };

    return NextResponse.json({
      success: true,
      error: false,
      message: "Braintree service is operational",
      status: statusData,
    });
  } catch (error) {
    console.error("Braintree status check error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: `Braintree status check failed: ${errorMessage}`,
        status: {
          isConnected: false,
          environment: process.env.BRAINTREE_ENVIRONMENT || "sandbox",
          lastChecked: new Date().toISOString(),
          error: errorMessage,
          serviceHealth: "error",
        },
      },
      { status: 500 }
    );
  }
}

// POST /api/braintree/status - Force refresh Braintree status (admin only)
export async function POST(request: NextRequest) {
  try {
    // Initialize Braintree service with authentication required
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Authentication required for status refresh",
        },
        { status: 401 }
      );
    }

    const braintreeService = new BraintreeService({
      requireAuth: true,
    });

    // Set the service context with session and request
    braintreeService.setContext({
      session,
      user: session.user,
      request,
    });

    // Parse request body for any specific checks
    const body = await request.json().catch(() => ({}));
    const { includeTransactionStats = false, includeCustomerStats = false } =
      body;

    // Perform comprehensive status check
    const connectionResult = await braintreeService.testConnection();

    if (!connectionResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: connectionResult.message || "Braintree connection failed",
        },
        { status: 503 }
      );
    }

    // Build comprehensive status response
    const statusData: any = {
      isConnected: true,
      environment:
        connectionResult.data?.environment ||
        process.env.BRAINTREE_ENVIRONMENT ||
        "sandbox",
      lastChecked: new Date().toISOString(),
      refreshedBy: session.user.email || session.user.id,
      configuration: {
        merchantId: process.env.BRAINTREE_MERCHANT_ID
          ? "configured"
          : "not_configured",
        publicKey: process.env.BRAINTREE_PUBLIC_KEY
          ? "configured"
          : "not_configured",
        privateKey: process.env.BRAINTREE_PRIVATE_KEY
          ? "configured"
          : "not_configured",
        webhookUrl: process.env.BRAINTREE_WEBHOOK_URL || null,
        companyMerchantAccount:
          process.env.BRAINTREE_COMPANY_MERCHANT_ACCOUNT_ID || "default",
      },
      serviceHealth: "operational",
      features: {
        transactions: true,
        customers: true,
        paymentMethods: true,
        webhooks: !!process.env.BRAINTREE_WEBHOOK_URL,
        voidRefund: true,
      },
    };

    // Add optional statistics if requested
    if (includeTransactionStats) {
      statusData.statistics = {
        transactionStats: "Available in production implementation",
        note: "Transaction statistics would be fetched from Braintree API in production",
      };
    }

    if (includeCustomerStats) {
      statusData.customerStats = {
        customerStats: "Available in production implementation",
        note: "Customer statistics would be fetched from Braintree API in production",
      };
    }

    return NextResponse.json({
      success: true,
      error: false,
      message: "Braintree status refreshed successfully",
      status: statusData,
    });
  } catch (error) {
    console.error("Braintree status refresh error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    return NextResponse.json(
      {
        success: false,
        error: true,
        message: `Braintree status refresh failed: ${errorMessage}`,
      },
      { status: 500 }
    );
  }
}
