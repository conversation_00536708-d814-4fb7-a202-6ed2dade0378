"use server";

import { NextRequest, NextResponse } from "next/server";
import { BraintreeService } from "@/lib/api/services/braintree";

// POST /api/braintree/webhook - Handle Braintree webhook notifications
export async function POST(request: NextRequest) {
  try {
    // Initialize Braintree service
    const braintreeService = new BraintreeService({
      requireAuth: false, // Webhooks don't require user authentication
    });

    // Get webhook signature and payload
    const signature = request.headers.get("bt-signature");
    const payload = await request.text();

    if (!signature || !payload) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Missing webhook signature or payload",
        },
        { status: 400 }
      );
    }

    // Verify and process webhook
    const result = await braintreeService.processWebhook({
      bt_signature: signature,
      bt_payload: payload,
    });

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 400,
    });
  } catch (error) {
    console.error("Braintree webhook error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Failed to process webhook",
      },
      { status: 500 }
    );
  }
}

// GET /api/braintree/webhook - Webhook verification endpoint
export async function GET(request: NextRequest) {
  // This can be used for webhook URL verification if needed
  return NextResponse.json({
    success: true,
    message: "Braintree webhook endpoint is active",
    timestamp: new Date().toISOString(),
  });
}
