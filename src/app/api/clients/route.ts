"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ClientService } from "@/lib/api/services/client";

export async function GET(request: NextRequest) {
  try {
    // Initialize client service
    const session: any = await auth();
    const clientService = new ClientService();

    // Set the service context with session and request
    clientService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get clients
    const result = await clientService.getClients();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Clients GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
