"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoomService } from "@/lib/api/services/room";

export async function GET(request: NextRequest) {
  try {
    // Initialize room service
    const session: any = await auth();
    const roomService = new RoomService();

    // Set the service context with session and request
    roomService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Use the service to get rooms
    const result = await roomService.getRooms(queryParams);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Rooms GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Initialize room service
    const session: any = await auth();
    const roomService = new RoomService();

    // Set the service context with session and request
    roomService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to create room
    const result = await roomService.createRoom(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Room POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
