import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { CompanyService } from "@/lib/api/services/company";

/**
 * Individual Company API Route Handler
 *
 * Handles operations for specific companies using the [id] slug:
 * - GET: Get company by ID
 * - PUT: Update company by ID
 * - DELETE: Delete company by ID
 */

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/company/[id] - Get company by ID
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Company ID is required" },
        { status: 400 }
      );
    }

    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.getById(id);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 404 });
    }
  } catch (error) {
    console.error("Company GET by ID error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/company/[id] - Update company by ID
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Company ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.update(id, body);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 400 });
    }
  } catch (error) {
    console.error("Company PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/company/[id] - Delete company by ID
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: true, message: "Company ID is required" },
        { status: 400 }
      );
    }

    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.delete(id);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 404 });
    }
  } catch (error) {
    console.error("Company DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// POST method not allowed for individual company operations
export async function POST(request: NextRequest, { params }: RouteParams) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message:
        "Method not allowed. Use POST /api/company to create a new company or POST /api/company/upsert to create/update.",
    },
    { status: 405 }
  );
}
