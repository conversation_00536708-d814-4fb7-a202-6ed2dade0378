import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { CompanyService } from "@/lib/api/services/company";

/**
 * Current User Company API Route Handler
 * 
 * GET /api/company/current - Get current user's company
 */

export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.getCurrentUserCompany();

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      // Return 404 if user doesn't have a company yet (not an error)
      if (result.data === null) {
        return NextResponse.json(
          {
            success: true,
            error: false,
            data: null,
            message: "User does not have a company yet",
          },
          { status: 200 }
        );
      }
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error) {
    console.error("Current company GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// Other methods not allowed for current company endpoint
export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST /api/company/upsert to create/update company.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST /api/company/upsert to create/update company.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use DELETE /api/company/[id] to delete a specific company.",
    },
    { status: 405 }
  );
}
