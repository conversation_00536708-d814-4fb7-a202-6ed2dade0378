import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { CompanyService } from "@/lib/api/services/company";

/**
 * Company Upsert API Route Handler
 * 
 * POST /api/company/upsert - Create or update company for current user
 */

export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.upsert(body);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 400 });
    }
  } catch (error) {
    console.error("Company upsert error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// Only POST method is allowed for upsert
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to upsert company.",
    },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to upsert company.",
    },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Method not allowed. Use POST to upsert company.",
    },
    { status: 405 }
  );
}
