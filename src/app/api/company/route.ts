import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { CompanyService } from "@/lib/api/services/company";

/**
 * Company API Route Handler
 *
 * Handles CRUD operations for companies:
 * - GET: Get all companies (admin only) or current user's company
 * - POST: Create new company or upsert company
 */

// GET /api/company - Get all companies (admin) or current user's company
export async function GET(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const { searchParams } = new URL(request.url);
    const current = searchParams.get("current");

    // If current=true, get current user's company
    if (current === "true") {
      const result = await companyService.getCurrentUserCompany();

      if (result.success) {
        return NextResponse.json(result, { status: 200 });
      } else {
        return NextResponse.json(result, { status: result.statusCode || 500 });
      }
    }

    // Otherwise, get all companies (admin only)
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : undefined;
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : undefined;
    const search = searchParams.get("search") || undefined;
    const name = searchParams.get("name") || undefined;
    const tin = searchParams.get("tin") || undefined;
    const category = searchParams.get("category") || undefined;

    const result = await companyService.getAll({
      page,
      limit,
      search,
      name,
      tin,
      category,
    });

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 500 });
    }
  } catch (error) {
    console.error("Company GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// POST /api/company - Create new company
export async function POST(request: NextRequest) {
  try {
    const session: any = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: true, message: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const companyService = new CompanyService();
    companyService.setContext({ session, user: session.user, request });

    const result = await companyService.create(body);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: result.statusCode || 400 });
    }
  } catch (error) {
    console.error("Company POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/company - Update company (not implemented at route level, handled by /api/company/[id])
export async function PUT(request: NextRequest) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Use PUT /api/company/[id] to update a specific company",
    },
    { status: 405 }
  );
}

// DELETE /api/company - Delete company (not implemented at route level, handled by /api/company/[id])
export async function DELETE(request: NextRequest) {
  return NextResponse.json(
    {
      success: false,
      error: true,
      message: "Use DELETE /api/company/[id] to delete a specific company",
    },
    { status: 405 }
  );
}
