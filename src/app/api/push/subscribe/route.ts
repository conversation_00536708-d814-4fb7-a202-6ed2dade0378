import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { notificationService } from "@/lib/api/services/notification";

export async function GET(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    // Get push subscription using the service
    const response = await notificationService.getPushSubscription(
      session.user.id
    );

    if (!response.success) {
      // If no subscription found, return null instead of error
      if (response.statusCode === 404) {
        return NextResponse.json({
          success: true,
          subscription: null,
          vapidPublicKey: notificationService.getVapidPublicKey(),
        });
      }

      return NextResponse.json(
        { error: response.message || "Failed to get push subscription" },
        { status: response.statusCode || 500 }
      );
    }

    return NextResponse.json({
      success: true,
      subscription: response.data,
      vapidPublicKey: notificationService.getVapidPublicKey(),
    });
  } catch (error) {
    console.error("Error getting push subscription:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { endpoint, keys } = body;

    if (!endpoint || !keys?.p256dh || !keys?.auth) {
      return NextResponse.json(
        { error: "Invalid subscription data" },
        { status: 400 }
      );
    }

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    // Store or update push subscription using the service
    const response = await notificationService.upsertPushSubscription({
      userId: session.user.id,
      endpoint,
      p256dhKey: keys.p256dh,
      authKey: keys.auth,
    });

    if (!response.success) {
      return NextResponse.json(
        { error: response.message || "Failed to save push subscription" },
        { status: response.statusCode || 500 }
      );
    }

    return NextResponse.json({
      success: true,
      subscriptionId: response.data?.id,
    });
  } catch (error) {
    console.error("Error saving push subscription:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    // Delete push subscription using the service
    const response = await notificationService.deletePushSubscription(
      session.user.id
    );

    if (!response.success) {
      return NextResponse.json(
        { error: response.message || "Failed to remove push subscription" },
        { status: response.statusCode || 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Push subscription removed",
    });
  } catch (error) {
    console.error("Error removing push subscription:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
