import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import {
  notificationService,
  type SendPushRequest,
} from "@/lib/api/services/notification";

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session: any = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body: SendPushRequest = await request.json();

    // Initialize notification service with context
    await notificationService.initialize({
      session,
      user: session.user,
      request,
    });

    // Check VAPID configuration
    if (!notificationService.isVapidConfigured()) {
      return NextResponse.json(
        { error: "VAPID keys not configured" },
        { status: 500 }
      );
    }

    // Send push notification using the service
    const response = await notificationService.sendPushNotification(body);

    if (!response.success) {
      return NextResponse.json(
        { error: response.message || "Failed to send push notifications" },
        { status: response.statusCode || 500 }
      );
    }

    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Error sending push notifications:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
