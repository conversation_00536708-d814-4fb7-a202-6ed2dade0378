"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ChatService } from "@/lib/api/services/chat";

/**
 * GET /api/chat/statistics
 * Get chat statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Initialize chat service
    const session: any = await auth();
    const chatService = new ChatService();

    // Set the service context with session and request
    chatService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get statistics
    const result = await chatService.getStatistics();

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Chat statistics GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
