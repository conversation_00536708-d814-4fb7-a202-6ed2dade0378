import { Badge } from "@/components/common/ui/badge";
import { cn } from "@/lib/utils";

const Pill = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <Badge
      variant="secondary"
      className={cn(
        "flex flex-row items-center gap-2 bg-zinc-900/80 text-sm text-white px-4 py-2 border border-white/10 rounded-full hover:bg-zinc-800/80 transition-colors",
        className
      )}
    >
      {children}
    </Badge>
  );
};

export default Pill;
