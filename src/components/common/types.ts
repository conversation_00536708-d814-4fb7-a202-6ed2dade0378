// Art Component Types
export interface GridArtProps {
  amount?: number;
}

// CTA Component Types
export interface CallToActionsProps {
  theme?: "dark" | "light";
}

// Footer Component Types
export interface FooterProps {
  className?: string;
}

export interface ContactItem {
  title: string;
  email: string;
  url: string;
}

// Tab Component Types
export interface TabStyle {
  width?: string;
  theme?:
    | "primary"
    | "secondary"
    | "muted"
    | "accent"
    | "outline"
    | "lime"
    | "navy"
    | "gray"
    | "black";
}

export interface ListTabProps {
  title?: string;
  caption?: string;
  icon?: string;
  style?: TabStyle;
  position?: "start" | "center" | "end";
}

export interface RowTabProps {
  title?: string;
  caption?: string;
  style?:
    | "primary"
    | "secondary"
    | "muted"
    | "accent"
    | "lime"
    | "blue"
    | "navy"
    | "gray"
    | "black";
}

// Loader Component Types
export interface LoaderProps {
  active: boolean;
}

// Scroll Component Types
export interface ScrollAnimationProps {
  className?: string;
}

// Logo Component Types
export interface BrandProps {
  size?: "lg" | "md" | "sm";
  style?: string;
}

// Heading Component Types
export interface HeadingProps {
  tagline?: string;
  title?: string;
  className?: string;
}

// Document Types (migrated from documents-mock.ts)
export interface Document {
  id: string;
  name: string;
  path: string;
  file_type: string;
  size: string;
  status:
    | "created"
    | "submitted"
    | "received"
    | "negotiating"
    | "agreed"
    | "inprogress"
    | "reviewing"
    | "completed";
  category: string;
  association_entity: string;
  association_id: string;
  proposalId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentStatistics {
  total: number;
  created: number;
  submitted: number;
  received: number;
  negotiating: number;
  agreed: number;
  inprogress: number;
  reviewing: number;
  completed: number;
  totalSize: number;
  averageSize: number;
}

// Contract Types (migrated from contracts-mock.ts)
export interface Contract {
  id: string;
  title: string;
  description: string;
  status: "draft" | "active" | "completed" | "terminated" | "expired";
  clientName: string;
  contractValue: number;
  startDate: string;
  endDate: string;
  createdDate: string;
  lastModified: string;
  proposalId?: string;
}

export interface ContractStatistics {
  total: number;
  active: number;
  completed: number;
  draft: number;
  terminated: number;
  totalValue: number;
  averageValue: number;
}

// Proposal Types (migrated from proposals-mock.ts)
export interface Milestone {
  index: number;
  amount: number;
  description: string;
}

export interface Proposal {
  id: string;
  name: string;
  description: string;
  client: string;
  status: "draft" | "pending" | "approved" | "rejected" | "completed";
  budgetType: "fixed" | "milestone";
  fixedBudget?: number;
  milestones?: Milestone[];
  totalBudget: number;
  duration: number; // in weeks
  createdDate: string;
  lastModified: string;
  attachments: string[];
  agreed_to_terms_and_conditions: boolean;
}

export interface ProposalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  totalValue: number;
}

// Dashboard Types (migrated from dashboard-mock.ts)
export interface UserProfile {
  id: string;
  name: string;
  position: string;
  email: string;
  avatar: string;
  joinedDate: string;
  yearsInCompany: number;
  salary: number;
  projectsInProgress: number;
  projectsCompleted: number;
}

export interface ActivityData {
  weeklyActivity: number;
  design: number;
  communication: number;
}

export interface WorkingFormat {
  remote: number;
  hybrid: number;
}

export interface TimeEntry {
  id: string;
  project: string;
  time: string;
  status: "active" | "completed";
}

export interface CalendarEvent {
  id: string;
  time: string;
  title: string;
  type: string;
  hasVideo: boolean;
}

export interface CompanyInfo {
  name: string;
  logo: string;
  members: number;
  isPremium: boolean;
}

// Notification Types
export interface NotificationItem {
  id: string;
  title: string;
  message: string;
  type:
    | "room"
    | "chat"
    | "proposal"
    | "contract"
    | "systemAlerts"
    | "roleChanges"
    | "weeklyDigest";
  category: "email" | "push" | "inApp";
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  data?: {
    entityId?: string;
    entityType?: string;
    actionUrl?: string;
    [key: string]: any;
  };
}

export interface NotificationSummary {
  total: number;
  unread: number;
  read: number;
  byType: {
    room: number;
    chat: number;
    proposal: number;
    contract: number;
    systemAlerts: number;
    roleChanges: number;
    weeklyDigest: number;
  };
}
