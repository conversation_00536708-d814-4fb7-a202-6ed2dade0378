// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import type { CarouselProps } from "./types";

export const Carousel = ({ style = "", pictures }: CarouselProps) => {
  const transitionDelay = 8000;

  // * Main component
  return (
    <span className={style}>
      <Swiper
        spaceBetween={30}
        centeredSlides={false}
        oneWayMovement={true}
        autoplay={{
          delay: transitionDelay,
          disableOnInteraction: true,
        }}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          480: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 30,
          },
          1080: {
            slidesPerView: 3,
            spaceBetween: 30,
          },
        }}
        slidesPerView={3}
        modules={[Autoplay]}
      >
        {pictures?.map((image, index) => {
          return (
            <SwiperSlide key={index}>
              <img
                className="w-full h-[30em] object-cover overflow-hidden"
                src={image}
                alt={`slide-${index}`}
              />
            </SwiperSlide>
          );
        })}
      </Swiper>
    </span>
  );
};
