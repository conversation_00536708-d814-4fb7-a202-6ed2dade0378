"use client";

import { useEffect } from "react";
import { initializeServiceWorker } from "@/lib/service-worker";

interface ServiceWorkerInitializerProps {
  vapidPublicKey?: string;
}

export function ServiceWorkerInitializer({
  vapidPublicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
}: ServiceWorkerInitializerProps) {
  useEffect(() => {
    if (typeof window !== "undefined" && vapidPublicKey) {
      initializeServiceWorker(vapidPublicKey);
    }
  }, [vapidPublicKey]);

  return null; // This component doesn't render anything
}
