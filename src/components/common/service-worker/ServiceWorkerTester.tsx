"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Badge } from "@/components/common/ui/badge";
import { useServiceWorkerContext } from "./ServiceWorkerProvider";
import { toast } from "sonner";

export function ServiceWorkerTester() {
  const {
    isSupported,
    isPushSupported,
    notificationPermission,
    isSubscribed,
    isLoading,
    registerServiceWorker,
    requestNotificationPermission,
    subscribeToPush,
    unsubscribeFromPush,
    showTestNotification,
    updateServiceWorker,
    triggerNotificationSync,
  } = useServiceWorkerContext();

  const [swRegistration, setSwRegistration] =
    useState<ServiceWorkerRegistration | null>(null);
  const [swStatus, setSwStatus] = useState<string>("Unknown");

  useEffect(() => {
    // Check service worker status
    const checkServiceWorkerStatus = async () => {
      if ("serviceWorker" in navigator) {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          setSwRegistration(registration || null);

          if (registration) {
            if (registration.active) {
              setSwStatus("Active");
            } else if (registration.installing) {
              setSwStatus("Installing");
            } else if (registration.waiting) {
              setSwStatus("Waiting");
            } else {
              setSwStatus("Registered");
            }
          } else {
            setSwStatus("Not Registered");
          }
        } catch (error) {
          console.error("Failed to check service worker status:", error);
          setSwStatus("Error");
        }
      } else {
        setSwStatus("Not Supported");
      }
    };

    checkServiceWorkerStatus();

    // Check status every 5 seconds
    const interval = setInterval(checkServiceWorkerStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleRegisterServiceWorker = async () => {
    try {
      const registration = await registerServiceWorker();
      if (registration) {
        setSwRegistration(registration);
        toast.success("Service Worker registered successfully");
      } else {
        toast.error("Failed to register Service Worker");
      }
    } catch (error) {
      console.error("Service Worker registration failed:", error);
      toast.error("Service Worker registration failed");
    }
  };

  const handleRequestPermission = async () => {
    try {
      const permission = await requestNotificationPermission();
      toast.success(`Notification permission: ${permission}`);
    } catch (error) {
      console.error("Permission request failed:", error);
      toast.error("Permission request failed");
    }
  };

  const handleSubscribeToPush = async () => {
    try {
      const subscription = await subscribeToPush();
      if (subscription) {
        toast.success("Successfully subscribed to push notifications");
      } else {
        toast.error("Failed to subscribe to push notifications");
      }
    } catch (error) {
      console.error("Push subscription failed:", error);
      toast.error("Push subscription failed");
    }
  };

  const handleUnsubscribeFromPush = async () => {
    try {
      const success = await unsubscribeFromPush();
      if (success) {
        toast.success("Successfully unsubscribed from push notifications");
      } else {
        toast.error("Failed to unsubscribe from push notifications");
      }
    } catch (error) {
      console.error("Push unsubscription failed:", error);
      toast.error("Push unsubscription failed");
    }
  };

  const handleShowTestNotification = async () => {
    try {
      await showTestNotification();
      toast.success("Test notification sent");
    } catch (error) {
      console.error("Test notification failed:", error);
      toast.error("Test notification failed");
    }
  };

  const handleUpdateServiceWorker = async () => {
    try {
      await updateServiceWorker();
      toast.success("Service Worker updated");
    } catch (error) {
      console.error("Service Worker update failed:", error);
      toast.error("Service Worker update failed");
    }
  };

  const handleTriggerNotificationSync = async () => {
    try {
      const result = await triggerNotificationSync();
      if (result) {
        toast.success("Notification sync triggered successfully");
      } else {
        toast.error("Failed to trigger notification sync");
      }
    } catch (error) {
      console.error("Notification sync failed:", error);
      toast.error("Notification sync failed");
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active":
        return "default";
      case "Installing":
      case "Waiting":
        return "secondary";
      case "Not Registered":
      case "Error":
      case "Not Supported":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Service Worker Tester</CardTitle>
        <CardDescription>
          Test and debug service worker functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Information */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">Support Status</h4>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm">Service Worker:</span>
                <Badge variant={isSupported ? "default" : "destructive"}>
                  {isSupported ? "Supported" : "Not Supported"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Push Notifications:</span>
                <Badge variant={isPushSupported ? "default" : "destructive"}>
                  {isPushSupported ? "Supported" : "Not Supported"}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Current Status</h4>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm">SW Status:</span>
                <Badge variant={getStatusBadgeVariant(swStatus)}>
                  {swStatus}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Permission:</span>
                <Badge
                  variant={
                    notificationPermission === "granted"
                      ? "default"
                      : "destructive"
                  }
                >
                  {notificationPermission}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Push Subscription:</span>
                <Badge variant={isSubscribed ? "default" : "secondary"}>
                  {isSubscribed ? "Subscribed" : "Not Subscribed"}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={handleRegisterServiceWorker}
              disabled={!isSupported || isLoading}
              variant="outline"
            >
              Register SW
            </Button>
            <Button
              onClick={handleUpdateServiceWorker}
              disabled={!swRegistration || isLoading}
              variant="outline"
            >
              Update SW
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={handleRequestPermission}
              disabled={!isPushSupported || isLoading}
              variant="outline"
            >
              Request Permission
            </Button>
            <Button
              onClick={handleShowTestNotification}
              disabled={notificationPermission !== "granted" || isLoading}
              variant="outline"
            >
              Test Notification
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={handleSubscribeToPush}
              disabled={
                !isPushSupported ||
                notificationPermission !== "granted" ||
                isSubscribed ||
                isLoading
              }
              variant="outline"
            >
              Subscribe Push
            </Button>
            <Button
              onClick={handleUnsubscribeFromPush}
              disabled={!isSubscribed || isLoading}
              variant="outline"
            >
              Unsubscribe Push
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-2">
            <Button
              onClick={handleTriggerNotificationSync}
              disabled={!swRegistration || isLoading}
              variant="outline"
              className="w-full"
            >
              🔄 Sync Notifications
            </Button>
          </div>
        </div>

        {/* Debug Information */}
        {swRegistration && (
          <div className="space-y-2">
            <h4 className="font-medium">Debug Info</h4>
            <div className="text-xs font-mono bg-muted p-2 rounded">
              <div>Scope: {swRegistration.scope}</div>
              <div>Update Via Cache: {swRegistration.updateViaCache}</div>
              {swRegistration.active && (
                <div>Script URL: {swRegistration.active.scriptURL}</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
