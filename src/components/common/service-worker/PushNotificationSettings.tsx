"use client";

import React from 'react';
import { Bell, BellOff, TestTube, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/common/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/common/ui/card';
import { Badge } from '@/components/common/ui/badge';
import { Alert, AlertDescription } from '@/components/common/ui/alert';
import { usePushNotifications } from './ServiceWorkerProvider';

export function PushNotificationSettings() {
  const {
    isPushSupported,
    notificationPermission,
    isSubscribed,
    isLoading,
    enablePushNotifications,
    disablePushNotifications,
    showTestNotification,
    canEnablePush,
    needsPermission,
  } = usePushNotifications();

  const getPermissionBadge = () => {
    switch (notificationPermission) {
      case 'granted':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Granted</Badge>;
      case 'denied':
        return <Badge variant="destructive"><BellOff className="w-3 h-3 mr-1" />Denied</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />Not Requested</Badge>;
    }
  };

  const getSubscriptionBadge = () => {
    if (isSubscribed) {
      return <Badge variant="default" className="bg-blue-500"><Bell className="w-3 h-3 mr-1" />Active</Badge>;
    }
    return <Badge variant="outline"><BellOff className="w-3 h-3 mr-1" />Inactive</Badge>;
  };

  if (!isPushSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BellOff className="w-5 h-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Manage your push notification preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Push notifications are not supported in your browser. Please use a modern browser like Chrome, Firefox, or Safari.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Push Notifications
        </CardTitle>
        <CardDescription>
          Manage your push notification preferences and test functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Permission Status</div>
            {getPermissionBadge()}
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium">Subscription Status</div>
            {getSubscriptionBadge()}
          </div>
        </div>

        {/* Permission Denied Alert */}
        {notificationPermission === 'denied' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Push notifications are blocked. To enable them, click the lock icon in your browser's address bar and allow notifications, then refresh the page.
            </AlertDescription>
          </Alert>
        )}

        {/* Need Permission Alert */}
        {needsPermission && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You need to grant notification permission to receive push notifications.
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          {!isSubscribed && canEnablePush && (
            <Button
              onClick={enablePushNotifications}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Bell className="w-4 h-4" />
              )}
              Enable Push Notifications
            </Button>
          )}

          {isSubscribed && (
            <Button
              onClick={disablePushNotifications}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <BellOff className="w-4 h-4" />
              )}
              Disable Push Notifications
            </Button>
          )}

          {isSubscribed && (
            <Button
              onClick={showTestNotification}
              disabled={isLoading}
              variant="secondary"
              className="flex items-center gap-2"
            >
              <TestTube className="w-4 h-4" />
              Test Notification
            </Button>
          )}
        </div>

        {/* Information */}
        <div className="text-sm text-muted-foreground space-y-2">
          <p>
            <strong>What are push notifications?</strong>
          </p>
          <p>
            Push notifications allow you to receive real-time updates about:
          </p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>New messages in chat rooms</li>
            <li>Document updates and changes</li>
            <li>Contract status changes</li>
            <li>Proposal updates</li>
            <li>System alerts and announcements</li>
          </ul>
          <p className="mt-3">
            You can disable notifications at any time from this page or your browser settings.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
