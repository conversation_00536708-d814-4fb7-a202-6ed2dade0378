import { motion } from "framer-motion";
import { Jelly } from "ldrs/react";
import "ldrs/react/Jelly.css";
import { cn } from "@/lib/utils";
import type { LoaderProps } from "./types";

export const Loader = ({ active }: LoaderProps) => {
  const transition = { ease: "easeInOut", duration: 0.8 };

  if (!active) return null;

  return (
    <motion.div
      transition={transition}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        "bg-background/80 backdrop-blur-md",
        "transition-all duration-300"
      )}
    >
      <div className="flex flex-col items-center gap-4 p-8">
        <Jelly size="44" speed=".8" color="hsl(var(--primary))" />
      </div>
    </motion.div>
  );
};
