import type { ScrollAnimationProps } from "./types";

export const ScrollAnimation = ({}: ScrollAnimationProps) => {
  return (
    <span className="scroll-arrow-container hidden xl:flex xl:flex-row xl:items-center xl:justify-center overflow-hidden">
      <img
        className="scroll-arrow-icon"
        src="/svg/down-arrow.svg"
        width={"30"}
        alt="underscor's scroll down animation"
      />
    </span>
  );
};
