import Link from "next/link";
import {
  FaLinkedin as LinkedIn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  FaInstagram as Instagram,
  FaGithub as Gith<PERSON>,
} from "react-icons/fa";
import { FaXTwitter as Twitter, FaThreads as Threads } from "react-icons/fa6";

import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/common/ui/card";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { Button } from "@/components/common/ui/button";
import { cn } from "@/lib/utils";
import type { TeamProps } from "./types";

export const Team = ({
  img,
  name,
  title,
  style = "",
  socials = [],
}: TeamProps) => {
  const socialIcons = {
    linkedin: LinkedIn,
    twitter: Twitter,
    dribbble: Dribbble,
    threads: Threads,
    instagram: Instagram,
    github: Github,
  };

  const getInitials = (name?: string) => {
    return (
      name
        ?.split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase() || "?"
    );
  };

  return (
    <Card className={cn("w-full xl:w-max max-w-sm", style)}>
      <CardHeader className="pb-4">
        <div className="flex justify-center">
          <Avatar className="w-32 h-32">
            <AvatarImage src={img} alt={`${name} profile`} />
            <AvatarFallback className="text-2xl font-semibold">
              {getInitials(name)}
            </AvatarFallback>
          </Avatar>
        </div>
      </CardHeader>

      <CardContent className="text-center pb-4">
        <h3 className="text-lg font-semibold text-foreground mb-1">{name}</h3>
        <p className="text-sm text-muted-foreground capitalize">{title}</p>
      </CardContent>

      {socials && socials.length > 0 && (
        <CardFooter className="justify-center pt-0">
          <div className="flex gap-2">
            {socials.map((link, index) => {
              const { url, name: iconName } = link ?? {};
              const IconComponent =
                socialIcons[iconName as keyof typeof socialIcons];

              if (!IconComponent) return null;

              return (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  asChild
                >
                  <Link href={url} target="_blank" rel="noopener noreferrer">
                    <IconComponent size={16} />
                    <span className="sr-only">{iconName}</span>
                  </Link>
                </Button>
              );
            })}
          </div>
        </CardFooter>
      )}
    </Card>
  );
};
