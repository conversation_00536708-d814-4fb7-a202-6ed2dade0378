import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/common/ui/card";
import { cn } from "@/lib/utils";
import type { ServiceProps } from "./types";

export const Service = ({ img, title, description }: ServiceProps) => {
  return (
    <Card className="w-full border-0 bg-transparent shadow-none">
      <CardHeader className="px-0 pb-4">
        {img?.url && (
          <div className="mb-4">
            <img
              className="w-auto h-auto max-w-full"
              src={img.url}
              width={img.width}
              height={img.height}
              alt={`${title} service illustration`}
            />
          </div>
        )}
        <CardTitle className="text-2xl md:text-3xl lg:text-4xl font-light text-primary">
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className="px-0">
        <p className="text-lg md:text-xl font-extralight text-foreground leading-relaxed md:max-w-md lg:max-w-lg xl:max-w-2xl">
          {description}
        </p>
      </CardContent>
    </Card>
  );
};
