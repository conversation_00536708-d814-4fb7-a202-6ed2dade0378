import { Card, CardContent, CardHeader } from "@/components/common/ui/card";
import type { TestimonyCardProps } from "./types";

export const TestimonyCard = ({
  company,
  message,
  from,
}: TestimonyCardProps) => {
  return (
    <Card className="w-full border-border">
      <CardHeader className="pb-4">
        <img
          className="w-[100px]"
          src={`/svg/companies/${company}.svg`}
          width={"auto"}
          height={"200"}
          alt={`${company} logo`}
        />
      </CardHeader>

      <CardContent className="flex flex-col gap-8">
        <p className="text-foreground">{message}</p>
        <div className="flex flex-col gap-1">
          <p className="font-bold text-foreground">{from?.name}</p>
          <p className="text-muted-foreground">{from?.designation}</p>
        </div>
      </CardContent>
    </Card>
  );
};
