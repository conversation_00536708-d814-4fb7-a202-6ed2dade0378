"use client";

import { useState } from "react";

import { Brand } from "../../logo";

import { TapMe } from "./standard";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import type { ThumbnailedProps } from "./types";

export const Thumbnailed = ({
  image,
  title,
  caption,
  navigateTo,
  style = "",
}: ThumbnailedProps) => {
  const navigate = useRouter();
  const [hover, setHover] = useState(false);

  function handleNavigation() {
    const { internal, url, params } = navigateTo ?? {};
    if (internal && url) {
      navigate.push(url, params);
    } else if (url) {
      window.location.href = url;
    }
  }

  const backgroundImage = `url(${
    navigateTo?.internal
      ? (window.location.origin ?? "") + (image?.url ?? "")
      : image?.url ?? ""
  })`;

  return (
    <span
      style={{ backgroundImage }}
      //
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      onClick={handleNavigation}
      className={`card ${style}`}
    >
      <motion.span
        animate={hover ? "hover" : "normal"}
        variants={{
          hover: {
            translateY: -48,
          },
          normal: {
            translateY: 0,
          },
        }}
        transition={{ duration: 0.6, ease: "backInOut" }}
        className="content origin-bottom"
      >
        <Brand size="sm" />
        {/* title */}
        <h6 className="text-white">{title}</h6>
        {/* Caption */}
        <p className="text-slate-300">{caption}</p>
        {/* Links */}
      </motion.span>
      <TapMe active={hover} text="Tap me, for more" />
      {/* underline */}
      <motion.span
        animate={hover ? "show" : "hide"}
        transition={{ duration: 0.6, ease: "easeInOut" }}
        variants={{
          show: {
            width: "100%",
          },
          hide: {
            width: 0,
          },
        }}
        className={`h-[2px] absolute z-20 bottom-0 flex bg-lime-200`}
      ></motion.span>
      <motion.span
        initial={{ scaleY: 0, opacity: 0 }}
        animate={{ scaleY: hover ? 4.5 : 1, opacity: 100 }}
        transition={{ duration: 0.45, ease: "easeInOut" }}
        className="overlay overlay-nrm"
      ></motion.span>
    </span>
  );
};
