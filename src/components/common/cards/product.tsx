"use client";

import { useRouter } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
} from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { removeSpaces } from "@/lib/common/utils";
import type { ProductProps } from "./types";

export const Product = ({
  id,
  image,
  title,
  caption,
  categories = [],
  price,
}: ProductProps) => {
  const navigate = useRouter();

  const handleViewProduct = () => {
    navigate.push(`/market/${removeSpaces(title ?? "")}/${id}`);
  };

  return (
    <Card className="w-full h-[25em] cursor-pointer bg-primary border-0 overflow-hidden group hover:shadow-lg transition-all duration-300">
      <CardHeader className="p-0 h-1/2">
        <div className="w-full h-full relative bg-primary/80 overflow-hidden">
          <img
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            src={(window.location.origin ?? "") + (image?.url ?? "")}
            width={7680}
            height={4320}
            alt={`${title} product`}
          />
        </div>
      </CardHeader>

      <CardContent className="p-4 flex-1 flex flex-col justify-between">
        <div className="space-y-3">
          <h3 className="text-primary-foreground text-xl font-semibold line-clamp-2">
            {title}
          </h3>

          {categories && categories.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {categories.map((category, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs uppercase bg-secondary text-secondary-foreground hover:bg-secondary/80"
                >
                  {category?.name}
                </Badge>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-between items-center mt-4 pt-3 border-t border-primary-foreground/20">
          <span className="text-primary-foreground font-bold text-lg">
            Price
          </span>
          <span className="text-primary-foreground font-bold text-lg">
            ${price}
          </span>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button
          onClick={handleViewProduct}
          className="w-full bg-gradient-to-r from-secondary via-secondary/80 to-secondary border border-secondary/50 text-secondary-foreground font-medium hover:from-secondary/80 hover:via-secondary/60 hover:to-secondary/80 transition-all duration-300"
        >
          Check it out
        </Button>
      </CardFooter>
    </Card>
  );
};
