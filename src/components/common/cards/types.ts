// Product Card Types
export interface ProductImage {
  url?: string;
}

export interface ProductCategory {
  name: string;
}

export interface ProductProps {
  id: string | number;
  image?: ProductImage;
  title?: string;
  caption?: string;
  categories?: ProductCategory[];
  price?: number | string;
}

// Team Card Types
export interface TeamSocial {
  url: string;
  name: keyof typeof SocialIcons;
}

export interface TeamProps {
  img?: string;
  name?: string;
  title?: string;
  style?: string;
  socials?: TeamSocial[];
}

// Service Card Types
export interface ServiceImg {
  url?: string;
  width?: number;
  height?: number;
}

export interface ServiceProps {
  img?: ServiceImg;
  title?: string;
  description?: string;
}

// Testimony Card Types
export interface TestimonyFrom {
  name?: string;
  designation?: string;
}

export interface TestimonyCardProps {
  company: string;
  message: string;
  from?: TestimonyFrom;
}

// Helper for TeamSocial type
export const SocialIcons = {
  linkedin: true,
  twitter: true,
  dribbble: true,
  threads: true,
  instagram: true,
  github: true,
};
