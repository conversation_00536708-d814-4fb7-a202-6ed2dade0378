"use client";

import React, { useState, useMemo } from "react";
import { <PERSON>, <PERSON>, X, <PERSON>ting<PERSON>, Trash2, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { ScrollArea } from "@/components/common/ui/scroll-area";
import { Separator } from "@/components/common/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/common/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { useParams, useRouter } from "next/navigation";

import { useNotifications } from "@/hooks/useNotifications";
import { NotificationItem } from "@/components/common/types";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

// Notification type icons and colors
const notificationConfig = {
  room: {
    icon: "🏠",
    color: "bg-blue-500",
    bgColor: "bg-blue-50 dark:bg-blue-950",
  },
  chat: {
    icon: "💬",
    color: "bg-blue-500",
    bgColor: "bg-blue-50 dark:bg-blue-950",
  },

  contract: {
    icon: "📋",
    color: "bg-purple-500",
    bgColor: "bg-purple-50 dark:bg-purple-950",
  },
  proposal: {
    icon: "💼",
    color: "bg-orange-500",
    bgColor: "bg-orange-50 dark:bg-orange-950",
  },
  systemAlerts: {
    icon: "⚠️",
    color: "bg-red-500",
    bgColor: "bg-red-50 dark:bg-red-950",
  },
  roleChanges: {
    icon: "🔐",
    color: "bg-indigo-500",
    bgColor: "bg-indigo-50 dark:bg-indigo-950",
  },
  weeklyDigest: {
    icon: "📊",
    color: "bg-teal-500",
    bgColor: "bg-teal-50 dark:bg-teal-950",
  },
};

interface NotificationItemProps {
  notification: NotificationItem;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onClick?: (notification: NotificationItem) => void;
}

function NotificationItemComponent({
  notification,
  onMarkAsRead,
  onDelete,
  onClick,
}: NotificationItemProps) {
  const config = notificationConfig[notification.type];
  const timeAgo = formatDistanceToNow(new Date(notification.createdAt), {
    addSuffix: true,
  });

  return (
    <div
      className={cn(
        "group relative p-3 rounded-lg border transition-colors cursor-pointer hover:bg-muted/50",
        !notification.isRead && "bg-muted/30 border-primary/20",
        notification.isRead && "bg-background border-border"
      )}
      onClick={() => onClick?.(notification)}
    >
      <div className="flex items-start gap-3">
        {/* Notification Icon */}
        <div
          className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm",
            config.bgColor
          )}
        >
          <span>{config.icon}</span>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0 max-w-md">
          <div className="flex items-start justify-between gap-2">
            <h4 className="text-sm font-medium text-foreground truncate text-wrap">
              {notification.title}
            </h4>
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {!notification.isRead && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onMarkAsRead(notification.id);
                  }}
                >
                  <Check className="h-3 w-3" />
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {!notification.isRead && (
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        onMarkAsRead(notification.id);
                      }}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Mark as read
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(notification.id);
                    }}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {notification.message}
          </p>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-muted-foreground">{timeAgo}</span>
            {!notification.isRead && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export function NotificationsPopover() {
  const [isOpen, setIsOpen] = useState(false);
  const { slug } = useParams();
  const router = useRouter();
  // Don't enable socket handling here since it's handled by NotificationIntegrationProvider
  const {
    isLoading,
    notifications,
    markAsRead,
    markAsUnread,
    deleteNotification,
    markAllAsRead,
    clearAll,
    sendUnifiedNotification,
  } = useNotifications({ enableSocketHandling: false });

  const { unreadCount, hasUnread } = useMemo(() => {
    const unread = notifications.filter((n: any) => !n.isRead);
    return {
      unreadCount: unread.length,
      hasUnread: unread.length > 0,
    };
  }, [notifications]);

  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsRead(id);
    } catch (error) {
      console.error("Failed to mark as read:", error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteNotification(id);
    } catch (error) {
      console.error("Failed to delete notification:", error);
    }
  };

  const handleMarkAsUnread = async (id: string) => {
    try {
      await markAsUnread(id);
    } catch (error) {
      console.error("Failed to mark as unread:", error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error("Failed to mark all as read:", error);
    }
  };

  const handleClearAll = async () => {
    try {
      await clearAll();
    } catch (error) {
      console.error("Failed to clear all notifications:", error);
    }
  };

  const handleNotificationClick = async (notification: NotificationItem) => {
    try {
      // Mark as read when clicked
      if (!notification.isRead) {
        await markAsRead(notification.id);
      }

      // Navigate to the notification's action URL
      if (notification.data?.actionUrl) {
        window.location.href = notification.data.actionUrl;
      }
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to handle notification click:", error);
    }
  };

  // Note: Notification preferences are now automatically synced via NotificationIntegrationProvider
  // No need to manually set preferences here as it's handled at the provider level

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {hasUnread && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full min-w-[27rem] p-0" align="end">
        <div className="flex items-center justify-between p-4 border-border border-b">
          <h3 className="font-semibold">Notifications</h3>
          <div className="flex items-center gap-2">
            {hasUnread && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-xs"
              >
                Mark all read
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={handleClearAll}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <ScrollArea className="h-96">
          {isLoading ? (
            <div className="p-4 text-center text-muted-foreground">
              Loading notifications...
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No notifications yet</p>
            </div>
          ) : (
            <div className="p-2 space-y-2">
              {notifications.map((notification: any) => (
                <NotificationItemComponent
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                  onClick={handleNotificationClick}
                />
              ))}
            </div>
          )}
        </ScrollArea>

        <Separator />
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => {
              setIsOpen(false);
              router.push(`${slug}/settings`);
            }}
          >
            <Settings className="h-4 w-4 mr-2" />
            Notification Settings
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
