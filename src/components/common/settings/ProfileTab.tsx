"use client";

/**
 * Profile Tab Component
 *
 * Manages user profile information based on the user and profile schema.
 * Handles both user basic info and extended profile details.
 */

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Textarea } from "@/components/common/ui/textarea";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { useUserProfile } from "@/hooks/useRBAC";
import { Save, User, Globe, MapPin, Calendar, Mail } from "lucide-react";
import { toast } from "sonner";

export function ProfileTab() {
  const { data: session } = useSession();
  const { profile, currentUser, isLoading, updateUserProfile } =
    useUserProfile();

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    about: "",
    website: "",
    location: "",
    socials: [] as string[],
  });
  const [socialInput, setSocialInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (currentUser) {
      setFormData({
        name: currentUser.name || "",
        email: currentUser.email || "",
        about: profile?.about || "",
        website: profile?.website || "",
        location: profile?.location || "",
        socials: profile?.socials || [],
      });
    }
  }, [currentUser, profile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddSocial = () => {
    if (socialInput.trim() && !formData.socials.includes(socialInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        socials: [...prev.socials, socialInput.trim()],
      }));
      setSocialInput("");
    }
  };

  const handleRemoveSocial = (social: string) => {
    setFormData((prev) => ({
      ...prev,
      socials: prev.socials.filter((s) => s !== social),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.about.trim() ||
      !formData.website.trim() ||
      !formData.location.trim()
    ) {
      toast.error("Please fill in all required profile fields");
      return;
    }

    setIsSubmitting(true);

    try {
      const profileData = {
        about: formData.about.trim(),
        website: formData.website.trim(),
        location: formData.location.trim(),
        socials: formData.socials,
      };

      // Server handles upsert logic - single call for create or update
      const result = await updateUserProfile(profileData);

      if (result.meta?.requestStatus === "fulfilled") {
        toast.success("Profile updated successfully");
      } else {
        toast.error("Failed to update profile");
      }
    } catch (error) {
      toast.error("An error occurred while updating profile");
      console.error("Profile update error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* User Basic Info */}
      <div className="flex items-center space-y-2">
        <h3 className="text-xl font-semibold">
          {currentUser?.name || "Unnamed User"}
        </h3>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Mail className="h-4 w-4" />
          {currentUser?.email}
        </div>
        {currentUser?.emailVerified && (
          <Badge variant="outline" className="text-xs">
            Email Verified
          </Badge>
        )}
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Calendar className="h-4 w-4" />
          Joined {new Date(currentUser?.createdAt || "").toLocaleDateString()}
        </div>
      </div>

      {/* Profile Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </h4>

            <div className="space-y-2">
              <Label htmlFor="name">Display Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Your display name"
                disabled // User name is managed by auth system
              />
              <p className="text-xs text-gray-500">
                Display name is managed by your authentication provider
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="<EMAIL>"
                disabled // Email is managed by auth system
              />
              <p className="text-xs text-gray-500">
                Email address is managed by your authentication provider
              </p>
            </div>
          </div>

          {/* Extended Profile */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Profile Details
            </h4>

            <div className="space-y-2">
              <Label htmlFor="website">Website *</Label>
              <Input
                id="website"
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange("website", e.target.value)}
                placeholder="https://your-website.com"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location *</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) =>
                    handleInputChange("location", e.target.value)
                  }
                  placeholder="City, Country"
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        <Avatar className="h-20 w-20">
          <AvatarImage
            src={currentUser?.image || ""}
            alt={currentUser?.name || "User"}
          />
          <AvatarFallback className="text-lg">
            {currentUser?.name?.charAt(0)?.toUpperCase() || "U"}
          </AvatarFallback>
        </Avatar>
        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Profile
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
