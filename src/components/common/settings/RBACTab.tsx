"use client";

/**
 * RBAC Tab Component
 *
 * Provides RBAC management interface with user list, roles, and permissions.
 * Integrates with the updated RBAC system and schema.
 */

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/common/ui/table";
import { UserRoleDialog } from "@/components/common/rbac/UserRoleDialog";
import { RolePermissionDialog } from "@/components/common/rbac/RolePermissionDialog";
import { useRBAC, usePermissions, useRoleManagement } from "@/hooks/useRBAC";
import {
  Users,
  Shield,
  UserPlus,
  Settings,
  Search,
  Plus,
  Edit,
  Eye,
  Trash2,
} from "lucide-react";

export function RBACTab() {
  const { users, isLoading, error, initializeRBAC } = useRBAC();
  const { roles = [] } = useRoleManagement(); // Ensure roles is always an array
  const { hasPermission, userRole } = usePermissions();

  const [searchTerm, setSearchTerm] = useState("");
  const [userRoleDialogOpen, setUserRoleDialogOpen] = useState(false);
  const [rolePermissionDialogOpen, setRolePermissionDialogOpen] =
    useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string>();
  const [selectedRoleId, setSelectedRoleId] = useState<string>();
  const [roleDialogMode, setRoleDialogMode] = useState<
    "create" | "edit" | "view"
  >("create");

  // Initialize RBAC data on mount
  useEffect(() => {
    initializeRBAC();
  }, [initializeRBAC]);

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const openUserRoleDialog = (userId?: string) => {
    setSelectedUserId(userId);
    setUserRoleDialogOpen(true);
  };

  const openRoleDialog = (
    mode: "create" | "edit" | "view",
    roleId?: string
  ) => {
    setRoleDialogMode(mode);
    setSelectedRoleId(roleId);
    setRolePermissionDialogOpen(true);
  };

  const handleDialogSuccess = () => {
    // Refresh data after successful operations
    initializeRBAC();
  };

  // Check if user has admin permissions
  const canManageUsers =
    hasPermission("user:manage") || hasPermission("admin:all");
  const canManageRoles =
    hasPermission("role:manage") || hasPermission("admin:all");

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">Error loading RBAC data: {error}</p>
        <Button onClick={initializeRBAC} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current User Role Info */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">Your Current Role</h4>
        {userRole ? (
          <div className="flex items-center gap-2">
            <Badge variant="outline">{userRole.name}</Badge>
            <span className="text-sm text-blue-600">
              {userRole.permissions.length} permission
              {userRole.permissions.length !== 1 ? "s" : ""}
            </span>
          </div>
        ) : (
          <p className="text-sm text-blue-600">No role assigned</p>
        )}
      </div>

      {/* Users Management Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users ({users.length})
          </h3>
          {canManageUsers && (
            <Button onClick={() => openUserRoleDialog()} size="sm">
              <UserPlus className="h-4 w-4 mr-2" />
              Assign Roles
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Users Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
                {canManageUsers && <TableHead>Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={user.image || ""}
                            alt={user.name || "User"}
                          />
                          <AvatarFallback className="text-xs">
                            {user.name?.charAt(0)?.toUpperCase() || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {user.name || "Unnamed User"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.role ? (
                        <Badge variant="outline">{user.role.name}</Badge>
                      ) : (
                        <span className="text-sm text-gray-500">No role</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">Active</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </TableCell>
                    {canManageUsers && (
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openUserRoleDialog(user.id)}
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit Role
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={canManageUsers ? 5 : 4}
                    className="text-center py-8"
                  >
                    <p className="text-gray-500">No users found</p>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <Separator />

      {/* Roles Management Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Roles ({roles.length})
          </h3>
          {canManageRoles && (
            <Button onClick={() => openRoleDialog("create")} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Role
            </Button>
          )}
        </div>

        {/* Roles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {roles.length > 0 ? (
            roles.map((role) => (
              <div key={role.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{role.name}</h4>
                  <div className="flex items-center gap-1">
                    {canManageRoles && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openRoleDialog("view", role.id)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openRoleDialog("edit", role.id)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {role.permissions.length} permission
                      {role.permissions.length !== 1 ? "s" : ""}
                    </Badge>
                    {role.status === "active" && (
                      <Badge variant="outline" className="text-xs">
                        Active
                      </Badge>
                    )}
                  </div>

                  <div className="text-sm text-gray-500">
                    {users.filter((u) => u.roleId === role.id).length} user
                    {users.filter((u) => u.roleId === role.id).length !== 1
                      ? "s"
                      : ""}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">No roles found</p>
            </div>
          )}
        </div>
      </div>

      {/* Permission Check Notice */}
      {!canManageUsers && !canManageRoles && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            You have read-only access to RBAC settings. Contact an administrator
            to manage users and roles.
          </p>
        </div>
      )}

      {/* Dialogs */}
      <UserRoleDialog
        open={userRoleDialogOpen}
        onOpenChange={setUserRoleDialogOpen}
        selectedUserId={selectedUserId}
        onSuccess={handleDialogSuccess}
      />

      <RolePermissionDialog
        open={rolePermissionDialogOpen}
        onOpenChange={setRolePermissionDialogOpen}
        mode={roleDialogMode}
        roleId={selectedRoleId}
        onSuccess={handleDialogSuccess}
      />
    </div>
  );
}
