"use client";

/**
 * Notifications Tab Component
 * 
 * Manages user notification preferences for different types of alerts.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/common/ui/button';
import { Label } from '@/components/common/ui/label';
import { Checkbox } from '@/components/common/ui/checkbox';
import { Separator } from '@/components/common/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/common/ui/card';
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  FileText, 
  Users, 
  Shield,
  Calendar,
  AlertTriangle,
  Save
} from 'lucide-react';
import { toast } from 'sonner';

interface NotificationPreferences {
  email: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    weeklyDigest: boolean;
  };
  push: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
  };
  inApp: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    roleChanges: boolean;
  };
}

const defaultPreferences: NotificationPreferences = {
  email: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
    weeklyDigest: true,
  },
  push: {
    newMessages: true,
    documentUpdates: false,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
  },
  inApp: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: true,
    systemAlerts: true,
    roleChanges: true,
  },
};

export function NotificationsTab() {
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load preferences on mount
  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      // In a real implementation, this would fetch from your API
      // const response = await api.get('/user/notification-preferences');
      // setPreferences(response.data || defaultPreferences);
      
      // For now, load from localStorage or use defaults
      const saved = localStorage.getItem('notificationPreferences');
      if (saved) {
        setPreferences(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
      toast.error('Failed to load notification preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const updatePreference = (category: keyof NotificationPreferences, key: string, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, this would save to your API
      // await api.put('/user/notification-preferences', preferences);
      
      // For now, save to localStorage
      localStorage.setItem('notificationPreferences', JSON.stringify(preferences));
      
      toast.success('Notification preferences saved successfully');
    } catch (error) {
      console.error('Failed to save notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    setPreferences(defaultPreferences);
    toast.info('Preferences reset to defaults');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  const notificationTypes = [
    {
      key: 'newMessages',
      label: 'New Messages',
      description: 'Notifications when you receive new messages',
      icon: MessageSquare,
    },
    {
      key: 'documentUpdates',
      label: 'Document Updates',
      description: 'When documents are created, updated, or shared',
      icon: FileText,
    },
    {
      key: 'contractChanges',
      label: 'Contract Changes',
      description: 'Updates to contracts and agreements',
      icon: FileText,
    },
    {
      key: 'proposalUpdates',
      label: 'Proposal Updates',
      description: 'Changes to proposals and project updates',
      icon: FileText,
    },
    {
      key: 'userActivity',
      label: 'User Activity',
      description: 'When users join, leave, or change roles',
      icon: Users,
    },
    {
      key: 'systemAlerts',
      label: 'System Alerts',
      description: 'Important system notifications and maintenance',
      icon: AlertTriangle,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Notification Preferences</h3>
          <p className="text-sm text-gray-600">
            Choose how you want to be notified about different activities
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Preferences
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Receive notifications via email
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {notificationTypes.map(({ key, label, description, icon: Icon }) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Icon className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="font-medium">{label}</Label>
                  <p className="text-sm text-gray-500">{description}</p>
                </div>
              </div>
              <Checkbox
                checked={preferences.email[key as keyof typeof preferences.email]}
                onCheckedChange={(checked) => 
                  updatePreference('email', key, checked as boolean)
                }
              />
            </div>
          ))}
          
          {/* Weekly Digest - Email only */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <Label className="font-medium">Weekly Digest</Label>
                <p className="text-sm text-gray-500">Summary of weekly activity</p>
              </div>
            </div>
            <Checkbox
              checked={preferences.email.weeklyDigest}
              onCheckedChange={(checked) => 
                updatePreference('email', 'weeklyDigest', checked as boolean)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Receive push notifications on your device
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {notificationTypes.map(({ key, label, description, icon: Icon }) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Icon className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="font-medium">{label}</Label>
                  <p className="text-sm text-gray-500">{description}</p>
                </div>
              </div>
              <Checkbox
                checked={preferences.push[key as keyof typeof preferences.push]}
                onCheckedChange={(checked) => 
                  updatePreference('push', key, checked as boolean)
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* In-App Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            In-App Notifications
          </CardTitle>
          <CardDescription>
            Notifications shown within the application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {notificationTypes.map(({ key, label, description, icon: Icon }) => (
            <div key={key} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Icon className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="font-medium">{label}</Label>
                  <p className="text-sm text-gray-500">{description}</p>
                </div>
              </div>
              <Checkbox
                checked={preferences.inApp[key as keyof typeof preferences.inApp]}
                onCheckedChange={(checked) => 
                  updatePreference('inApp', key, checked as boolean)
                }
              />
            </div>
          ))}
          
          {/* Role Changes - In-App only */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-4 w-4 text-gray-500" />
              <div>
                <Label className="font-medium">Role Changes</Label>
                <p className="text-sm text-gray-500">When your role or permissions change</p>
              </div>
            </div>
            <Checkbox
              checked={preferences.inApp.roleChanges}
              onCheckedChange={(checked) => 
                updatePreference('inApp', 'roleChanges', checked as boolean)
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Notice */}
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h4 className="font-medium mb-2">Privacy & Data</h4>
        <p className="text-sm text-gray-600">
          Your notification preferences are stored securely and are only used to deliver 
          the notifications you've requested. You can change these settings at any time.
        </p>
      </div>
    </div>
  );
}
