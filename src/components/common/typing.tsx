import { Bouncy } from "ldrs/react";
import "ldrs/react/Bouncy.css";

import { cn } from "@/lib/utils";

interface TypingProps {
  className?: string;
  size?: string;
  speed?: string;
  color?: string;
}

export const Typing = ({
  className,
  size = "45",
  speed = "1.75",
  color = "black",
}: TypingProps) => {
  return (
    <div className={cn("flex flex-row gap-2 items-center", className)}>
      <Bouncy size={size} speed={speed} color={color} />
    </div>
  );
};
