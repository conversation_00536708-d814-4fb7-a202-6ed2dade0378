import type { GridArtProps } from "./types";

export const GridArt = ({ amount = 5 }: GridArtProps) => {
  return (
    <div className="w-full h-full fixed z-0 flex flex-row justify-between xl:px-[3.2rem]">
      {/* [...Array(n).keys => [0,1,2,3,4.. ]] */}
      {[...Array(amount).keys()].map((_, index) => {
        return (
          <span
            key={index}
            className="w-[1px] flex bg-gradient-to-b from-lime-500 via-lime-300/[.15] to-lime-500"
          ></span>
        );
      })}
    </div>
  );
};
