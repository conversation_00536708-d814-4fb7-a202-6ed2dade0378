"use client";

import React, { useState } from "react";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Link, Loader2 } from "lucide-react";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "../ui/combobox";
import { cn } from "@/lib/utils";

import { COMMON_ENTITY_TYPES } from "./entity-types";
import { type UseEntityAssociationReturn } from "@/hooks/useEntityAssociation";

export interface EntityAssociationProps {
  entityAssociation: UseEntityAssociationReturn;
  title?: string;
  description?: string;
  layout?: "vertical" | "horizontal";
  required?: boolean;
  className?: string;
  showIcon?: boolean;
  entityTypeFilter?: string[];
  entityTypePlaceholder?: string;
  entityPlaceholder?: string;
  customEntityRenderer?: (entity: any, entityType: string) => React.ReactNode;
}

export function EntityAssociation({
  entityAssociation,
  title = "Entity Association",
  description,
  layout = "vertical",
  required = false,
  className = "",
  showIcon = true,
  entityTypeFilter,
  entityTypePlaceholder = "Select entity type",
  entityPlaceholder,
  customEntityRenderer,
}: EntityAssociationProps) {
  const { state, handleTableChange, handleIdChange, getEntityDisplayName } =
    entityAssociation;

  const [open, setOpen] = useState(false);

  // Generate dynamic placeholder
  const dynamicPlaceholder =
    entityPlaceholder ||
    (state.selectedTable
      ? `Select ${COMMON_ENTITY_TYPES.find(
          (e) => e.value === state.selectedTable
        )?.label.toLowerCase()}`
      : "Select entity");

  const containerClass =
    layout === "vertical"
      ? "space-y-4"
      : "flex flex-col sm:flex-row sm:items-end sm:space-x-4 sm:space-y-0 space-y-4";

  // Filter entity types if specified
  const availableEntityTypes = entityTypeFilter
    ? COMMON_ENTITY_TYPES.filter((type) =>
        entityTypeFilter.includes(type.value)
      )
    : COMMON_ENTITY_TYPES;

  return (
    <div
      className={cn(
        `flex flex-col sm:flex-row sm:items-start sm:space-x-4 sm:space-y-0 space-y-4`,
        className
      )}
    >
      {/* Entity Type Selection */}
      <div className="flex-1 space-y-2">
        <Label htmlFor="entity-type" className="text-sm font-medium">
          {showIcon && <Link className="w-4 h-4 inline mr-2" />}
          {title}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <Select
          value={state.selectedTable}
          onValueChange={handleTableChange}
          disabled={state.loading}
        >
          <SelectTrigger>
            <SelectValue placeholder={entityTypePlaceholder} />
          </SelectTrigger>
          <SelectContent>
            {availableEntityTypes.map((entityType) => (
              <SelectItem key={entityType.value} value={entityType.value}>
                {entityType.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1 space-y-2" hidden={!state.selectedTable}>
        {/* Entity Selection */}
        <Label htmlFor="entity" className="text-sm font-medium capitalize">
          {state.loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Fetching...
            </>
          ) : (
            <>Select {state.selectedTable}</>
          )}
        </Label>

        <Combobox open={open} onOpenChange={setOpen}>
          <ComboboxTrigger className="w-full">
            <ComboboxInput
              placeholder={state.loading ? "Loading..." : dynamicPlaceholder}
              className="w-full"
            />
          </ComboboxTrigger>
          <ComboboxContent>
            <ComboboxCommand>
              <ComboboxList>
                <ComboboxEmpty>No entities found</ComboboxEmpty>
                <ComboboxGroup>
                  {state.entities.map((entity) => {
                    // Create a serializable version to prevent Redux errors
                    const safeEntity = {
                      id: String(entity.id || ""),
                      name: String(entity.name || entity.label || ""),
                      ...Object.fromEntries(
                        Object.entries(entity).filter(
                          ([, value]) =>
                            typeof value === "string" ||
                            typeof value === "number" ||
                            typeof value === "boolean" ||
                            value === null
                        )
                      ),
                    };

                    return (
                      <ComboboxItem
                        key={safeEntity.id}
                        value={safeEntity.id}
                        onSelect={(currentValue) => {
                          handleIdChange(
                            currentValue === state.selectedId
                              ? ""
                              : currentValue
                          );
                          setOpen(false);
                        }}
                      >
                        <ComboboxItemIndicator
                          isSelected={state.selectedId === safeEntity.id}
                        />
                        {customEntityRenderer ? (
                          customEntityRenderer(safeEntity, state.selectedTable)
                        ) : (
                          <span className="truncate">
                            {getEntityDisplayName(
                              safeEntity,
                              state.selectedTable
                            )}
                          </span>
                        )}
                      </ComboboxItem>
                    );
                  })}
                </ComboboxGroup>
              </ComboboxList>
            </ComboboxCommand>
          </ComboboxContent>
        </Combobox>
      </div>

      {/* Error State */}
      {state.error && (
        <div className="text-red-500 text-sm mt-2">{state.error}</div>
      )}
    </div>
  );
}
