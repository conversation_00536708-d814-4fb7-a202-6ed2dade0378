/**
 * Entity Types Configuration
 *
 * This file defines the supported entity types for the EntityAssociationViewer component.
 * It provides type definitions, service mappings, and utility functions for handling
 * different entity types in the system.
 *
 * Supported Entity Types:
 * - Proposals: Business proposals and quotes
 * - Contracts: Business contracts and agreements
 * - Documents: Document management and storage
 * - Constituante: Constituante entity management
 */

import { api } from "@/lib/common/requests";

export interface EntityTypeOption {
  value: string;
  name: string;
  category: "finance" | "system";
  description?: string;
}

export interface EntityInstance {
  id: string;
  name: string;
  description?: string;
  metadata?: any;
}

export interface EntityFetchResponse {
  success: boolean;
  data: EntityInstance[];
  error?: string;
}

// Core Business Entities
export const BUSINESS_ENTITIES: EntityTypeOption[] = [
  {
    value: "proposals",
    name: "Proposals",
    category: "finance",
    description: "Business proposals and quotes",
  },
  {
    value: "contracts",
    name: "Contracts",
    category: "finance",
    description: "Business contracts and agreements",
  },
  {
    value: "documents",
    name: "Documents",
    category: "system",
    description: "Document management and storage",
  },
];

// Combined Entity Types (All entities)
export const ALL_ENTITY_TYPES: EntityTypeOption[] = [...BUSINESS_ENTITIES];

// Entity Types by Category
export const ENTITY_TYPES_BY_CATEGORY = {
  finance: BUSINESS_ENTITIES.filter((e) => e.category === "finance"),
  system: BUSINESS_ENTITIES.filter((e) => e.category === "system"),
} as const;

// Most commonly used entities for general selectors
export const COMMON_ENTITY_TYPES: EntityTypeOption[] = [...BUSINESS_ENTITIES];

// Legacy format for backward compatibility
export const ENTITY_TYPES = ALL_ENTITY_TYPES.map((entity) => ({
  value: entity.value,
  name: entity.name,
}));

// Helper functions
export const getEntityTypeByValue = (
  value: string
): EntityTypeOption | undefined => {
  return ALL_ENTITY_TYPES.find((entity) => entity.value === value);
};

export const getEntitiesByCategory = (
  category: EntityTypeOption["category"]
): EntityTypeOption[] => {
  return ALL_ENTITY_TYPES.filter((entity) => entity.category === category);
};

/**
 * Check if an entity type is supported by the system
 *
 * @param entityType - The entity type to check
 * @returns boolean - Whether the entity type is supported
 */
export const isEntityTypeSupported = (entityType: string): boolean => {
  const supportedTypes = [
    "proposals",
    "contracts",
    "documents",
    "constituante",
  ];
  return supportedTypes.includes(entityType);
};

/**
 * Get the appropriate service method name for an entity type
 *
 * @param entityType - The entity type
 * @returns string - The service method name used for fetching
 */
export const getEntityServiceMethod = (entityType: string): string => {
  const methodMap: Record<string, string> = {
    proposals: "getAll",
    contracts: "getAll",
    documents: "getAll",
    constituante: "getAll",
  };
  return methodMap[entityType] || "getAll";
};

/**
 * Fetch entity instances for a given entity type using real API calls
 *
 * @param entityType - The entity type to fetch
 * @param options - Fetch options like limit
 * @returns Promise<EntityFetchResponse> - The fetch response
 */
export const fetchEntityInstances = async (
  entityType: string,
  options: { limit?: number } = {}
): Promise<EntityFetchResponse> => {
  try {
    // Map entity types to their API endpoints
    const endpointMap: Record<string, string> = {
      proposals: "proposal",
      contracts: "contract/get", // Assuming this endpoint exists
      documents: "document",
      constituante: "constituante/get", // New constituante endpoint
    };

    const endpoint = endpointMap[entityType];
    if (!endpoint) {
      throw new Error(`Unsupported entity type: ${entityType}`);
    }

    // Build query parameters
    const params: Record<string, any> = {};
    if (options.limit) {
      params.limit = options.limit;
    }

    // Make API call
    const response = await api.get(endpoint, params);

    // Handle different response formats
    let entities: any[] = [];
    if (response.success && response.data) {
      entities = Array.isArray(response.data) ? response.data : [response.data];
    } else if (Array.isArray(response)) {
      entities = response;
    } else if (response.docs) {
      // Handle Payload CMS format
      entities = response.docs;
    }

    // Transform API response to EntityInstance format
    const transformedEntities: EntityInstance[] = entities.map(
      (entity: any) => ({
        id: entity.id || entity._id || String(entity.key || Math.random()),
        name:
          entity.name ||
          entity.title ||
          entity.filename ||
          `${entityType} ${entity.id}`,
        description:
          entity.description || entity.caption || entity.summary || "",
        metadata: {
          status: entity.status,
          ...entity.metadata,
          // Include other relevant fields based on entity type
          ...(entityType === "proposals" && {
            budget: entity.fixed_budget || entity.total_budget,
            client: entity.client_name,
          }),
          ...(entityType === "contracts" && {
            value: entity.value || entity.contract_value,
            client: entity.client_name,
          }),
          ...(entityType === "documents" && {
            type: entity.file_type || entity.category,
            size: entity.size,
          }),
          ...(entityType === "constituante" && {
            // Add constituante-specific metadata fields here
            type: entity.type,
            category: entity.category,
          }),
        },
      })
    );

    return {
      success: true,
      data: transformedEntities,
    };
  } catch (error: any) {
    console.error(`Error fetching ${entityType}:`, error);
    return {
      success: false,
      data: [],
      error: error.message || `Failed to fetch ${entityType}`,
    };
  }
};
