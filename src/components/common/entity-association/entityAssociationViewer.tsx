"use client";

import React, { useCallback } from "react";
import { Badge } from "@/components/common/ui/badge";
import { Loader2, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/common/ui/button";
import { COMMON_ENTITY_TYPES, type EntityInstance } from "./entity-types";

export interface EntityAssociationViewerProps {
  /**
   * The entity table/type
   */
  entityTable: string | null;

  /**
   * Single entity ID or array of entity IDs
   */
  entityId: string | string[] | null;

  /**
   * Multiple entity ID or array of entity IDs
   */
  entityIds?: string[] | null;

  /**
   * Display variant
   */
  variant?: "default" | "compact" | "detailed";

  /**
   * Whether to show the entity type badge
   */
  showEntityType?: boolean;

  /**
   * Whether to show a link to view the entity (if applicable)
   */
  showViewLink?: boolean;

  /**
   * Custom class name
   */
  className?: string;

  /**
   * Callback when view link is clicked
   */
  onViewEntity?: (entityId: string, entityTable: string) => void;

  /**
   * Custom entity renderer
   */
  customRenderer?: (entity: any, entityType: string) => React.ReactNode;

  /**
   * Maximum number of entities to display (for arrays)
   */
  maxDisplay?: number;

  /**
   * Whether to show a "show more" button for large arrays
   */
  showExpandButton?: boolean;

  /**
   * Sort entities by name
   */
  sortBy?: "name";

  /**
   * Sort direction
   */
  sortDirection?: "asc" | "desc";
}

interface EntityDisplayData {
  id: string;
  name: string;
  metadata?: any;
}

export function EntityAssociationViewer({
  entityTable,
  entityId,
  entityIds,
  variant = "default",
  showEntityType = true,
  showViewLink = false,
  className = "",
  onViewEntity,
  customRenderer,
  maxDisplay = 10,
  showExpandButton = true,
  sortBy = "name",
  sortDirection = "asc",
}: EntityAssociationViewerProps) {
  const [entities, setEntities] = React.useState<EntityDisplayData[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Get entity type label
  const entityTypeLabel = React.useMemo(() => {
    if (!entityTable) return null;
    return (
      COMMON_ENTITY_TYPES.find((type) => type.value === entityTable)?.label ||
      entityTable
    );
  }, [entityTable]);

  const fetchEntityData = useCallback(async () => {
    if (!entityTable) {
      setEntities([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Properly flatten and filter out null/undefined values to ensure we have string[]
      const ids = entityIds
        ? [entityIds, entityId].flat().filter((id): id is string => Boolean(id))
        : Array.isArray(entityId)
        ? entityId.filter((id): id is string => Boolean(id))
        : entityId
        ? [entityId]
        : [];

      // Early return if no IDs to fetch
      if (ids.length === 0) {
        setEntities([]);
        return;
      }

      // Map entity table names to bulk fetch service entity types
      const entityTypeMap: Record<string, string> = {
        proposals: "proposals",
        contracts: "contracts",
        documents: "documents",
      };

      const mappedEntityType = entityTypeMap[entityTable] || entityTable;

      // For now, just set empty entities since we don't have actual service integration
      // This is a placeholder for future service integration
      const supportedTypes = ["proposals", "contracts", "documents"];

      if (supportedTypes.includes(mappedEntityType)) {
        // Placeholder: In a real implementation, you would fetch entities here
        setEntities([]);
      } else {
        setError(`Entity type "${entityTable}" is not supported`);
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch entity data");
    } finally {
      setLoading(false);
    }
  }, [entityTable, entityId, entityIds]);

  // Fetch entity data
  React.useEffect(() => {
    fetchEntityData();
  }, [entityTable, entityId, entityIds]);

  // Sort and filter entities for display
  const displayEntities = React.useMemo(() => {
    if (!entities.length) return [];

    // Sort entities by name
    const sorted = [...entities].sort((a, b) => {
      const aValue = a.name || "";
      const bValue = b.name || "";

      const comparison = aValue.localeCompare(bValue);
      return sortDirection === "asc" ? comparison : -comparison;
    });

    // Limit display if not expanded
    if (!isExpanded && sorted.length > maxDisplay) {
      return sorted.slice(0, maxDisplay);
    }

    return sorted;
  }, [entities, sortBy, sortDirection, isExpanded, maxDisplay]);

  // Handle view entity click
  const handleViewEntity = (entityId: string) => {
    if (onViewEntity && entityTable) {
      onViewEntity(entityId, entityTable);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`text-sm text-red-500 ${className}`}>Error: {error}</div>
    );
  }

  // Render empty state
  if (!entityTable || (!entityId && !entityIds) || entities.length === 0) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>No association</div>
    );
  }

  // Render single entity
  if (entities.length === 1) {
    const entity = displayEntities[0];

    if (!entity) {
      return (
        <div className={`text-sm text-gray-500 ${className}`}>
          Entity not found
        </div>
      );
    }

    if (customRenderer) {
      return (
        <div className={className}>{customRenderer(entity, entityTable)}</div>
      );
    }

    return (
      <div className={`flex items-start gap-3 ${className}`}>
        {showEntityType && entityTypeLabel && (
          <Badge variant="secondary" className="text-xs">
            {entityTypeLabel}
          </Badge>
        )}

        <div className="flex flex-col flex-1 min-w-0">
          <span className="text-sm font-medium truncate">{entity.name}</span>
        </div>

        {showViewLink && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 flex-shrink-0"
            onClick={() => handleViewEntity(entity.id)}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  // Render multiple entities
  return (
    <div className={`space-y-2 ${className}`}>
      {showEntityType && entityTypeLabel && (
        <Badge variant="secondary" className="text-xs capitalize">
          {entityTypeLabel} ({entities.length})
        </Badge>
      )}

      <div className="space-y-3 flex flex-col h-[17em] overflow-y-auto">
        {displayEntities.map((entity) => (
          <div
            key={entity.id}
            className="flex items-start gap-2 p-2 border rounded-lg bg-gray-100"
          >
            {customRenderer ? (
              customRenderer(entity, entityTable)
            ) : (
              <>
                <div className="flex flex-col flex-1 min-w-0">
                  <span className="text-sm font-medium truncate capitalize">
                    {entity.name}
                  </span>
                </div>

                {showViewLink && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 flex-shrink-0"
                    onClick={() => handleViewEntity(entity.id)}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                )}
              </>
            )}
          </div>
        ))}
      </div>

      {/* Show More/Less Button */}
      {showExpandButton && entities.length > maxDisplay && (
        <div className="flex justify-center pt-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? (
              <>Show Less ({entities.length - maxDisplay} hidden)</>
            ) : (
              <>Show More ({entities.length - maxDisplay} more)</>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
