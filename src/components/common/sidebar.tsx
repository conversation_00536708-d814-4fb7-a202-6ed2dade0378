import Image from "next/image";

export const SideBarBackdrop = ({ className }: { className?: string }) => {
  return (
    <div className="w-max xl:max-w-xl h-screen flex flex-row relative">
      <div className="w-auto relative">
        <Image
          className="h-full object-cover"
          width={440}
          height={900}
          src="/brand/auth-backdrop.png"
          alt="tenderbank sidebar"
        />
        {/* Overlay */}
        <div className="absolute top-0 left-0 w-full h-full bg-black/20" />
        {/* Logo */}
        <div>
          <Image
            className="w-auto h-max absolute top-8 left-8"
            src="/brand/white-logo.png"
            width={344}
            height={52}
            alt="tenderbank sidebar"
          />
        </div>
      </div>
      {/* Ribbon */}
      <div className="w-auto h-full">
        <Image
          className="h-full object-cover"
          src="/brand/ribbon.svg"
          width={8}
          height={900}
          alt="tenderbank sidebar"
        />
      </div>
    </div>
  );
};
