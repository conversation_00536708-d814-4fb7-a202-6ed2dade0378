"use client";

import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Card, CardContent } from "@/components/common/ui/card";

import { IoRemoveOutline as Outline } from "react-icons/io5";
import { HiOutlineArrowSmallRight as RightArrow } from "react-icons/hi2";
import { Send } from "lucide-react";
import type { FooterProps } from "./types";

// Newsletter Subscription Component using shadcn
const NewsletterSubscription = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { api } = await import("@/lib/common/requests");

      await api.post("subscribe", {
        email,
        interest: ["newsletter"],
      });

      setEmail("");
      alert("Subscribed successfully!");
    } catch (error) {
      alert("Error subscribing");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full flex flex-col gap-4">
      <div className="relative">
        <Input
          type="email"
          size="xl"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Join to our weekly newsletter"
          required
        />
      </div>
      <Button type="submit" disabled={loading}>
        {loading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-navy" />
            Subscribing...
          </>
        ) : (
          <>
            <Send className="h-4 w-4" />
            Subscribe
          </>
        )}
      </Button>
    </form>
  );
};

export const Footer = ({}: FooterProps) => {
  const year = new Date().getFullYear();

  return (
    <footer>
      <span className="globe"></span>
      <section>
        {/* Directive */}
        <span className="w-full flex flex-row justify-start items-center gap-4 mb-8">
          <RightArrow size={30} className="text-white" />
          <h6 className="text-base md:text-lg lg:text-xl font-light capitalize">
            lets connect
          </h6>
        </span>
        {/* Convincing */}
        <span className="w-full flex flex-col gap-8 md:gap-12 xl:gap-0 xl:flex-row xl:justify-between xl:gap-12 2xl:gap-0 xl:items-start">
          {/* Question */}
          <span className="w-full">
            <h3 className="text-3xl md:text-4xl lg:text-5xl font-medium inline-block">
              Have a project in mind? <br /> Lets make great stuff <br />{" "}
              together
            </h3>
          </span>
          {/* Call to action */}
          <div className="w-full flex flex-col items-start gap-6 md:gap-8 xl:gap-8">
            <NewsletterSubscription />
            <h5 className="text-lg md:text-xl lg:text-2xl font-light">
              Our team will review your submission <br /> and get back to you as
              soon as possible
            </h5>
            <Button
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-navy gap-3"
              asChild
            >
              <Link href="mailto:<EMAIL>?subject=Inquiry%20From%20Client">
                <span className="text-base font-medium">Get a quote</span>
                <RightArrow className="mt-[2px]" size={18} />
              </Link>
            </Button>
          </div>
        </span>
      </section>

      {/* Contacts */}
      <section className="py-4">
        <Contacts />
      </section>

      <section className="w-full">
        {/* Footer */}
        <div className="w-full flex flex-col gap-4 lg:gap-12 items-center xl:items-end xl:gap-0 xl:flex-row xl:justify-between">
          <Button
            variant="link"
            className="text-balance text-sm font-medium text-white/80 hover:text-white p-0 h-auto"
            asChild
          >
            <Link href="">{year} &#169; Underscor. All rights reserved</Link>
          </Button>

          <img
            className="w-full max-w-52 lg:max-w-96 py-8 lg:py-0"
            src="/svg/underscor.svg"
            width="280"
            alt="Underscor logo"
          />

          <div className="gap-2 flex flex-row justify-center items-center">
            <Button
              variant="link"
              className="text-sm font-medium text-white/80 hover:text-lime-accent p-0 h-auto"
              asChild
            >
              <Link href="">Privacy policy</Link>
            </Button>

            <div className="rotate-90 text-white/40">
              <Outline size={25} />
            </div>

            <Button
              variant="link"
              className="text-sm font-medium text-white/80 hover:text-lime-accent p-0 h-auto"
              asChild
            >
              <Link href="">Terms of service</Link>
            </Button>
          </div>
        </div>
      </section>
    </footer>
  );
};

const Contacts = () => {
  const contacts = [
    {
      title: "Email",
      email: "<EMAIL>",
      url: "mailto:<EMAIL>?subject=New Client Inquiry",
    },
    {
      title: "linkedin",
      email: "underscor",
      url: "https://www.linkedin.com/company/underscor-io/?viewAsMember=true",
    },
    {
      title: "upwork",
      email: "@underscor",
      url: "https://www.upwork.com/agencies/1655373505334222848/",
    },
    {
      title: "instagram",
      email: "@underscor.io",
      url: "https://www.instagram.com/underscor.io",
    },
  ];

  return (
    <div className="w-full grid grid-cols-1 xl:grid-cols-4 gap-0">
      {contacts.map((contact, index) => {
        const { title, email, url } = contact;
        return (
          <Card
            key={index}
            className="rounded-none border-0 border-l xl:border-l-0 xl:border-r border-b border-zinc-700 bg-transparent shadow-none hover:bg-zinc-900/20 transition-colors"
          >
            <CardContent className="p-6 xl:py-8 xl:px-16">
              <Link
                href={url ?? ""}
                target="_blank"
                className="w-full h-auto p-0 flex flex-col items-start gap-2 justify-start hover:bg-transparent"
              >
                <h6 className="text-base md:text-lg lg:text-xl font-light capitalize text-white">
                  {title}
                </h6>
                <p className="text-base font-medium text-white/80 hover:text-lime-accent transition-colors">
                  {email}
                </p>
              </Link>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
