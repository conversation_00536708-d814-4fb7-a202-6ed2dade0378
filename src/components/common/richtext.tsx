"use client";

import React, { useEffect } from "react";
import dynamic from "next/dynamic";
import { cn } from "@/lib/utils";
const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import { Quill } from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";
import "react-quill-new/dist/quill.bubble.css";

const modules: any = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    ["bold", "italic", "underline"],
    [{ list: "ordered" }, { list: "bullet" }],
    [{ indent: "-1" }, { indent: "+1" }],
    ["link", "blockquote", "image"],
    ["clean"],
  ],
  history: {
    delay: 1000,
    maxStack: 500,
    userOnly: true,
  },
  clipboard: {
    matchVisual: false,
  },
};

const formats = [
  "header",
  "bold",
  "italic",
  "underline",
  "strike",
  "blockquote",
  "list", // 'list' is the main format, 'bullet' and 'ordered' are values
  "indent",
  "link",
  "image",
];

export default function RichTextEditor({
  id,
  value,
  theme = "snow",
  onChange,
  className,
  placeholder,
  ...props
}: {
  id: string;
  value: string;
  theme: "snow" | "bubble";
  className?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  [x: string]: any;
}) {
  return (
    <ReactQuill
      id={id}
      className={cn("w-full max-h-[40em]", className)}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      theme={theme}
      style={{ resize: "vertical" }}
      modules={modules}
      formats={formats}
      preserveWhitespace
      {...props}
    />
  );
}
