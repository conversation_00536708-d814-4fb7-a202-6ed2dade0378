"use client";

import { SidebarLayout } from "@/components/common/layout/SidebarLayout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

export function SidebarExample() {
  return (
    <SidebarLayout>
      <div className="grid auto-rows-min gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Home</CardTitle>
            <CardDescription>Welcome to your dashboard</CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              This is the home page content. The sidebar provides easy
              navigation to all your important sections.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Proposals</CardTitle>
            <CardDescription>Manage your project proposals</CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              View, create, and manage all your project proposals in one place.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
            <CardDescription>Access your files and documents</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Store and organize all your project documents and files.</p>
          </CardContent>
        </Card>
      </div>

      <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
        <div className="p-8">
          <h2 className="text-2xl font-bold mb-4">Sidebar Features</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Navigation Links</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Home - Dashboard overview</li>
                <li>Proposals - Project proposal management</li>
                <li>Documents - File and document storage</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold">Footer Section</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Profile - User profile with avatar and name</li>
                <li>Settings - Application settings and preferences</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold">Features</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Collapsible sidebar with icon-only mode</li>
                <li>Mobile-responsive with sheet overlay</li>
                <li>Active state highlighting for current page</li>
                <li>Tooltips when collapsed</li>
                <li>User authentication state handling</li>
                <li>Keyboard shortcut (Cmd/Ctrl + B) to toggle</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </SidebarLayout>
  );
}
