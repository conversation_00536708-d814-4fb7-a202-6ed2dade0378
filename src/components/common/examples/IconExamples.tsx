/**
 * Icon Examples Component
 *
 * This component demonstrates proper usage of react-icons
 * throughout the application. Use this as a reference for
 * consistent icon implementation.
 */

import {
  FaGoogle,
  FaLinkedin,
  FaFacebook,
  FaTwitter,
  FaGithub,
  FaUser,
  FaEnvelope,
  FaLock,
  FaEye,
  FaEyeSlash,
  FaHome,
  FaBars,
  FaTimes,
  FaChevronLeft,
  FaChevronRight,
  FaPlus,
  FaEdit,
  FaTrash,
  FaSave,
  FaCheck,
  FaExclamation,
  FaInfo,
  FaSpinner,
  FaBell,
} from "react-icons/fa";

import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

export function IconExamples() {
  return (
    <div className="space-y-8 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Social Authentication Icons</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button variant="outline">
              <FaGoogle className="mr-2 h-4 w-4" />
              Google
            </Button>
            <Button variant="outline">
              <FaLinkedin className="mr-2 h-4 w-4" />
              LinkedIn
            </Button>
            <Button variant="outline">
              <FaFacebook className="mr-2 h-4 w-4" />
              Facebook
            </Button>
            <Button variant="outline">
              <FaTwitter className="mr-2 h-4 w-4" />
              Twitter
            </Button>
            <Button variant="outline">
              <FaGithub className="mr-2 h-4 w-4" />
              GitHub
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Form Input Icons</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="relative">
              <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input placeholder="Username" className="pl-10" />
            </div>
            <div className="relative">
              <FaEnvelope className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input placeholder="Email" className="pl-10" />
            </div>
            <div className="relative">
              <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                type="password"
                placeholder="Password"
                className="pl-10 pr-10"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              >
                <FaEye className="h-4 w-4 text-muted-foreground" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Navigation Icons</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <FaHome className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <FaBars className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <FaTimes className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <FaChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <FaChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Action Icons</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <FaPlus className="mr-2 h-4 w-4" />
              Add
            </Button>
            <Button variant="outline" size="sm">
              <FaEdit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button variant="outline" size="sm">
              <FaTrash className="mr-2 h-4 w-4" />
              Delete
            </Button>
            <Button variant="outline" size="sm">
              <FaSave className="mr-2 h-4 w-4" />
              Save
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Status Icons</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <FaCheck className="h-4 w-4 text-green-500" />
              <span>Success message</span>
            </div>
            <div className="flex items-center gap-2">
              <FaTimes className="h-4 w-4 text-red-500" />
              <span>Error message</span>
            </div>
            <div className="flex items-center gap-2">
              <FaExclamation className="h-4 w-4 text-yellow-500" />
              <span>Warning message</span>
            </div>
            <div className="flex items-center gap-2">
              <FaInfo className="h-4 w-4 text-blue-500" />
              <span>Information message</span>
            </div>
            <div className="flex items-center gap-2">
              <FaSpinner className="h-4 w-4 animate-spin text-gray-500" />
              <span>Loading...</span>
            </div>
            <div className="flex items-center gap-2">
              <FaBell className="h-4 w-4 text-gray-500" />
              <span>Notification</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Icon Sizes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <FaUser className="h-3 w-3" title="Small (h-3 w-3)" />
            <FaUser className="h-4 w-4" title="Default (h-4 w-4)" />
            <FaUser className="h-5 w-5" title="Medium (h-5 w-5)" />
            <FaUser className="h-6 w-6" title="Large (h-6 w-6)" />
            <FaUser className="h-8 w-8" title="Extra Large (h-8 w-8)" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
