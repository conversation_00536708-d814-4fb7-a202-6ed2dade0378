import { cn } from "@/lib/utils";
import type { HeadingProps } from "./types";

export const Heading = ({ tagline, title, className }: HeadingProps) => {
  return (
    <div className={cn("flex flex-col gap-2", className)}>
      {tagline && (
        <p className="text-sm font-bold uppercase text-lime-bright">
          {tagline}
        </p>
      )}
      {title && (
        <h5 className="text-2xl md:text-3xl lg:text-4xl text-white font-light capitalize">
          {title}
        </h5>
      )}
    </div>
  );
};
