"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/common/ui/card";

import { cn } from "@/lib/utils";
import type { ListTabProps, RowTabProps } from "./types";

const transition = {
  duration: 0.4,
  ease: "circInOut",
};

const getThemeClasses = (theme: string = "primary") => {
  const themes = {
    primary:
      "bg-gradient-to-r from-primary to-primary/60 hover:border-r-2 hover:border-primary/50",
    secondary:
      "bg-gradient-to-r from-secondary to-secondary/60 hover:border-r-2 hover:border-secondary/50",
    muted:
      "bg-gradient-to-r from-muted to-muted/60 hover:border-r-2 hover:border-muted-foreground/50",
    accent:
      "bg-gradient-to-r from-accent to-accent/60 hover:border-r-2 hover:border-accent-foreground/50",
    outline:
      "border border-border hover:border-primary/50 duration-300 bg-primary/10",
    // Legacy support
    lime: "bg-gradient-to-r from-primary to-primary/60 hover:border-r-2 hover:border-primary/50",
    navy: "bg-gradient-to-r from-secondary to-secondary/60 hover:border-r-2 hover:border-secondary/50",
    gray: "bg-gradient-to-r from-muted to-muted/60 hover:border-r-2 hover:border-muted-foreground/50",
    black: "bg-background hover:border-r-2 hover:border-primary/50",
  };
  return themes[theme as keyof typeof themes] || themes.primary;
};

export const ListTab = ({
  title = "Tab",
  caption = "",
  icon,
  style = { width: "w-full", theme: "primary" },
  position = "start",
}: ListTabProps) => {
  const [hover, setHover] = useState(false);

  return (
    <div className={cn("w-full flex", `justify-${position}`)}>
      <Card
        className={cn(
          style?.width || "w-full",
          getThemeClasses(style?.theme),
          "cursor-pointer transition-all duration-300 border-0 rounded-none overflow-hidden"
        )}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        <CardHeader className="pb-2">
          <CardTitle
            className={cn(
              "text-lg font-medium",
              style?.theme === "outline" && hover
                ? "text-primary"
                : "text-primary-foreground"
            )}
          >
            {title}
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-0">
          <motion.div
            animate={hover ? "expand" : "collapse"}
            variants={{
              expand: {
                height: "auto",
                opacity: 1,
              },
              collapse: {
                height: 0,
                opacity: 0,
              },
            }}
            transition={transition}
            className="overflow-hidden"
          >
            {caption && (
              <p className="text-muted-foreground leading-relaxed mb-4">
                {caption}
              </p>
            )}
            {icon && (
              <img
                src={`/svg/prisms/service/${icon}.svg`}
                width="70"
                height="70"
                alt=""
                className="mt-4"
              />
            )}
          </motion.div>
        </CardContent>
      </Card>
    </div>
  );
};

const getRowThemeClasses = (theme: string = "primary") => {
  const themes = {
    primary: "bg-primary",
    secondary: "bg-secondary",
    muted: "bg-muted",
    accent: "bg-accent",
    // Legacy support
    lime: "bg-primary",
    blue: "bg-secondary",
    navy: "bg-secondary",
    gray: "bg-muted",
    black: "bg-background",
  };
  return themes[theme as keyof typeof themes] || themes.primary;
};

export const RowTab = ({
  title = "Tab",
  caption = "",
  style = "primary",
}: RowTabProps) => {
  const [hover, setHover] = useState(false);

  return (
    <Card
      className={cn(
        "w-full h-full rounded-none border-0 cursor-pointer overflow-hidden",
        getRowThemeClasses(style)
      )}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <CardContent className="p-6 h-full flex flex-col lg:flex-row justify-between lg:items-center">
        <CardTitle className="w-full lg:w-1/2 text-primary-foreground capitalize text-lg font-medium">
          {title}
        </CardTitle>

        <motion.div
          animate={hover ? "expand" : "collapse"}
          variants={{
            expand: {
              height: "12rem",
            },
            collapse: {
              height: 0,
            },
          }}
          transition={transition}
          className="w-full lg:w-1/2 h-full flex flex-col justify-end items-end overflow-hidden"
        >
          <motion.p
            animate={hover ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 1, ease: "easeInOut" }}
            className={cn(
              "text-muted-foreground",
              caption ? "block" : "hidden"
            )}
          >
            {caption}
          </motion.p>
        </motion.div>
      </CardContent>
    </Card>
  );
};
