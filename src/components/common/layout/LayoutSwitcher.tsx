"use client";

import React from "react";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/common/ui/dropdown-menu";
import { 
  Settings, 
  Layout, 
  Zap, 
  Layers,
  Check,
  Search,
  Filter,
  Grid3X3,
  Table,
  List,
} from "lucide-react";
import { useLayout } from "@/hooks/useLayout";

interface LayoutSwitcherProps {
  className?: string;
  showLabel?: boolean;
  variant?: "button" | "dropdown";
}

export function LayoutSwitcher({ 
  className = "", 
  showLabel = false,
  variant = "dropdown" 
}: LayoutSwitcherProps) {
  const { 
    layoutType, 
    isBasic, 
    isAdvanced, 
    capabilities,
    switchToBasic, 
    switchToAdvanced, 
    toggleLayout 
  } = useLayout();

  if (variant === "button") {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showLabel && (
          <span className="text-sm text-muted-foreground">Layout:</span>
        )}
        <div className="flex gap-1 border rounded-md p-1">
          <Button
            variant={isBasic ? "default" : "ghost"}
            size="sm"
            onClick={switchToBasic}
            className="px-3"
          >
            <Layout size={16} />
            {showLabel && <span className="ml-2">Basic</span>}
          </Button>
          <Button
            variant={isAdvanced ? "default" : "ghost"}
            size="sm"
            onClick={switchToAdvanced}
            className="px-3"
          >
            <Zap size={16} />
            {showLabel && <span className="ml-2">Advanced</span>}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={`gap-2 ${className}`}>
          {isBasic ? <Layout size={16} /> : <Zap size={16} />}
          {showLabel && (
            <span className="hidden sm:inline">
              {isBasic ? "Basic" : "Advanced"} Layout
            </span>
          )}
          <Badge variant="secondary" className="ml-1">
            {layoutType}
          </Badge>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Layers size={16} />
          Layout Options
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Basic Layout Option */}
        <DropdownMenuItem onClick={switchToBasic} className="flex-col items-start p-4">
          <div className="flex items-center justify-between w-full mb-2">
            <div className="flex items-center gap-2">
              <Layout size={16} />
              <span className="font-medium">Basic Layout</span>
            </div>
            {isBasic && <Check size={16} className="text-green-600" />}
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Simple and clean interface with separate header, filters, and controls.
          </p>
          <div className="flex flex-wrap gap-1">
            <Badge variant="outline" className="text-xs">Header</Badge>
            <Badge variant="outline" className="text-xs">Filters</Badge>
            <Badge variant="outline" className="text-xs">Controls</Badge>
            <Badge variant="outline" className="text-xs">Table/Cards</Badge>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Advanced Layout Option */}
        <DropdownMenuItem onClick={switchToAdvanced} className="flex-col items-start p-4">
          <div className="flex items-center justify-between w-full mb-2">
            <div className="flex items-center gap-2">
              <Zap size={16} />
              <span className="font-medium">Advanced Layout</span>
            </div>
            {isAdvanced && <Check size={16} className="text-green-600" />}
          </div>
          <p className="text-sm text-muted-foreground mb-2">
            Feature-rich interface with integrated toolbar, dynamic icons, and multiple view modes.
          </p>
          <div className="flex flex-wrap gap-1">
            <Badge variant="outline" className="text-xs">
              <Search size={12} className="mr-1" />
              Search
            </Badge>
            <Badge variant="outline" className="text-xs">
              <Filter size={12} className="mr-1" />
              Filters
            </Badge>
            <Badge variant="outline" className="text-xs">
              <Table size={12} className="mr-1" />
              Views
            </Badge>
            <Badge variant="outline" className="text-xs">Dynamic Icons</Badge>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Current Layout Capabilities */}
        <div className="p-3">
          <div className="text-sm font-medium mb-2">Current Capabilities:</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className={`flex items-center gap-1 ${capabilities.hasToolbar ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasToolbar ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              Toolbar
            </div>
            <div className={`flex items-center gap-1 ${capabilities.hasDynamicIcons ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasDynamicIcons ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              Dynamic Icons
            </div>
            <div className={`flex items-center gap-1 ${capabilities.hasAdvancedFilters ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasAdvancedFilters ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              Advanced Filters
            </div>
            <div className={`flex items-center gap-1 ${capabilities.hasViewModeToggle ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasViewModeToggle ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              View Toggle
            </div>
            <div className={`flex items-center gap-1 ${capabilities.hasHeader ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasHeader ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              Header
            </div>
            <div className={`flex items-center gap-1 ${capabilities.hasControls ? 'text-green-600' : 'text-muted-foreground'}`}>
              <div className={`w-2 h-2 rounded-full ${capabilities.hasControls ? 'bg-green-600' : 'bg-muted-foreground'}`} />
              Controls
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default LayoutSwitcher;
