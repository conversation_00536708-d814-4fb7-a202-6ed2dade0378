"use client";

import * as React from "react";
import Link from "next/link";
import { Brand } from "@/components/common/logo";
import { useRouter, usePathname } from "next/navigation";
import { FaUser } from "react-icons/fa";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";

import { useAuth } from "@/hooks/useAuth";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/common/ui/sidebar";

// Navigation items - Template generic navigation
const navigationItems = [
  {
    title: "Home",
    url: "home",
    icon: LottieIconLib.contract,
  },
  {
    title: "Analytics",
    url: "analytics",
    icon: LottieIconLib.file,
  },
  {
    title: "Subscriptions",
    url: "subscriptions",
    icon: LottieIconLib.folder,
  },
  {
    title: "Do it for me",
    url: "requests",
    icon: LottieIconLib.chat,
  },
];

// Footer items
const footerItems = [
  {
    title: "Settings",
    url: "settings",
    icon: LottieIconLib.settings,
  },
  {
    title: "Exit",
    url: "/",
    icon: LottieIconLib.exit,
    action: "exitDashboardView",
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const router = useRouter();

  const pathname = usePathname();
  const { user, isAuthenticated, exitDashboardView } = useAuth();

  function handleExtraActions(action: string) {
    switch (action) {
      case "exitDashboardView":
        exitDashboardView();
        break;
      default:
        break;
    }
  }

  function isActive(url: string): boolean {
    return pathname === url;
  }

  function handleNavigation(url: string) {
    if (url !== "") return;
    router.push(url);
  }

  // Get user initials for avatar fallback
  const getUserInitials = (
    firstName?: string | null | undefined,
    lastName?: string | null | undefined,
    email?: string | null | undefined
  ) => {
    if (firstName) {
      const initials = `${firstName[0] || ""}${lastName?.[0] || ""}`;
      return initials.toUpperCase().slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return "U";
  };

  // Get user display name
  const getUserDisplayName = (
    firstName?: string | null | undefined,
    lastName?: string | null | undefined
  ) => {
    if (firstName) {
      return `${firstName} ${lastName || ""}`.trim();
    }
    return "User";
  };

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader className="flex flex-col items-start gap-6 p-4">
        <Brand />
        <div className="grid flex-1 text-left text-sm leading-tight gap-1">
          <span className="truncate font-semibold">PWA Template</span>
          <span className="truncate text-xs">Common Features Template</span>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    isActive={isActive(`/${item.url}`)}
                    onClick={() => handleNavigation(`/${item.url}`)}
                  >
                    <LottieIconPlayer
                      icon={item.icon}
                      size={16}
                      highlightOnHover={true}
                    />
                    {item.title}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}

              <SidebarSeparator />

              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"Manage your tenders"}
                  isActive={isActive(`/tenders`)}
                  onClick={() => handleNavigation(`/tenders`)}
                >
                  <LottieIconPlayer
                    icon={LottieIconLib.home}
                    size={16}
                    highlightOnHover={true}
                  />
                  Tender Management
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"Manage your client base"}
                  isActive={isActive(`/management`)}
                  onClick={() => handleNavigation(`/management`)}
                >
                  <LottieIconPlayer
                    icon={LottieIconLib.chat}
                    size={16}
                    highlightOnHover={true}
                  />
                  User Management
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarSeparator />
        {/* Settings */}
        <SidebarMenu>
          {footerItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                isActive={isActive(`/${item.url}`)}
                tooltip={item.title}
                onClick={() => {
                  if (item.action) handleExtraActions(item.action);
                }}
              >
                <LottieIconPlayer
                  icon={item.icon}
                  size={16}
                  highlightOnHover={true}
                />
                {item.title}
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>

        <SidebarSeparator />

        {/* Profile Section */}
        {isAuthenticated && user ? (
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                size="lg"
                asChild
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <Link href="/profile">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage
                      src={user.image || undefined}
                      alt={getUserDisplayName(user.firstName, user.lastName)}
                    />
                    <AvatarFallback className="rounded-lg">
                      {getUserInitials(
                        user.firstName,
                        user.lastName,
                        user.email
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {getUserDisplayName(user.firstName, user.lastName)}
                    </span>
                    <span className="truncate text-xs">{user.email}</span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        ) : (
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton size="lg" asChild>
                <Link href="/">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg">
                      <FaUser className="size-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">Sign In</span>
                    <span className="truncate text-xs">
                      Access your account
                    </span>
                  </div>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
