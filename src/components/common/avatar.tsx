"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { fadeIn } from "@/modules/framer-variants";

import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from "@/components/common/ui/avatar";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";

// Loaders
import { Bouncy } from "ldrs/react";
import "ldrs/react/Bouncy.css";

export const AvatarManager = ({
  isLoading,
  currentUser,
  onOpen,
  onUpload,
}: {
  isLoading: boolean;
  currentUser: any;
  onOpen: () => void;
  onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  const [hover, setHover] = useState(false);

  return (
    <div className="h-max flex flex-row items-center gap-4">
      <Avatar
        className="h-36 w-36 cursor-pointer border-border flex flex-col items-center justify-center bg-zinc-800/80"
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        {isLoading ? (
          <Bouncy size="30" speed="1.75" color="white" />
        ) : (
          <div className="w-full relative">
            <motion.div
              animate={hover ? "show" : "hide"}
              variants={fadeIn}
              hidden={!hover}
              className="flex items-center justify-center h-full w-full absolute bg-zinc-800/80 top-0 left-0"
              onClick={onOpen}
            >
              <LottieIconPlayer
                icon={LottieIconLib.picture}
                size={32}
                className="text-zinc-100"
              />
            </motion.div>
            <AvatarImage
              className="object-cover"
              width={200}
              height={200}
              src={
                currentUser?.image ||
                `https://placehold.co/1000x1000?text=${
                  currentUser?.name?.charAt(0)?.toUpperCase() || "U"
                }`
              }
              alt={currentUser?.name || "User"}
            />
          </div>
        )}

        <AvatarFallback className="text-lg" hidden={hover && !isLoading}>
          {currentUser?.name?.charAt(0)?.toUpperCase() || "U"}
        </AvatarFallback>
        <input
          id="avatar-upload"
          type="file"
          hidden
          accept={"image/*"}
          onChange={onUpload}
        />
      </Avatar>
      <div className="w-full h-full min-h-[8rem] flex flex-col items-center justify-center bg-white border border-border rounded-lg p-4">
        {" "}
        <p>
          <span
            className="cursor-pointer text-blue-600 font-bold"
            onClick={onOpen}
          >
            Click to upload avatar
          </span>
          , or drag and drop
        </p>
        <p>PNG or JPG (max. 5MB)</p>
      </div>
    </div>
  );
};
