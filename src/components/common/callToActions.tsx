"use client";

// Icons
import { BiLogoUpwork as Upwork } from "react-icons/bi";
import { ImQuill as Propose } from "react-icons/im";
import { SiGooglemeet as GoogleMeet } from "react-icons/si";
import { handleExternalRouting } from "@/lib/common/utils";

// Shadcn Components
import { Button } from "@/components/common/ui/button";

import { useAuth } from "@/hooks/useAuth";
import type { CallToActionsProps } from "./types";

export const CallToActions = ({ theme = "dark" }: CallToActionsProps) => {
  const { routeAuthenticated } = useAuth();

  return (
    <div className="flex flex-col gap-2 items-end">
      <Button
        onClick={routeAuthenticated}
        variant={theme === "dark" ? "default" : "secondary"}
        size="lg"
        className="w-full min-w-[280px] justify-between gap-20 px-8 py-4 h-auto bg-background text-foreground hover:bg-muted"
      >
        <Propose size={22} />
        <p className="text-nowrap font-medium">Propose a solution</p>
      </Button>

      <div className="flex gap-0 w-full">
        <Button
          variant="outline"
          size="lg"
          className="flex-1 justify-between gap-4 h-auto"
          onClick={() =>
            handleExternalRouting("https://calendly.com/underscor/30min")
          }
        >
          <GoogleMeet size={20} />
          <p className="text-nowrap font-medium">Let&apos;s Talk</p>
        </Button>

        <Button
          variant="outline"
          size="lg"
          className="flex-1 justify-between gap-4 h-auto"
          onClick={() =>
            handleExternalRouting(
              "https://www.upwork.com/agencies/1655373505334222848"
            )
          }
        >
          <Upwork size={27} />
          <p className="text-nowrap font-medium">Contract us</p>
        </Button>
      </div>
    </div>
  );
};
