"use client";

import React, { useState, useMemo } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Tabs,
  Ta<PERSON>List,
  Ta<PERSON>Trigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Package,
} from "lucide-react";
import { useTenders } from "@/hooks/useTenders";
import { useRequest } from "@/hooks/useRequest";
import type { TenderResponse } from "@/store/actions/tender";
import type { Request } from "@/lib/api/validators/schemas/request";
import { useAuth } from "@/hooks/useAuth";

// Mock data for dashboard statistics
const dashboardStats = [
  {
    // icon: FileText,
    name: "Total Documents",
    value: 1247,
    valueType: "number" as const,
    caption: "+12% from last month",
    color: "blue" as const,
  },
  {
    // icon: Users,
    name: "Active Clients",
    value: 89,
    valueType: "number" as const,
    caption: "+5 new this week",
    color: "green" as const,
  },
  {
    // icon: DollarSign,
    name: "Revenue",
    value: 125000,
    valueType: "dollar" as const,
    caption: "+8% from last month",
    color: "primary" as const,
  },
  {
    // icon: TrendingUp,
    name: "Growth Rate",
    value: 23.5,
    valueType: "percent" as const,
    caption: "Quarterly growth",
    color: "amber" as const,
  },
];

export function DashboardContainer() {
  // Filter state for activities (requests tab)
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter state for tenders
  const [tenderStatusFilter, setTenderStatusFilter] = useState("all");
  const [tenderCategoryFilter, setTenderCategoryFilter] = useState("all");

  const { user } = useAuth();

  // Use request hook for requests data
  const {
    requests,
    isLoading: requestsLoading,
    error: requestsError,
    pagination: requestPagination,
    refreshData: refreshRequests,
  } = useRequest();

  // Use tenders hook with integrated search and pagination
  const {
    tenders,
    searchTerm: tenderSearchTerm,
    pagination,
    isLoading: tendersLoading,
    isSearching: tendersSearching,
    error: tendersError,
    refreshTenders,
    search: searchTenders,
    clearSearch,
    goToPage,
    nextPage,
    previousPage,
  } = useTenders({
    page: 1,
    limit: 10,
    status: tenderStatusFilter !== "all" ? tenderStatusFilter : undefined,
    category: tenderCategoryFilter !== "all" ? tenderCategoryFilter : undefined,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Filter requests based on search term and status (for requests tab)
  const filteredRequests = useMemo(() => {
    let filtered = requests;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (request) =>
          request.tender_id.toLowerCase().includes(searchLower) ||
          request.message?.toLowerCase().includes(searchLower) ||
          request.user?.email?.toLowerCase().includes(searchLower) ||
          request.user?.firstName?.toLowerCase().includes(searchLower) ||
          request.user?.lastName?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((request) => request.status === statusFilter);
    }

    return filtered;
  }, [requests, searchTerm, statusFilter]);

  // Handle refresh action for requests
  const handleRequestRefresh = () => {
    refreshRequests();
  };

  // Handle search for tenders
  const handleTenderSearch = async (query: string) => {
    if (query.trim()) {
      await searchTenders(query, {
        status: tenderStatusFilter !== "all" ? tenderStatusFilter : undefined,
        category:
          tenderCategoryFilter !== "all" ? tenderCategoryFilter : undefined,
      });
    } else {
      clearSearch();
    }
  };

  // Handle refresh action for tenders
  const handleTenderRefresh = () => {
    refreshTenders();
  };

  // Reset tender filters
  const resetTenderFilters = () => {
    clearSearch();
    setTenderStatusFilter("all");
    setTenderCategoryFilter("all");
    goToPage(1);
  };

  // Tender table columns
  const tenderColumns = [
    {
      key: "ocid",
      label: "OCID",
      render: (tender: TenderResponse) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">{tender.ocid}</p>
          {tender.category && (
            <Badge variant="outline" className="w-fit mt-1 text-xs">
              {tender.category}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "description",
      label: "Description",
      render: (tender: TenderResponse) => (
        <div className="max-w-md">
          <p className="text-sm line-clamp-2">
            {tender.description || "No description available"}
          </p>
          {tender.procuring_entity && (
            <p className="text-xs text-muted-foreground mt-1 capitalize">
              {tender.procuring_entity}
            </p>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (tender: TenderResponse) => (
        <Badge
          variant={
            tender.status === "active" || tender.status === "online"
              ? "default"
              : tender.status === "completed" || tender.status === "closed"
              ? "secondary"
              : "outline"
          }
          className="capitalize"
        >
          {tender.status === "active" || tender.status === "online" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : tender.status === "completed" || tender.status === "closed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {tender.status || "unknown"}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (tender: TenderResponse) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(tender.createdAt).toLocaleDateString()}
        </div>
      ),
    },
  ];

  // Request table columns (for requests tab)
  const requestColumns = [
    {
      key: "tender_id",
      label: "Tender ID",
      render: (request: Request) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">{request.tender_id}</p>
          {request.message && (
            <p className="text-xs text-muted-foreground line-clamp-1 mt-1">
              {request.message}
            </p>
          )}
        </div>
      ),
    },
    {
      key: "user",
      label: "Requested By",
      render: (request: Request) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">
            {request.user?.firstName && request.user?.lastName
              ? `${request.user.firstName} ${request.user.lastName}`
              : request.user?.email || "Unknown User"}
          </p>
          {request.user?.email &&
            (request.user?.firstName || request.user?.lastName) && (
              <p className="text-xs text-muted-foreground">
                {request.user.email}
              </p>
            )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (request: Request) => (
        <Badge
          variant={
            request.status === "active" || request.status === "submitted"
              ? "default"
              : request.status === "completed" || request.status === "closed"
              ? "secondary"
              : "outline"
          }
          className="capitalize"
        >
          {request.status === "active" || request.status === "submitted" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : request.status === "completed" || request.status === "closed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {request.status}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (request: Request) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(request.createdAt).toLocaleDateString()}
        </div>
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header
        title={{
          text: `Welcome back, ${user?.firstName} ${user?.lastName}`,
          size: "2xl",
        }}
      />

      {/* Statistics Cards */}
      <Listing.Statistics columns="grid-cols-4">
        {dashboardStats.map((stat, index) => (
          <Listing.StatCard
            key={index}
            name={stat.name}
            value={stat.value}
            valueType={stat.valueType}
            caption={stat.caption}
            color={stat.color}
          />
        ))}
      </Listing.Statistics>

      {/* Tabs */}
      <Tabs defaultValue="tenders" className="w-full">
        <TabsList className="bg-muted border-border mb-6">
          <TabsTrigger
            value="tenders"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            Recent Tenders
          </TabsTrigger>
          <TabsTrigger
            value="requests"
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
          >
            Recent Requests
          </TabsTrigger>
        </TabsList>

        {/* Recent Tenders */}
        <TabsContent value="tenders" className="mt-0">
          <Listing className="space-y-1">
            <Listing.Header
              title={{ text: "Recent Tenders", size: "md" }}
              caption="Latest tenders posted to the platform"
            />

            {/* Tender Filters */}
            <Listing.Filters
              searchTerm={tenderSearchTerm}
              onSearchChange={handleTenderSearch}
              onRefresh={handleTenderRefresh}
              loading={tendersLoading || tendersSearching}
              customActions={
                <div className="flex items-center gap-2 flex-wrap">
                  {/* Status Filters */}
                  <div className="flex items-center gap-1">
                    <Button
                      variant={
                        tenderStatusFilter === "all" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("all")}
                    >
                      All Status
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "active" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("active")}
                    >
                      Active
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "completed"
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("completed")}
                    >
                      Completed
                    </Button>
                    <Button
                      variant={
                        tenderStatusFilter === "pending" ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setTenderStatusFilter("pending")}
                    >
                      Pending
                    </Button>
                  </div>

                  {/* Reset Filters */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={resetTenderFilters}
                    className="text-muted-foreground"
                  >
                    Reset
                  </Button>
                </div>
              }
            />

            {/* Tenders Table */}
            <Listing.Table
              data={tenders}
              columns={tenderColumns}
              loading={tendersLoading}
              emptyState={
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {tendersError
                      ? "Failed to load tenders"
                      : tenderSearchTerm ||
                        tenderStatusFilter !== "all" ||
                        tenderCategoryFilter !== "all"
                      ? "No tenders match your search criteria"
                      : "No tenders available"}
                  </p>
                  {tendersError ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTenderRefresh}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  ) : tenderSearchTerm ||
                    tenderStatusFilter !== "all" ||
                    tenderCategoryFilter !== "all" ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetTenderFilters}
                      className="mt-2"
                    >
                      Clear Filters
                    </Button>
                  ) : null}
                </div>
              }
            />

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <span className="text-sm text-muted-foreground">
                  Showing {tenders.length} of {pagination.total} tenders
                  {tenderSearchTerm && ` (filtered by "${tenderSearchTerm}")`}
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={previousPage}
                    disabled={!pagination.hasPreviousPage || tendersLoading}
                  >
                    Previous
                  </Button>
                  <span className="px-3 py-1 text-sm">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={nextPage}
                    disabled={!pagination.hasNextPage || tendersLoading}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </Listing>
        </TabsContent>

        {/* Recent Requests */}
        <TabsContent value="requests" className="mt-0">
          <Listing className="space-y-1">
            <Listing.Header
              title={{ text: "Recent Requests", size: "md" }}
              caption="Latest do it for me requests, posted to the platform"
            />

            {/* Filters */}
            <Listing.Filters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              onRefresh={handleRequestRefresh}
              loading={requestsLoading}
              customActions={
                <div className="flex items-center gap-2">
                  <Button
                    variant={statusFilter === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatusFilter("all")}
                  >
                    All
                  </Button>
                  <Button
                    variant={statusFilter === "active" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setStatusFilter("active")}
                  >
                    Active
                  </Button>
                  <Button
                    variant={
                      statusFilter === "submitted" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setStatusFilter("submitted")}
                  >
                    Submitted
                  </Button>
                  <Button
                    variant={
                      statusFilter === "completed" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setStatusFilter("completed")}
                  >
                    Completed
                  </Button>
                </div>
              }
            />

            {/* Main Content Grid */}
            <Listing.Table
              data={filteredRequests}
              columns={requestColumns}
              loading={requestsLoading}
              emptyState={
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {requestsError
                      ? "Failed to load requests"
                      : searchTerm || statusFilter !== "all"
                      ? "No requests match your filters"
                      : "No recent requests"}
                  </p>
                  {requestsError ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRequestRefresh}
                      className="mt-2"
                    >
                      Try Again
                    </Button>
                  ) : searchTerm || statusFilter !== "all" ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSearchTerm("");
                        setStatusFilter("all");
                      }}
                      className="mt-2"
                    >
                      Clear Filters
                    </Button>
                  ) : null}
                </div>
              }
            />

            {/* Pagination for Requests */}
            {requestPagination && requestPagination.totalPages > 1 && (
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <span className="text-sm text-muted-foreground">
                  Showing {filteredRequests.length} of {requestPagination.total}{" "}
                  requests
                  {searchTerm && ` (filtered by "${searchTerm}")`}
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Handle previous page - would need to implement pagination in useRequest hook
                      console.log("Previous page");
                    }}
                    disabled={
                      !requestPagination.hasPreviousPage || requestsLoading
                    }
                  >
                    Previous
                  </Button>
                  <span className="px-3 py-1 text-sm">
                    Page {requestPagination.page} of{" "}
                    {requestPagination.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Handle next page - would need to implement pagination in useRequest hook
                      console.log("Next page");
                    }}
                    disabled={!requestPagination.hasNextPage || requestsLoading}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </Listing>
        </TabsContent>
      </Tabs>
    </Listing>
  );
}
