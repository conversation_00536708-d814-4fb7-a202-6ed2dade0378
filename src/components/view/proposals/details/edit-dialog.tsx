"use client";

import { useEffect, useState } from "react";
import { DatabaseCrypto } from "@/lib/crypto/middleware";
import {
  CreateProposalDialog,
  type CreateProposalFormData,
} from "@/components/view/proposals/create-dialog";
import type { Proposal as ApiProposal } from "@/lib/api/validators/schemas/proposal";
import type { Proposal } from "@/data/proposals-mock";
import { useProposal } from "@/hooks/useProposal";

interface ProposalEditDialogProps {
  proposal: Proposal;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: Partial<ApiProposal>) => void;
  isUpdating: boolean;
}

export function ProposalEditDialog({
  proposal,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: ProposalEditDialogProps) {
  const { adaptUIProposalToAPI } = useProposal();
  const [formData, setFormData] = useState<CreateProposalFormData | null>(null);

  async function decryptProposalDescription() {
    // Convert UI proposal to form data format
    const convertedData: CreateProposalFormData = {
      id: proposal.id || "",
      createdAt: new Date(proposal.createdDate),
      updatedAt: new Date(proposal.lastModified),
      name: proposal.name || "",
      description: proposal.description || "",
      status:
        proposal.status === "draft"
          ? "created"
          : proposal.status === "pending"
          ? "submitted"
          : proposal.status === "approved"
          ? "agreed"
          : proposal.status === "rejected"
          ? "closed"
          : "completed",
      budgetType: proposal.budgetType || "fixed",
      total_budget: proposal.totalBudget || 0,
      fixed_budget: proposal.fixedBudget || proposal.totalBudget || 0,
      milestones: proposal.milestones || [],
      duration: proposal.duration || 1,
      agreed_to_terms_and_conditions:
        proposal.agreed_to_terms_and_conditions || false,
      links: proposal.attachments || [],
      accountId: "", // UI proposal doesn't have accountId, will be handled by API
    };

    try {
      let decryptedProposal = await DatabaseCrypto.decryptFields(
        convertedData,
        ["description"]
      );

      setFormData(decryptedProposal);
    } catch (error: any) {
      console.error("Failed to decrypt proposal description:", error);
    }
  }

  useEffect(() => {
    decryptProposalDescription();
  }, [proposal]);

  const handleFormSubmit = (data: CreateProposalFormData) => {
    // Create updated UI proposal with form data
    const updatedUIProposal: Proposal = {
      ...proposal,
      name: data.name,
      description: data.description || "",
      status:
        data.status === "created"
          ? "draft"
          : data.status === "submitted"
          ? "pending"
          : data.status === "agreed"
          ? "approved"
          : data.status === "closed"
          ? "rejected"
          : "completed",
      budgetType: data.budgetType,
      fixedBudget: data.budgetType === "fixed" ? data.fixed_budget : undefined,
      totalBudget:
        data.budgetType === "fixed"
          ? data.fixed_budget || 0
          : data.milestones.reduce((sum, m) => sum + m.amount, 0),
      milestones: data.milestones,
      duration: data.duration,
      agreed_to_terms_and_conditions: data.agreed_to_terms_and_conditions,
      attachments: data.attachments,
    };

    // Convert UI proposal to API format for submission
    const apiProposalData = adaptUIProposalToAPI(updatedUIProposal);

    onSubmit(apiProposalData);
  };

  // Don't render until formData is ready
  if (!formData) {
    return null;
  }

  return (
    <CreateProposalDialog
      editMode
      data={formData}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onSubmit={handleFormSubmit}
      isCreating={false}
      isEditing={isUpdating}
    />
  );
}
