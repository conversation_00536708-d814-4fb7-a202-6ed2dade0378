"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { AlertTriangle } from "lucide-react";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalDeleteDialogProps {
  proposal: Proposal;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
}

export function ProposalDeleteDialog({
  proposal,
  isOpen,
  onOpenChange,
  onConfirm,
  isDeleting,
}: ProposalDeleteDialogProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex flex-col gap-8">
            <AlertTriangle className="h-6 w-6 text-destructive" />
            <DialogTitle>Delete Proposal</DialogTitle>
          </div>
        </DialogHeader>

        <div className="flex flex-col gap-4">
          <p className="text-sm text-muted-foreground">
            This action cannot be undone. Are you sure you want to delete this
            proposal? This will permanently remove:
          </p>

          <div className="bg-muted/50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium">Proposal:</span>
              <span className="text-sm">{proposal.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Total Budget:</span>
              <span className="text-sm">
                {formatCurrency(proposal.total_budget)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium">Status:</span>
              <span className="text-sm capitalize">{proposal.status}</span>
            </div>
          </div>

          <div className="bg-destructive/10 border border-destructive/20 p-3 rounded-lg">
            <p className="text-sm text-destructive">
              <strong>Warning:</strong> All associated data including
              attachments, milestones, and history will be permanently deleted.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete Proposal"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
