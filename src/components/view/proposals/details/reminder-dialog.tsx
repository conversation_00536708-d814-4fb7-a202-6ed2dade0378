"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Textarea } from "@/components/common/ui/textarea";
import { Label } from "@/components/common/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { Calendar } from "@/components/common/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/common/ui/popover";
import { Badge } from "@/components/common/ui/badge";
import { CalendarIcon, Send, AlertCircle, Clock } from "lucide-react";
import { format, addDays, isAfter, isBefore } from "date-fns";
import { cn } from "@/lib/utils";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalReminderDialogProps {
  proposal: Proposal;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: { message: string; scheduledDate: Date }) => void;
}

export function ProposalReminderDialog({
  proposal,
  isOpen,
  onOpenChange,
  onSubmit,
}: ProposalReminderDialogProps) {
  const [message, setMessage] = useState("");
  const [scheduledDate, setScheduledDate] = useState<Date>();
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [remindersSent] = useState(0); // In real app, this would come from API

  // Check if reminders can be sent
  const canSendReminder = () => {
    const createdDate = new Date(proposal.createdAt);
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    return (
      createdDate < oneWeekAgo && // Proposal is older than a week
      remindersSent < 3 && // Less than 3 reminders sent
      proposal.status !== "completed" &&
      proposal.status !== "agreed"
    );
  };

  // Initialize default message and date
  useEffect(() => {
    if (isOpen && proposal) {
      setMessage(
        `Hi there,\n\nI hope this message finds you well. I wanted to follow up on the proposal &quot;${proposal.name}&quot; that I submitted.\n\nI would appreciate any feedback or updates on the status of this proposal. Please let me know if you need any additional information or clarification.\n\nThank you for your time and consideration.\n\nBest regards`
      );

      // Set default scheduled date to tomorrow
      setScheduledDate(addDays(new Date(), 1));
    }
  }, [isOpen, proposal]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!scheduledDate || !message.trim()) return;

    onSubmit({
      message: message.trim(),
      scheduledDate,
    });

    // Reset form
    setMessage("");
    setScheduledDate(undefined);
  };

  const getTimeUntilEligible = () => {
    const createdDate = new Date(proposal.createdAt);
    const eligibleDate = addDays(createdDate, 7);
    const now = new Date();

    if (isAfter(now, eligibleDate)) {
      return null;
    }

    const daysLeft = Math.ceil(
      (eligibleDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    return daysLeft;
  };

  const daysUntilEligible = getTimeUntilEligible();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Send Reminder
          </DialogTitle>
          <DialogDescription>
            Send a follow-up reminder for "{proposal.name}"
          </DialogDescription>
        </DialogHeader>

        {/* Reminder Status */}
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Reminders Sent</span>
            </div>
            <Badge variant={remindersSent >= 3 ? "destructive" : "secondary"}>
              {remindersSent} / 3
            </Badge>
          </div>

          {daysUntilEligible !== null && (
            <div className="flex items-center gap-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <span className="text-sm text-orange-700 dark:text-orange-300">
                Reminders will be available in {daysUntilEligible} day
                {daysUntilEligible !== 1 ? "s" : ""}
                (proposals must be at least 1 week old)
              </span>
            </div>
          )}

          {remindersSent >= 3 && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700 dark:text-red-300">
                Maximum number of reminders (3) has been reached for this
                proposal
              </span>
            </div>
          )}
        </div>

        {canSendReminder() ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Schedule Date */}
            <div className="space-y-2">
              <Label>Schedule Date</Label>
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !scheduledDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {scheduledDate
                      ? format(scheduledDate, "PPP")
                      : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={scheduledDate}
                    onSelect={(date) => {
                      setScheduledDate(date);
                      setIsCalendarOpen(false);
                    }}
                    disabled={(date) => isBefore(date, new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message">Reminder Message</Label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your reminder message..."
                rows={8}
                className="resize-none"
                required
              />
              <p className="text-xs text-muted-foreground">
                {message.length} characters
              </p>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!scheduledDate || !message.trim()}
              >
                Schedule Reminder
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Reminder Not Available</h3>
            <p className="text-sm text-muted-foreground">
              {daysUntilEligible !== null
                ? `Reminders will be available in ${daysUntilEligible} day${
                    daysUntilEligible !== 1 ? "s" : ""
                  }`
                : remindersSent >= 3
                ? "Maximum number of reminders reached"
                : "Reminders are not available for this proposal status"}
            </p>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="mt-4"
            >
              Close
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
