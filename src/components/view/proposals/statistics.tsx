import {
  FileText,
  Calendar,
  CheckCircle,
  XCircle,
  DollarSign,
} from "lucide-react";
import { Listing } from "@/layouts/dashboard/details/basic";
import type { ProposalStats } from "@/data/proposals-mock";

interface ProposalStatisticsProps {
  statistics: ProposalStats | null;
  isLoading: boolean;
}

export function ProposalStatistics({
  statistics,
  isLoading,
}: ProposalStatisticsProps) {
  if (!statistics && !isLoading) {
    return (
      <Listing.Statistics>
        <Listing.StatCard
          icon={FileText}
          name="No Data"
          value={0}
          color="primary"
          loading={false}
        />
      </Listing.Statistics>
    );
  }

  return (
    <Listing.Statistics>
      <Listing.StatCard
        icon={FileText}
        name="Total Proposals"
        value={statistics?.total || 0}
        color="blue"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={Calendar}
        name="Pending"
        value={statistics?.pending || 0}
        color="amber"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={CheckCircle}
        name="Approved"
        value={statistics?.approved || 0}
        color="green"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={XCircle}
        name="Rejected"
        value={statistics?.rejected || 0}
        color="red"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={DollarSign}
        name="Total Value"
        value={statistics?.totalValue || 0}
        valueType="dollar"
        color="purple"
        loading={isLoading}
      />
    </Listing.Statistics>
  );
}
