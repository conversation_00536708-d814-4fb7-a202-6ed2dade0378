import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/common/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import { Plus, Trash2 } from "lucide-react";
import type { Milestone } from "@/data/proposals-mock";
import { Checkbox } from "@/components/common/ui/checkbox";
import { Label } from "@/components/common/ui/label";
import { useDocumentHandler } from "@/components/common/documents";
import RichTextEditor from "@/components/common/richtext";

interface ProposalFormProps {
  editMode?: boolean;
  data?: CreateProposalFormData;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (formData: CreateProposalFormData) => void;
  isCreating: boolean;
  isEditing: boolean;
}

export interface CreateProposalFormData {
  id?: string;
  createdAt?: Date;
  updatedAt?: Date;
  name: string;
  description?: string;
  status?: string;
  budgetType: "fixed" | "milestone"; // UI helper field
  total_budget: number;
  fixed_budget?: number;
  milestones: Milestone[];
  duration: number;
  agreed_to_terms_and_conditions: boolean;
  links: string[];
  accountId?: string;
  attachments?: any;
}

const INITIAL_FORMDATA: CreateProposalFormData = {
  name: "",
  description: "",
  status: "created",
  budgetType: "fixed",
  total_budget: 0,
  fixed_budget: 0,
  milestones: [{ index: 1, amount: 0, description: "" }],
  duration: 1,
  agreed_to_terms_and_conditions: false,
  links: [],
};

export function CreateProposalDialog({
  editMode,
  data,
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
  isEditing,
}: ProposalFormProps) {
  const [formData, setFormData] =
    useState<CreateProposalFormData>(INITIAL_FORMDATA);
  const {
    selectedFiles,
    selectedLibrary,
    DocumentHandler,
    setSelectedFiles,
    setSelectedLibrary,
  } = useDocumentHandler();

  const calculateTotalBudget = () => {
    let total = formData.total_budget || 0;

    if (formData.budgetType === "fixed") {
      total = formData.fixed_budget || 0;
    } else if (formData.budgetType === "milestone") {
      total = formData.milestones.reduce(
        (total, milestone) => total + milestone.amount,
        0
      );
    }

    setFormData({ ...formData, total_budget: total });

    return total;
  };

  useEffect(() => {
    calculateTotalBudget();
  }, [formData.budgetType, formData.fixed_budget, formData.milestones]);

  const addMilestone = () => {
    setFormData({
      ...formData,
      milestones: [
        ...formData.milestones,
        { index: formData.milestones.length + 1, amount: 0, description: "" },
      ],
    });
  };

  const removeMilestone = (index: number) => {
    setFormData({
      ...formData,
      milestones: formData.milestones.filter((_, i) => i !== index),
    });
  };

  const updateMilestone = (
    index: number,
    field: keyof Milestone,
    value: string | number
  ) => {
    const updatedMilestones = formData.milestones.map((milestone, i) =>
      i === index ? { ...milestone, [field]: value } : milestone
    );
    setFormData({ ...formData, milestones: updatedMilestones });
  };

  // Transform form data to match API schema
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Transform form data to match API schema structure
    let apiData: any = {
      name: formData.name,
      description: formData.description,
      status: formData.status || "created",
      links: formData.links,
      milestones: formData.milestones,
      total_budget: formData.total_budget,
      duration: formData.duration,
      agreed_to_terms_and_conditions: formData.agreed_to_terms_and_conditions,
    };

    // Set fixed_budget only if using fixed budget type
    if (formData.budgetType === "fixed") {
      apiData.fixed_budget = formData.fixed_budget;
    }

    // Include id for updates
    if (formData.id) {
      apiData.id = formData.id;
    }

    let data: any = apiData;

    // Handle file attachments
    if (selectedFiles?.length > 0) {
      let formDataObj = new FormData();
      selectedFiles.forEach((file: File, index: number) => {
        let count: number = index + 1;
        formDataObj.append("attachment-" + count, file, file.name);
      });

      Object.entries(data).forEach(([key, value]: [string, any]) => {
        formDataObj.append(key, value);
      });

      data = formDataObj;
    }

    // Handle library document attachment
    if (selectedLibrary) {
      data.attachment = { id: selectedLibrary };
    }

    onSubmit(data);

    // Reset form
    setFormData(INITIAL_FORMDATA);
  };

  useEffect(() => {
    // Set Data if present
    if (data) {
      setFormData(data);
    }
  }, [isOpen, data]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{editMode ? "Edit" : "Create"} New Proposal</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex flex-col gap-8 mt-8">
          {/* Basic Information */}
          <div className="space-y-2">
            <Label htmlFor="proposal-name">Proposal Name</Label>
            <Input
              id="proposal-name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Enter proposal name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="proposal-description">Description</Label>
            <RichTextEditor
              id="proposal-description"
              className="h-[30em] min-h-[35em] mb-8"
              value={formData.description || ""}
              theme="snow"
              onChange={(content) =>
                setFormData({ ...formData, description: content })
              }
              placeholder="Enter proposal description"
            />
          </div>

          {/* Budget Type Selection */}
          <div className="space-y-2">
            <Label>Budget Type</Label>
            <Tabs
              value={formData.budgetType}
              className="space-y-5"
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  budgetType: value as "fixed" | "milestone",
                })
              }
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="fixed">Total Budget</TabsTrigger>
                <TabsTrigger value="milestone">Milestone Based</TabsTrigger>
              </TabsList>
              <TabsContent value="fixed">
                <div className="space-y-2">
                  <Label htmlFor="fixed-budget">Total Budget ($)</Label>
                  <Input
                    id="fixed-budget"
                    type="number"
                    value={formData.fixed_budget || 0}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        fixed_budget: Number(e.target.value),
                      })
                    }
                    placeholder="Enter fixed budget amount"
                    min="0"
                    required
                  />
                </div>
              </TabsContent>

              {/* Budget Details */}
              <TabsContent value="milestone">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <Label>Milestones</Label>
                    <Button
                      type="button"
                      onClick={addMilestone}
                      variant="outline"
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Milestone
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {formData.milestones.map((milestone, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 space-y-3"
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            Milestone {milestone.index}
                          </span>
                          {formData.milestones.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeMilestone(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="space-y-1">
                            <Label
                              htmlFor={`milestone-amount-${index}`}
                              className="text-xs"
                            >
                              Amount ($)
                            </Label>
                            <Input
                              id={`milestone-amount-${index}`}
                              type="number"
                              value={milestone.amount}
                              onChange={(e) =>
                                updateMilestone(
                                  index,
                                  "amount",
                                  Number(e.target.value)
                                )
                              }
                              placeholder="Amount"
                              min="0"
                              required
                            />
                          </div>
                          <div className="space-y-1">
                            <Label
                              htmlFor={`milestone-description-${index}`}
                              className="text-xs"
                            >
                              Description
                            </Label>
                            <Input
                              id={`milestone-description-${index}`}
                              value={milestone.description}
                              onChange={(e) =>
                                updateMilestone(
                                  index,
                                  "description",
                                  e.target.value
                                )
                              }
                              placeholder="Milestone description"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (weeks)</Label>
            <Input
              id="duration"
              type="number"
              value={formData.duration}
              onChange={(e) =>
                setFormData({ ...formData, duration: Number(e.target.value) })
              }
              placeholder="Project duration in weeks"
              min="1"
              required
            />
          </div>

          {/* Total Budget Display */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">
                Total Budget:
              </span>
              <span className="text-lg font-bold text-gray-900">
                ${formData.total_budget.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Document Attachments */}
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
            <DocumentHandler
              setSelectedFiles={setSelectedFiles}
              setSelectedLibrary={setSelectedLibrary}
            />
          </div>

          {/* Terms and Conditions */}
          <div className="flex flex-row items-center gap-2">
            <Checkbox
              id="terms"
              checked={formData.agreed_to_terms_and_conditions}
              onCheckedChange={(checked) =>
                setFormData({
                  ...formData,
                  agreed_to_terms_and_conditions: !!checked,
                })
              }
              required
            />
            <Label htmlFor="terms" className="text-sm leading-relaxed">
              I agree to the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Terms & Conditions
              </a>
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isCreating}
            >
              {editMode
                ? `${isEditing ? "Editing..." : "Edit Proposal"}`
                : `${isCreating ? "Creating..." : "Create Proposal"}`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
