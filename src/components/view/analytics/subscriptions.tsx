"use client";

import * as React from "react";
import { TrendingUp } from "lucide-react";
import { Label, Pie, PieChart } from "recharts";
import { Dot } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/common/ui/chart";

export const description = "A pie chart with stacked sections";

const desktopData = [
  { month: "january", desktop: 186, fill: "var(--color-january)" },
  { month: "february", desktop: 305, fill: "var(--color-february)" },
  { month: "march", desktop: 237, fill: "var(--color-march)" },
];

const mobileData = [
  { month: "january", mobile: 80, fill: "var(--color-january)" },
  { month: "february", mobile: 200, fill: "var(--color-february)" },
  { month: "march", mobile: 120, fill: "var(--color-march)" },
];

const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  desktop: {
    label: "Desktop",
  },
  mobile: {
    label: "Mobile",
  },
  january: {
    label: "January",
    color: "var(--chart-1)",
  },
  february: {
    label: "February",
    color: "var(--chart-2)",
  },
  march: {
    label: "March",
    color: "var(--chart-3)",
  },
} satisfies ChartConfig;

export function SubscriptionAnalytics() {
  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Subscriptions</CardTitle>
        <CardDescription>January - December 2024</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-6 gap-4">
        <ChartContainer
          config={chartConfig}
          className="col-span-2 aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelKey="visitors"
                  nameKey="month"
                  indicator="line"
                  labelFormatter={(_, payload) => {
                    return chartConfig[
                      payload?.[0].dataKey as keyof typeof chartConfig
                    ].label;
                  }}
                />
              }
            />

            <Pie
              data={desktopData}
              dataKey="desktop"
              innerRadius={50}
              outerRadius={62}
            />
            <Pie
              data={mobileData}
              dataKey="mobile"
              innerRadius={72}
              outerRadius={84}
            />
            <Pie
              data={mobileData}
              dataKey="mobile"
              innerRadius={94}
              outerRadius={106}
            />
          </PieChart>
        </ChartContainer>

        <div className="col-span-4">
          <div className="grid grid-cols-2 gap-7">
            <KeyInfo
              label="Quick Win"
              indicator={{ color: "#2A5048" }}
              value={<h3 className="text-2xl font-bold">$684.911</h3>}
            />
            <KeyInfo
              label="Total Revenue"
              value={<h3 className="text-2xl font-bold">$1320.62</h3>}
            />
            <KeyInfo
              label="Focused Bidder"
              indicator={{ color: "#2B5A4F" }}
              value={<h3 className="text-2xl font-bold">$100.48</h3>}
            />
            <KeyInfo
              label="Active Subscriptions"
              value={
                <span className="w-full flex flex-row items-end gap-4">
                  <h3 className="text-2xl font-bold">$293.12</h3>
                  <p>18% conversion...</p>
                </span>
              }
            />
            <KeyInfo
              label="Average User"
              indicator={{ color: "#418B78" }}
              value={<h3 className="text-2xl font-bold">$684.48</h3>}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter>
    </Card>
  );
}

const KeyInfo = ({
  indicator,
  label,
  value,
}: {
  indicator?: { color: string };
  label: string;
  value: React.ReactNode | number;
}) => {
  return (
    <div className="w-full h-full flex flex-row justify-start items-start gap-1">
      <div className="w-auto h-auto" hidden={!indicator}>
        <Dot size={56} color={indicator?.color} />
      </div>
      <div className="w-max h-full flex flex-col gap-1">
        <small>{label}</small>
        {value}
      </div>
    </div>
  );
};
