"use client";

import { TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>, CartesianGrid, XAxis } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/common/ui/chart";

export const description = "A stacked bar chart with a legend";

const chartData = [
  { month: "January", desktop: 186, mobile: 80, watch: 80 },
  { month: "February", desktop: 305, mobile: 200, watch: 100 },
  { month: "March", desktop: 237, mobile: 120, watch: 60 },
  { month: "April", desktop: 73, mobile: 190, watch: 120 },
  { month: "May", desktop: 209, mobile: 130, watch: 60 },
  { month: "June", desktop: 214, mobile: 140, watch: 60 },
  { month: "July", desktop: 250, mobile: 150, watch: 60 },
  { month: "August", desktop: 280, mobile: 160, watch: 60 },
  { month: "September", desktop: 200, mobile: 170, watch: 60 },
  { month: "October", desktop: 220, mobile: 180, watch: 60 },
  { month: "November", desktop: 240, mobile: 190, watch: 60 },
  { month: "December", desktop: 260, mobile: 200, watch: 60 },
];

const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "var(--chart-1)",
  },
  mobile: {
    label: "Mobile",
    color: "var(--chart-2)",
  },
  watch: {
    label: "Watch",
    color: "var(--chart-3)",
  },
} satisfies ChartConfig;

export function TenderAnalytics() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Tenders</CardTitle>
        <CardDescription>January - December 2025</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="max-h-[35vh] w-full">
          <BarChart
            accessibilityLayer
            data={chartData}
            width={undefined}
            height={undefined}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip content={<ChartTooltipContent hideLabel />} />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="desktop"
              stackId="a"
              fill="var(--color-desktop)"
              radius={[0, 0, 4, 4]}
              width={10}
            />
            <Bar
              dataKey="mobile"
              stackId="a"
              fill="var(--color-mobile)"
              radius={[4, 4, 0, 0]}
              width={10}
            />
            <Bar
              dataKey="watch"
              stackId="a"
              fill="var(--color-watch)"
              radius={[4, 4, 0, 0]}
              width={10}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter>
    </Card>
  );
}
