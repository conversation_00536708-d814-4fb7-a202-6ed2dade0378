"use client";

import React, { useState } from "react";
import { ArrowDownLeft } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { useGoogleWallet } from "@/hooks/useGoogleWallet";
import { useAuth } from "@/hooks/useAuth";

export const DepositForm = () => {
  const { paymentMethods } = useAuth();
  const { addFunds, isLoading } = useGoogleWallet();

  const [amount, setAmount] = useState("");
  const [source, setSource] = useState("bank");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount) return;

    await addFunds({
      amount: parseFloat(amount),
      source,
    });

    setAmount("");
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <ArrowDownLeft className="w-5 h-5" />
          Add Funds
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="deposit-amount" className="text-zinc-300">
              Amount ($)
            </Label>
            <Input
              id="deposit-amount"
              type="number"
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="source" className="text-zinc-300">
              Payment Method
            </Label>
            <Select value={source} onValueChange={setSource}>
              <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-zinc-700 border-zinc-600">
                {paymentMethods?.map((method: any) => {
                  return (
                    <SelectItem value={method.id}>
                      {method.holder_name} -- {method.ref_number} --{" "}
                      {method.type} {method.isDefault ? "-- Default" : ""}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <Button
            type="submit"
            disabled={!amount || isLoading}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            {isLoading ? "Processing..." : "Add Funds"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
