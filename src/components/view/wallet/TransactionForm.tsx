"use client";

import React, { Fragment, useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, CreditCard } from "lucide-react";
// @ts-ignore - braintree-web doesn't have official TypeScript declarations
import * as braintree from "braintree-web";

import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Button } from "@/components/common/ui/button";

import { useContracts } from "@/hooks/useContracts";

interface TransactionFormProps {
  isReady: boolean;
  clientToken: string | null;
  error: string | null;
  onSubmit: (data: {
    amount: number;
    description?: string;
    paymentMethodNonce: string;
  }) => void;
  onClose: () => void;
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  isReady,
  clientToken,
  error,
  onSubmit,
  onClose,
}) => {
  const { currentContract } = useContracts();

  const [amount, setAmount] = useState("");
  const [description, setDescription] = useState("");
  const [isHostedFieldsReady, setIsHostedFieldsReady] = useState(false);
  const [hostedFieldsInstance, setHostedFieldsInstance] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const cardNumberRef = useRef<HTMLDivElement>(null);
  const expirationDateRef = useRef<HTMLDivElement>(null);
  const cvvRef = useRef<HTMLDivElement>(null);

  // Initialize Braintree Hosted Fields when client token is available
  useEffect(() => {
    if (
      clientToken &&
      cardNumberRef.current &&
      expirationDateRef.current &&
      cvvRef.current &&
      !hostedFieldsInstance
    ) {
      initializeBraintreeHostedFields();
    }
  }, [clientToken, hostedFieldsInstance]);

  const initializeBraintreeHostedFields = async () => {
    try {
      // Create Hosted Fields
      const hostedFields = await braintree.hostedFields.create({
        authorization: clientToken,
        styles: {
          input: {
            "font-size": "16px",
            "font-family": "system-ui, -apple-system, sans-serif",
            color: "#374151",
            padding: "12px",
          },
          "input:focus": {
            color: "#111827",
          },
          ".invalid": {
            color: "#ef4444",
          },
          ".valid": {
            color: "#10b981",
          },
        },
        fields: {
          number: {
            selector: "#card-number",
            placeholder: "4111 1111 1111 1111",
          },
          cvv: {
            selector: "#cvv",
            placeholder: "123",
          },
          expirationDate: {
            selector: "#expiration-date",
            placeholder: "MM/YY",
          },
        },
      });

      setHostedFieldsInstance(hostedFields);
      setIsHostedFieldsReady(true);
    } catch (error) {
      console.error("Failed to initialize Braintree Hosted Fields:", error);
    }
  };

  // Cleanup Hosted Fields instance when component unmounts
  useEffect(() => {
    return () => {
      if (hostedFieldsInstance) {
        hostedFieldsInstance.teardown();
      }
    };
  }, [hostedFieldsInstance]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !hostedFieldsInstance || !currentContract) return;

    setIsSubmitting(true);

    try {
      // Tokenize the hosted fields to get a payment method nonce
      const { nonce } = await hostedFieldsInstance.tokenize();

      if (!nonce) {
        throw new Error("Failed to get payment method nonce");
      }

      // Pass data to parent component
      onSubmit({
        amount: parseFloat(amount),
        description:
          description ||
          `Payment for contract ${currentContract.title || currentContract.id}`,
        paymentMethodNonce: nonce,
      });
    } catch (error) {
      console.error("Failed to tokenize payment method:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isReady && !clientToken) {
    return (
      <div className="mb-4 p-3 bg-yellow-900/20 border border-yellow-700 rounded-lg">
        <div className="flex items-center gap-2">
          <Loader2 className="w-4 h-4 text-yellow-400 animate-spin" />
          <span className="text-yellow-400 text-sm">Initializing...</span>
        </div>
      </div>
    );
  }

  return (
    <Fragment>
      {/* Show error if any */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/20 border border-red-700 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-400" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
        <div className="space-y-2">
          <Label htmlFor="transaction-amount" className="text-zinc-300">
            Amount ($)
          </Label>
          <Input
            id="transaction-amount"
            type="number"
            step="0.01"
            min="0.01"
            placeholder="0.00"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="transaction-description" className="text-zinc-300">
            Description (Optional)
          </Label>
          <Input
            id="transaction-description"
            type="text"
            placeholder="Payment description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
          />
        </div>

        {/* Braintree Hosted Fields */}
        <div className="space-y-4">
          <Label className="text-zinc-300">Payment Information</Label>

          {!isHostedFieldsReady && (
            <div className="flex items-center justify-center p-8 bg-white rounded-lg">
              <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-400">
                Loading payment form...
              </span>
            </div>
          )}

          <div className="space-y-4">
            {/* Card Number */}
            <div className="space-y-2">
              <Label htmlFor="card-number" className="text-zinc-300">
                Card Number
              </Label>
              <div
                id="card-number"
                ref={cardNumberRef}
                className="bg-white border border-gray-300 rounded-lg p-3 min-h-[48px] focus-within:ring-2 focus-within:ring-orange-500 focus-within:border-orange-500"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              {/* Expiration Date */}
              <div className="space-y-2">
                <Label htmlFor="expiration-date" className="text-zinc-300">
                  Expiry Date
                </Label>
                <div
                  id="expiration-date"
                  ref={expirationDateRef}
                  className="bg-white border border-gray-300 rounded-lg p-3 min-h-[48px] focus-within:ring-2 focus-within:ring-orange-500 focus-within:border-orange-500"
                />
              </div>

              {/* CVV */}
              <div className="space-y-2">
                <Label htmlFor="cvv" className="text-zinc-300">
                  CVV
                </Label>
                <div
                  id="cvv"
                  ref={cvvRef}
                  className="bg-white border border-gray-300 rounded-lg p-3 min-h-[48px] focus-within:ring-2 focus-within:ring-orange-500 focus-within:border-orange-500"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="flex-1 border-zinc-600 text-zinc-300 hover:bg-zinc-700"
          >
            Cancel
          </Button>

          <Button
            type="submit"
            disabled={
              !amount ||
              !isHostedFieldsReady ||
              !isReady ||
              !clientToken ||
              isSubmitting
            }
            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
          >
            {isSubmitting ? (
              <Fragment>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </Fragment>
            ) : (
              <Fragment>
                <CreditCard className="w-4 h-4 mr-2" />
                Review Payment
              </Fragment>
            )}
          </Button>
        </div>

        {!clientToken && isReady && (
          <p className="text-amber-400 text-sm text-center">
            Unable to load payment methods. Please try refreshing the page.
          </p>
        )}
      </form>
    </Fragment>
  );
};
