"use client";

import React from "react";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  ArrowUpRight,
  ArrowDownLeft,
  Shield,
  DollarSign,
  Eye,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { useGoogleWallet } from "@/hooks/useGoogleWallet";

export const TransactionHistory = () => {
  const { transactions, releaseEscrowFunds, isLoading } = useGoogleWallet();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="text-green-500 w-4 h-4" />;
      case "pending":
        return <Clock className="text-yellow-500 w-4 h-4" />;
      case "failed":
        return <AlertCircle className="text-red-500 w-4 h-4" />;
      default:
        return <Clock className="text-zinc-500 w-4 h-4" />;
    }
  };

  const getAmountColor = (type: string) => {
    if (type === "deposit") return "text-green-400";
    if (type === "payment" || type === "escrow") return "text-red-400";
    return "text-zinc-400";
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "payment":
        return <ArrowUpRight className="w-4 h-4" />;
      case "deposit":
        return <ArrowDownLeft className="w-4 h-4" />;
      case "escrow":
        return <Shield className="w-4 h-4" />;
      default:
        return <DollarSign className="w-4 h-4" />;
    }
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Eye className="w-5 h-5" />
          Transaction History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {transactions.length === 0 ? (
            <div className="text-center text-zinc-500 py-8">
              No transactions yet
            </div>
          ) : (
            transactions.map((tx) => (
              <div
                key={tx.id}
                className="border border-zinc-700 rounded-lg p-4 hover:bg-zinc-700/50 transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(tx.status)}
                    {getTypeIcon(tx.type)}
                    <span className="font-medium text-white capitalize">{tx.type}</span>
                    {tx.type === "escrow" && tx.status === "pending" && (
                      <Button
                        onClick={() => releaseEscrowFunds(tx.id)}
                        disabled={isLoading}
                        size="sm"
                        variant="outline"
                        className="text-xs bg-blue-600/20 text-blue-400 border-blue-600/30 hover:bg-blue-600/30"
                      >
                        Release
                      </Button>
                    )}
                  </div>
                  <span className={`font-semibold ${getAmountColor(tx.type)}`}>
                    {tx.amount > 0 ? "+" : ""}${Math.abs(tx.amount).toFixed(2)}
                  </span>
                </div>
                
                <p className="text-sm text-zinc-400 mb-1">{tx.description}</p>
                
                {tx.releaseCondition && (
                  <p className="text-xs text-purple-400 mb-1">
                    Condition: {tx.releaseCondition}
                  </p>
                )}
                
                <p className="text-xs text-zinc-500">{tx.timestamp}</p>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};
