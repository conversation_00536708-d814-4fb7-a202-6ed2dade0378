"use client";

import React, { Fragment } from "react";
import {
  <PERSON>ert<PERSON>ircle,
  CreditCard,
  DollarSign,
  Building2,
  Loader2,
  CheckCircle,
} from "lucide-react";

import { Button } from "@/components/common/ui/button";
import { useContracts } from "@/hooks/useContracts";

interface TransactionConfirmationProps {
  transactionData: {
    amount: number;
    description?: string;
    paymentMethodNonce: string;
  };
  isProcessing: boolean;
  error: string | null;
  onConfirm: () => Promise<void>;
  onBack: () => void;
  onClose: () => void;
}

export const TransactionConfirmation: React.FC<
  TransactionConfirmationProps
> = ({ transactionData, isProcessing, error, onConfirm, onBack, onClose }) => {
  const { currentContract } = useContracts();

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <Fragment>
      {/* Show error if any */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/20 border border-red-700 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-400" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Transaction Details */}
        <div className="bg-zinc-700 rounded-lg p-4 space-y-3">
          <h3 className="font-semibold text-white flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-green-400" />
            Transaction Details
          </h3>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-zinc-400">Amount:</span>
              <span className="text-white font-semibold text-lg">
                {formatCurrency(transactionData.amount)}
              </span>
            </div>

            {transactionData.description && (
              <div className="flex justify-between items-start">
                <span className="text-zinc-400">Description:</span>
                <span className="text-white text-sm text-right max-w-48">
                  {transactionData.description}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Contract Details */}
        {currentContract && (
          <div className="bg-zinc-700 rounded-lg p-4 space-y-3">
            <h3 className="font-semibold text-white flex items-center gap-2">
              <Building2 className="w-4 h-4 text-blue-400" />
              Contract Information
            </h3>

            <div className="space-y-2">
              <div className="flex justify-between items-start">
                <span className="text-zinc-400">Contract:</span>
                <span className="text-white text-sm text-right max-w-48">
                  {currentContract.title || currentContract.id}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-zinc-400">Status:</span>
                <span className="text-green-400 text-sm capitalize">
                  {currentContract.status || "Active"}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Payment Method Info */}
        <div className="bg-zinc-700 rounded-lg p-4">
          <h3 className="font-semibold text-white flex items-center gap-2 mb-2">
            <CreditCard className="w-4 h-4 text-purple-400" />
            Payment Method
          </h3>
          <div className="flex items-center gap-2 text-sm text-zinc-400">
            <CheckCircle className="w-4 h-4 text-green-400" />
            <span>Secure payment method verified</span>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-blue-400 mt-0.5" />
            <div className="text-sm">
              <p className="text-blue-400 font-medium">Secure Transaction</p>
              <p className="text-blue-300 text-xs mt-1">
                Your payment is processed securely through Braintree. Your card
                information is never stored on our servers.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-6">
        <Button
          variant="outline"
          onClick={onBack}
          disabled={isProcessing}
          className="flex-1 border-zinc-600 text-zinc-300 hover:bg-zinc-700"
        >
          Back
        </Button>

        <Button
          onClick={onConfirm}
          disabled={isProcessing || !currentContract}
          className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
        >
          {isProcessing ? (
            <Fragment>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </Fragment>
          ) : (
            <Fragment>
              <CreditCard className="w-4 h-4 mr-2" />
              Confirm Payment
            </Fragment>
          )}
        </Button>
      </div>
    </Fragment>
  );
};
