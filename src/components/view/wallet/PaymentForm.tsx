"use client";

import React, { useState } from "react";
import { CreditCard } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { useGoogleWallet } from "@/hooks/useGoogleWallet";

export const PaymentForm = () => {
  const { makePayment, isLoading } = useGoogleWallet();
  const [amount, setAmount] = useState("");
  const [recipient, setRecipient] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !recipient) return;
    
    await makePayment({
      amount: parseFloat(amount),
      recipient,
      description: `Payment to ${recipient}`,
    });
    
    setAmount("");
    setRecipient("");
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          Send Payment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount" className="text-zinc-300">Amount ($)</Label>
            <Input
              id="amount"
              type="number"
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="recipient" className="text-zinc-300">Recipient</Label>
            <Input
              id="recipient"
              type="text"
              placeholder="Email or phone"
              value={recipient}
              onChange={(e) => setRecipient(e.target.value)}
              className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
            />
          </div>
          
          <Button
            type="submit"
            disabled={!amount || !recipient || isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? "Processing..." : "Pay with Google Wallet"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
