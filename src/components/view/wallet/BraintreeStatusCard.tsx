"use client";

import React from "react";
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { useBraintreeStatus } from "@/hooks/useBrainTree";

/**
 * BraintreeStatusCard - A component to display Braintree service status
 * This demonstrates how the SWR hook works with the new status endpoint
 */
export const BraintreeStatusCard = () => {
  const { 
    isReady, 
    isConnected, 
    isLoading, 
    error, 
    initialize, 
    testConnection 
  } = useBraintreeStatus();

  const handleRefreshStatus = async () => {
    try {
      await testConnection();
    } catch (error) {
      console.error("Failed to refresh status:", error);
    }
  };

  const handleInitialize = async () => {
    try {
      await initialize();
    } catch (error) {
      console.error("Failed to initialize:", error);
    }
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
    }
    if (error) {
      return <XCircle className="w-5 h-5 text-red-500" />;
    }
    if (isConnected && isReady) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <AlertCircle className="w-5 h-5 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (isLoading) return "Checking status...";
    if (error) return `Error: ${error}`;
    if (isConnected && isReady) return "Connected & Ready";
    if (isConnected) return "Connected (Initializing...)";
    return "Disconnected";
  };

  const getStatusColor = () => {
    if (isLoading) return "text-blue-500";
    if (error) return "text-red-500";
    if (isConnected && isReady) return "text-green-500";
    return "text-yellow-500";
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          {getStatusIcon()}
          Braintree Service Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-zinc-300">Status:</span>
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-zinc-300">Environment:</span>
          <span className="text-zinc-400">
            {process.env.NODE_ENV === "development" ? "Development" : "Production"}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-zinc-300">Ready for Transactions:</span>
          <span className={isReady ? "text-green-500" : "text-red-500"}>
            {isReady ? "Yes" : "No"}
          </span>
        </div>

        {error && (
          <div className="p-3 bg-red-900/20 border border-red-700 rounded-lg">
            <div className="flex items-center gap-2">
              <XCircle className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Button
            onClick={handleRefreshStatus}
            disabled={isLoading}
            variant="outline"
            size="sm"
            className="flex-1 border-zinc-600 text-zinc-300 hover:bg-zinc-700"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh Status
          </Button>

          {!isReady && (
            <Button
              onClick={handleInitialize}
              disabled={isLoading}
              size="sm"
              className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
            >
              Initialize
            </Button>
          )}
        </div>

        <div className="text-xs text-zinc-500 space-y-1">
          <p>• Status is automatically synced with server</p>
          <p>• Refresh manually to check latest connection state</p>
          <p>• Initialize if Braintree is not ready for transactions</p>
        </div>
      </CardContent>
    </Card>
  );
};
