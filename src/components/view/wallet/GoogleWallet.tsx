"use client";

import React, { useEffect } from "react";
import { useGoogleWallet } from "@/hooks/useGoogleWallet";
// import { WalletStatus } from "./WalletStatus";
// import { WalletForms } from "./WalletForms";
import { TransactionHistory } from "./TransactionHistory";

// Main Google Wallet Component using Compound Pattern
export const GoogleWallet = () => {
  const { initWallet } = useGoogleWallet();

  useEffect(() => {
    initWallet();
  }, [initWallet]);

  return (
    <div className="min-h-screen bg-zinc-900 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Your Wallet</h1>
          <p className="text-zinc-400">
            Secure payments, deposits, and escrow services
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <WalletStatus />
          <div className="lg:col-span-2">
            <WalletForms />
          </div>
        </div>

        <TransactionHistory />
      </div>
    </div>
  );
};
