"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { usePermissions } from "@/hooks/useRBAC";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import { useRouter } from "next/navigation";
import { CreateRoomDialog } from "@/components/view/chat/create-room-dialog";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";
import { MakePaymentDialog } from "./make-payment-dialog";
import { useChat } from "@/hooks/useChat";
import { useAuth } from "@/hooks/useAuth";

interface ContractDetailsHeaderProps {
  contract: ApiContract;
  onEdit: () => void;
  onDelete: () => void;
}

const statusColors = {
  draft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
  active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
  completed: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
  terminated: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
  expired:
    "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
};

export function ContractDetailsHeader({
  contract,
  onEdit,
  onDelete,
}: ContractDetailsHeaderProps) {
  const router = useRouter();

  const { selectRoom, createRoom } = useChat();
  const { user } = useAuth();
  const { hasEntityPermission } = usePermissions();
  const [isMakePaymentDialogOpen, setIsMakePaymentDialogOpen] = useState(false);

  const handleBack = () => {
    router.back();
  };

  function navigateToRoom() {
    const roomURL: string = "/chat";
    router.push(roomURL);
  }

  const handleCreateRoom = () => {
    createRoom({ contractId: contract.id });
    navigateToRoom();
  };

  const handleGoToRoom = () => {
    if (contract.hasRoom && contract?.room?.id) {
      selectRoom(contract?.room?.id);
      navigateToRoom();
    }
  };

  const handleMakePayment = () => {
    setIsMakePaymentDialogOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header Content */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0 pt-1">
        <div className="flex flex-col items-start gap-8">
          <div className="flex items-end space-x-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
              <LottieIconPlayer icon={LottieIconLib.contract} size={24} />
            </div>
            <p className="text-gray-900 dark:text-zinc-500 font-semibold">
              Contract
            </p>
          </div>
          <div className="flex flex-col gap-6">
            <div className="flex flex-col items-start gap-3">
              <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                {contract.title}
              </h1>
              <Badge
                variant="secondary"
                className={
                  statusColors[contract.status as keyof typeof statusColors]
                }
              >
                {contract.status.charAt(0).toUpperCase() +
                  contract.status.slice(1)}
              </Badge>
            </div>
            <div className="flex flex-col space-y-1 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-between">
                <span>Client:</span>
                <span>
                  {contract.client?.name} - {contract.client?.email}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Total Value:</span>
                <span>{formatCurrency(contract.total_value || 0)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Duration:</span>
                <span>
                  {formatDate(contract.start_date)} -{" "}
                  {formatDate(contract.end_date)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <RBACWrapper
            entity={DEFAULT_ENTITIES.CONTRACT}
            action={PERMISSION_ACTIONS.UPDATE}
          >
            {contract.hasRoom ? (
              <Button variant="outline" onClick={handleGoToRoom}>
                <LottieIconPlayer
                  icon={LottieIconLib.chat}
                  size={16}
                  className="mr-2"
                />
                Go to Room
              </Button>
            ) : (
              <Button variant="outline" onClick={handleCreateRoom}>
                <LottieIconPlayer
                  icon={LottieIconLib.chat}
                  size={16}
                  className="mr-2"
                />
                Create a Room
              </Button>
            )}

            <Button variant="outline" onClick={handleMakePayment}>
              <LottieIconPlayer
                icon={LottieIconLib.payment}
                size={16}
                className="mr-2"
              />
              Make Payment
            </Button>
          </RBACWrapper>

          {(hasEntityPermission(
            DEFAULT_ENTITIES.CONTRACT,
            PERMISSION_ACTIONS.UPDATE
          ) ||
            hasEntityPermission(
              DEFAULT_ENTITIES.CONTRACT,
              PERMISSION_ACTIONS.DELETE
            )) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <LottieIconPlayer icon={LottieIconLib.menu} size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {hasEntityPermission(
                  DEFAULT_ENTITIES.CONTRACT,
                  PERMISSION_ACTIONS.UPDATE
                ) && (
                  <DropdownMenuItem onClick={onEdit}>
                    <LottieIconPlayer
                      icon={LottieIconLib.edit}
                      size={16}
                      className="mr-2"
                    />
                    Edit Contract
                  </DropdownMenuItem>
                )}
                {hasEntityPermission(
                  DEFAULT_ENTITIES.CONTRACT,
                  PERMISSION_ACTIONS.DELETE
                ) && (
                  <DropdownMenuItem
                    onClick={onDelete}
                    className="text-red-600 dark:text-red-400"
                  >
                    <LottieIconPlayer
                      icon={LottieIconLib.delete}
                      size={16}
                      className="mr-2"
                    />
                    Delete Contract
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Contract Meta Information */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Contract ID
            </div>
            <div className="mt-1 text-sm font-mono text-gray-900 dark:text-white">
              {contract.id}
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Created
            </div>
            <div className="mt-1 text-sm text-gray-900 dark:text-white">
              {formatDate(contract.createdAt)}
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Last Updated
            </div>
            <div className="mt-1 text-sm text-gray-900 dark:text-white">
              {formatDate(contract.updatedAt)}
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Proposal ID
            </div>
            <div className="mt-1 text-sm font-mono text-gray-900 dark:text-white">
              {contract.proposal_id || "N/A"}
            </div>
          </div>
        </div>
      </div>

      {/* Make Payment Dialog */}
      <MakePaymentDialog
        open={isMakePaymentDialogOpen}
        onOpenChange={setIsMakePaymentDialogOpen}
      />
    </div>
  );
}
