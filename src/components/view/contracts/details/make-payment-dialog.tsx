"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogDescription,
  DialogTitle,
} from "@/components/common/ui/dialog";
import { CreditCard, CheckCircle, AlertCircle } from "lucide-react";

import { useBraintree } from "@/hooks/useBrainTree";
import { useContracts } from "@/hooks/useContracts";
import type { CreateTransactionRequest } from "@/lib/api/validators/schemas/braintree";

// Import child components
import {
  TransactionForm,
  TransactionConfirmation,
} from "@/components/view/wallet";

// Dialog states
type DialogState = "form" | "confirmation" | "success" | "error";

interface MakePaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface TransactionData {
  amount: number;
  description?: string;
  paymentMethodNonce: string;
}

export const MakePaymentDialog = ({
  open,
  onOpenChange,
}: MakePaymentDialogProps) => {
  const { currentContract } = useContracts();
  const {
    isReady,
    clientToken,
    isConnected,
    isProcessingTransaction,
    error,
    initialize,
    processTransaction,
    clearBraintreeError,
  } = useBraintree();

  // Dialog state management
  const [dialogState, setDialogState] = useState<DialogState>("form");
  const [transactionData, setTransactionData] =
    useState<TransactionData | null>(null);
  const [transactionResult, setTransactionResult] = useState<any>(null);

  // Initialize Braintree when dialog opens
  useEffect(() => {
    if (open && !isReady && !isConnected) {
      initialize();
    }
  }, [open, isReady, isConnected, initialize]);

  // Reset dialog state when closed
  useEffect(() => {
    if (!open) {
      setDialogState("form");
      setTransactionData(null);
      setTransactionResult(null);
      clearBraintreeError();
    }
  }, [open, clearBraintreeError]);

  // Handler functions to pass to child components
  const handleFormSubmit = (data: TransactionData) => {
    setTransactionData(data);
    setDialogState("confirmation");
  };

  const handleConfirmTransaction = async () => {
    if (!currentContract || !transactionData) return;

    try {
      const request: CreateTransactionRequest = {
        amount: transactionData.amount,
        orderId: currentContract.id,
        paymentMethodNonce: transactionData.paymentMethodNonce,
        description:
          transactionData.description ||
          `Payment for contract ${currentContract.title || currentContract.id}`,
      };

      await processTransaction(request);

      setTransactionResult({
        amount: transactionData.amount,
        contractTitle: currentContract.title || currentContract.id,
        timestamp: new Date().toISOString(),
      });

      setDialogState("success");

      // Auto-close after success
      setTimeout(() => {
        handleClose();
      }, 3000);
    } catch (error) {
      console.error("Transaction failed:", error);
      setDialogState("error");
    }
  };

  const handleBackToForm = () => {
    setDialogState("form");
    setTransactionData(null);
  };

  const handleClose = () => {
    try {
      // Reset all states
      setDialogState("form");
      setTransactionData(null);
      setTransactionResult(null);
      clearBraintreeError();
    } finally {
      // Always close the dialog
      onOpenChange(false);
    }
  };

  const handleRetry = () => {
    setDialogState("form");
    clearBraintreeError();
  };

  // Helper function to get dialog title and description based on state
  const getDialogContent = () => {
    switch (dialogState) {
      case "form":
        return {
          title: "Make Payment",
          description: "Enter your payment details to process the transaction.",
          icon: <CreditCard className="w-8 h-8 text-orange-500" />,
        };
      case "confirmation":
        return {
          title: "Confirm Payment",
          description:
            "Please review your transaction details before proceeding.",
          icon: <CreditCard className="w-8 h-8 text-orange-500" />,
        };
      case "success":
        return {
          title: "Payment Successful",
          description: "Check your email inbox for receipts",
        };
      case "error":
        return {
          title: "Payment Failed",
          description:
            "There was an issue processing your payment. Please try again.",
          icon: <AlertCircle className="w-8 h-8 text-red-500" />,
        };
      default:
        return {
          title: "Make Payment",
          description: "Enter your payment details to process the transaction.",
          icon: <CreditCard className="w-8 h-8 text-orange-500" />,
        };
    }
  };

  const dialogContent = getDialogContent();

  // Render child component based on dialog state
  const renderDialogChild = () => {
    switch (dialogState) {
      case "form":
        return (
          <TransactionForm
            isReady={isReady}
            clientToken={clientToken}
            error={error}
            onSubmit={handleFormSubmit}
            onClose={handleClose}
          />
        );

      case "confirmation":
        return transactionData ? (
          <TransactionConfirmation
            transactionData={transactionData}
            isProcessing={isProcessingTransaction}
            error={error}
            onConfirm={handleConfirmTransaction}
            onBack={handleBackToForm}
            onClose={handleClose}
          />
        ) : null;

      case "success":
        return (
          <div className="flex flex-col items-center gap-2 text-center py-6">
            <div className="w-16 h-16 bg-green-600 rounded-full flex flex-col items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <p className="text-zinc-400 mb-4">
              Your payment of ${transactionResult?.amount?.toFixed(2)} has been
              processed.
            </p>
            <p className="text-sm text-zinc-500">
              This dialog will close automatically in a few seconds.
            </p>
          </div>
        );

      case "error":
        return (
          <div className="text-center py-6">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-red-400 mb-2">
              Payment Failed
            </h3>
            <p className="text-zinc-400 mb-4">
              {error ||
                "An unexpected error occurred while processing your payment."}
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleRetry}
                className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg"
              >
                Try Again
              </button>
              <button
                onClick={handleClose}
                className="px-4 py-2 border border-zinc-600 text-zinc-300 hover:bg-zinc-700 rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl bg-zinc-900 border-zinc-700">
        <DialogHeader className="space-y-4">
          <DialogTitle className="space-y-4">
            {dialogContent.icon}
            {dialogContent.title}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            {dialogContent.description}
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">{renderDialogChild()}</div>
      </DialogContent>
    </Dialog>
  );
};
