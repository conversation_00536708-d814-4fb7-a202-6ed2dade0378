"use client";

import { But<PERSON> } from "@/components/common/ui/button";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { Plus } from "lucide-react";
import { Listing } from "@/layouts/dashboard/details/basic";

interface ContractHeaderProps {
  onCreateContract: () => void;
  contractsCount: number;
}

export function ContractHeader({
  onCreateContract,
  contractsCount,
}: ContractHeaderProps) {
  return (
    <Listing.Header
      title="Contracts"
      caption={`Manage your client contracts and agreements, ${contractsCount} ${
        contractsCount === 1 ? "contract" : "contracts"
      }`}
      actions={
        <RBACWrapper
          entity={DEFAULT_ENTITIES.CONTRACT}
          action={PERMISSION_ACTIONS.CREATE}
        >
          <Button onClick={onCreateContract}>
            <Plus className="h-4 w-4 mr-2" />
            New Contract
          </Button>
        </RBACWrapper>
      }
    />
  );
}
