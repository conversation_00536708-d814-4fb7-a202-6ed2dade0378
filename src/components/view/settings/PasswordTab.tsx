"use client";

/**
 * Password Tab Component
 *
 * Manages user password updates with current password verification.
 * Handles password validation and security requirements.
 */

import React, { useState } from "react";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import { Save, Lock, Eye, EyeOff, Shield, AlertTriangle } from "lucide-react";

export function PasswordTab() {
  const { updatePassword, isLoading } = useAuth();

  // Form state
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const togglePasswordVisibility = (field: "current" | "new" | "confirm") => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }

    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.currentPassword.trim()) {
      toast.error("Current password is required");
      return;
    }

    if (!formData.newPassword.trim()) {
      toast.error("New password is required");
      return;
    }

    if (!formData.confirmPassword.trim()) {
      toast.error("Please confirm your new password");
      return;
    }

    // Validate password match
    if (formData.newPassword !== formData.confirmPassword) {
      toast.error("New passwords do not match");
      return;
    }

    // Validate password strength
    const passwordErrors = validatePassword(formData.newPassword);
    if (passwordErrors.length > 0) {
      toast.error(passwordErrors[0]); // Show first error
      return;
    }

    // Check if new password is different from current
    if (formData.currentPassword === formData.newPassword) {
      toast.error("New password must be different from current password");
      return;
    }

    setIsSubmitting(true);

    try {
      await updatePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
      });

      // Clear form on success
      setFormData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Success toast is handled by the hook
    } catch (error) {
      // Error toast is handled by the hook
      console.error("Password update error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPasswordStrength = (
    password: string
  ): { strength: number; label: string; color: string } => {
    if (!password)
      return { strength: 0, label: "No password", color: "text-gray-400" };

    const errors = validatePassword(password);
    const strength = Math.max(0, 5 - errors.length);

    switch (strength) {
      case 5:
        return { strength: 100, label: "Very Strong", color: "text-green-600" };
      case 4:
        return { strength: 80, label: "Strong", color: "text-green-500" };
      case 3:
        return { strength: 60, label: "Good", color: "text-yellow-500" };
      case 2:
        return { strength: 40, label: "Fair", color: "text-orange-500" };
      case 1:
        return { strength: 20, label: "Weak", color: "text-red-500" };
      default:
        return { strength: 10, label: "Very Weak", color: "text-red-600" };
    }
  };

  const passwordStrength = getPasswordStrength(formData.newPassword);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Current Password */}
      <div className="flex flex-row items-center gap-2">
        <Label
          htmlFor="currentPassword"
          className="w-full flex items-center gap-2"
        >
          Current Password *
        </Label>
        <div className="w-full relative">
          <Input
            id="currentPassword"
            type={showPasswords.current ? "text" : "password"}
            value={formData.currentPassword}
            onChange={(e) =>
              handleInputChange("currentPassword", e.target.value)
            }
            placeholder="Enter your current password"
            required
            className="pr-10"
          />
          <button
            type="button"
            onClick={() => togglePasswordVisibility("current")}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPasswords.current ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* New Password */}
      <div className="w-full flex items-center gap-2">
        <Label htmlFor="newPassword" className="w-full flex items-center gap-2">
          New Password *
        </Label>
        <div className="w-full flex flex-col gap-4">
          <div className="w-full relative">
            <Input
              id="newPassword"
              type={showPasswords.new ? "text" : "password"}
              value={formData.newPassword}
              onChange={(e) => handleInputChange("newPassword", e.target.value)}
              placeholder="Enter your new password"
              required
              className="pr-10"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility("new")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPasswords.new ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>

          {/* Password Strength Indicator */}
          {formData.newPassword && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <small>Password strength:</small>
                <small className={`font-medium ${passwordStrength.color}`}>
                  {passwordStrength.label}
                </small>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div
                  className={`h-1 rounded-full transition-all duration-300 ${
                    passwordStrength.strength >= 80
                      ? "bg-green-500"
                      : passwordStrength.strength >= 60
                      ? "bg-yellow-500"
                      : passwordStrength.strength >= 40
                      ? "bg-orange-500"
                      : "bg-red-500"
                  }`}
                  style={{ width: `${passwordStrength.strength}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Confirm New Password */}
      <div className="w-full flex items-center gap-2">
        <Label
          htmlFor="confirmPassword"
          className="w-full flex items-center gap-2"
        >
          Confirm New Password *
        </Label>
        <div className="w-full flex flex-col gap-4">
          <div className="w-full relative">
            <Input
              id="confirmPassword"
              type={showPasswords.confirm ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={(e) =>
                handleInputChange("confirmPassword", e.target.value)
              }
              placeholder="Confirm your new password"
              required
              className="pr-10"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility("confirm")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPasswords.confirm ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>

          {/* Password Match Indicator */}
          {formData.confirmPassword && (
            <div className="text-sm">
              {formData.newPassword === formData.confirmPassword ? (
                <span className="text-green-600">✓ Passwords match</span>
              ) : (
                <span className="text-red-600">✗ Passwords do not match</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting || isLoading}>
          {isSubmitting || isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Updating Password...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Update Password
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
