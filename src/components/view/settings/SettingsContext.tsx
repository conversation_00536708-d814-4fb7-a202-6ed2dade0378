"use client";

/**
 * Settings Context
 *
 * Provides a centralized context for settings-related functionality,
 * integrating RBAC validation hooks and settings state management.
 * Uses company-based architecture instead of profile-based.
 */

import React, { createContext, useContext, useState } from "react";
import { useRBAC, usePermissions } from "@/hooks/useRBAC";
import { useCompany } from "@/hooks/useCompany";
import { usePreference } from "@/hooks/usePreference";
import { useNotifications } from "@/hooks/useNotifications";

// NotificationPreferences interface moved to useNotifications hook

interface SettingsContextValue {
  // RBAC functionality (validation only)
  rbac: {
    currentUser: any;
    isLoading: boolean;
    error?: string;
  };

  // Permissions
  permissions: {
    // Permission functions
    hasPermission: (permission: string) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;

    // Entity-based permission functions
    hasEntityPermission: (entity: string, action: string) => boolean;
    hasAnyEntityPermission: (entity: string, actions: string[]) => boolean;
    hasAllEntityPermissions: (entity: string, actions: string[]) => boolean;

    // User data and convenience flags
    userRole: any;
    canManageUsers: boolean;
    canManageRoles: boolean;
    canManageDocuments: boolean;
    canManageProposals: boolean;
    canViewReports: boolean;
    isAdmin: boolean;
  };

  // Company management (from useCompany hook)
  company: ReturnType<typeof useCompany>;

  // Preference management (from usePreference hook)
  preferences: ReturnType<typeof usePreference>;

  // Notification management (from useNotifications hook)
  notifications: ReturnType<typeof useNotifications>;

  // Settings state
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const SettingsContext = createContext<SettingsContextValue | null>(null);

interface SettingsProviderProps {
  children: React.ReactNode;
}

// Default notification preferences moved to useNotifications hook

export function SettingsProvider({ children }: SettingsProviderProps) {
  // RBAC hooks (validation only)
  const rbacData = useRBAC();
  const permissionsData = usePermissions();

  // New separated hooks
  const companyData = useCompany();
  const preferencesData = usePreference();
  const notificationsData = useNotifications();

  // Local state
  const [activeTab, setActiveTab] = useState("profile");

  // Enhanced permissions with entity-based checks and convenience flags
  const enhancedPermissions = {
    // Include all original permission functions
    ...permissionsData,

    // Entity-based convenience flags
    canManageUsers:
      permissionsData.hasEntityPermission?.("user", "create") ||
      permissionsData.hasEntityPermission?.("user", "update") ||
      permissionsData.hasEntityPermission?.("user", "delete") ||
      permissionsData.hasPermission?.("user:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageRoles:
      permissionsData.hasEntityPermission?.("role", "create") ||
      permissionsData.hasEntityPermission?.("role", "update") ||
      permissionsData.hasEntityPermission?.("role", "delete") ||
      permissionsData.hasPermission?.("role:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageDocuments:
      permissionsData.hasEntityPermission?.("document", "create") ||
      permissionsData.hasEntityPermission?.("document", "update") ||
      permissionsData.hasEntityPermission?.("document", "delete") ||
      permissionsData.hasPermission?.("document:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageProposals:
      permissionsData.hasEntityPermission?.("proposal", "create") ||
      permissionsData.hasEntityPermission?.("proposal", "update") ||
      permissionsData.hasEntityPermission?.("proposal", "delete") ||
      permissionsData.hasPermission?.("proposal:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canViewReports:
      permissionsData.hasEntityPermission?.("report", "read") ||
      permissionsData.hasPermission?.("report:view") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    isAdmin:
      permissionsData.hasPermission?.("admin:all") ||
      permissionsData.hasEntityPermission?.("admin", "all") ||
      false,
  };

  const contextValue: SettingsContextValue = {
    rbac: rbacData,
    permissions: enhancedPermissions,
    company: companyData,
    preferences: preferencesData,
    notifications: notificationsData,
    activeTab,
    setActiveTab,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings context
export function useSettings(): SettingsContextValue {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
}

// Convenience hooks for accessing specific parts of settings
export function useSettingsCompany() {
  const { company } = useSettings();
  return company;
}

export function useSettingsPreferences() {
  const { preferences } = useSettings();
  return preferences;
}

export function useSettingsNotifications() {
  const { notifications } = useSettings();
  return notifications;
}

export function useSettingsPermissions() {
  const { permissions } = useSettings();
  return permissions;
}
