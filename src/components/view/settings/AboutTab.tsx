"use client";

/**
 * About Tab Component
 *
 * Displays information about the application and company attribution.
 */

import React from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import {
  Info,
  Globe,
  Mail,
  Github,
  Twitter,
  Linkedin,
  ExternalLink,
  Heart,
  Code,
  Shield,
  Zap,
} from "lucide-react";

export function AboutTab() {
  const appVersion = "1.0.0";
  const buildDate = new Date().toLocaleDateString();

  const features = [
    {
      icon: Shield,
      title: "Role-Based Access Control",
      description: "Comprehensive RBAC system with dynamic permissions",
    },
    {
      icon: Code,
      title: "Modern Tech Stack",
      description: "Built with Next.js, TypeScript, and Tailwind CSS",
    },
    {
      icon: Zap,
      title: "Real-time Updates",
      description: "Live notifications and real-time collaboration",
    },
  ];

  const teamMembers = [
    {
      name: "Development Team",
      role: "Full-Stack Development",
      description: "Building scalable web applications",
    },
    {
      name: "Design Team",
      role: "UI/UX Design",
      description: "Creating intuitive user experiences",
    },
    {
      name: "DevOps Team",
      role: "Infrastructure & Deployment",
      description: "Ensuring reliable and secure operations",
    },
  ];

  const socialLinks = [
    {
      name: "Website",
      url: "https://tenderbank.company",
      icon: Globe,
    },
    {
      name: "GitHub",
      url: "https://github.com/tenderbank-company",
      icon: Github,
    },
    {
      name: "Twitter",
      url: "https://twitter.com/tenderbank_co",
      icon: Twitter,
    },
    {
      name: "LinkedIn",
      url: "https://linkedin.com/company/tenderbank",
      icon: Linkedin,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Application Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Application Information
          </CardTitle>
          <CardDescription>
            Details about this application and its current version
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-8">
            <div>
              <h4 className="font-medium mb-2">Version Information</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Version:</span>
                  <Badge variant="outline">{appVersion}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Build Date:</span>
                  <span>{buildDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Environment:</span>
                  <Badge
                    variant={
                      process.env.NODE_ENV === "production"
                        ? "default"
                        : "secondary"
                    }
                  >
                    {process.env.NODE_ENV || "development"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle>About tenderbank</CardTitle>
          <CardDescription>
            Learn more about the company behind this application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose prose-sm max-w-none">
            <p className="text-zinc-300">
              tenderbank is a technology company focused on building innovative
              web applications and digital solutions. We specialize in creating
              scalable, secure, and user-friendly platforms that help businesses
              streamline their operations and improve productivity.
            </p>

            <p className="text-zinc-300">
              Our team combines expertise in modern web technologies with a deep
              understanding of business needs to deliver solutions that make a
              real impact. We believe in clean code, thoughtful design, and
              putting users first in everything we build.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Contact & Links */}
      <Card>
        <CardHeader>
          <CardTitle>Connect With Us</CardTitle>
          <CardDescription>
            Get in touch or follow us on social media
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {socialLinks.map((link, index) => (
              <Button
                key={index}
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => window.open(link.url, "_blank")}
              >
                <link.icon className="h-4 w-4" />
                {link.name}
                <ExternalLink className="h-3 w-3" />
              </Button>
            ))}
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Mail className="h-4 w-4" />
              <span>Contact us at: <EMAIL></span>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <span>Made with</span>
              <Heart className="h-4 w-4 text-red-500" />
              <span>by tenderbank</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legal & Privacy */}
      <Card>
        <CardHeader>
          <CardTitle>Legal & Privacy</CardTitle>
          <CardDescription>
            Important information about data handling and terms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Data Privacy</h4>
              <p className="text-gray-600">
                We take your privacy seriously and handle all data in accordance
                with applicable privacy laws and regulations.
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Security</h4>
              <p className="text-gray-600">
                All data is encrypted in transit and at rest. We follow industry
                best practices for security and regularly audit our systems.
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Support</h4>
              <p className="text-gray-600">
                Need help? Our support team is available to assist you with any
                questions or issues you may have.
              </p>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="flex flex-wrap gap-4 text-sm">
            <Button variant="link" className="p-0 h-auto">
              Terms of Service
            </Button>
            <Button variant="link" className="p-0 h-auto">
              Privacy Policy
            </Button>
            <Button variant="link" className="p-0 h-auto">
              Cookie Policy
            </Button>
            <Button variant="link" className="p-0 h-auto">
              Support Center
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 py-4">
        <p>© 2024 tenderbank. All rights reserved.</p>
        <p className="mt-1">
          This application is built with modern web technologies and follows
          best practices for security, performance, and accessibility.
        </p>
      </div>
    </div>
  );
}
