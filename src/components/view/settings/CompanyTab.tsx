"use client";

/**
 * Company Tab Component
 *
 * Manages company information based on the company schema.
 * Handles company details including name, contact info, TIN, and category.
 *
 * This component relies on stateful data from useAuth hook which exposes
 * the company data from the auth slice using the selectCompany selector.
 */

import React, { useState, useEffect } from "react";

import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";

import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import { useCompany } from "@/hooks/useCompany";
import {
  Save,
  Building2,
  Mail,
  MapPin,
  Phone,
  FileText,
  Tag,
} from "lucide-react";

// Company categories for the select dropdown
const COMPANY_CATEGORIES = [
  "Construction",
  "Information Technology",
  "Healthcare",
  "Education",
  "Manufacturing",
  "Agriculture",
  "Transportation",
  "Financial Services",
  "Consulting",
  "Retail",
  "Energy",
  "Telecommunications",
  "Other",
];

export function CompanyTab() {
  // Get company data from auth state using selectCompany selector
  const { company, isLoading } = useAuth();
  const { upsertCompany } = useCompany();

  // Form state for company information
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    address: "",
    phone: "",
    tin: "",
    category: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update form when company data from auth state changes
  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || "",
        email: company.email || "",
        address: company.address || "",
        phone: company.phone || "",
        tin: company.tin || "",
        category: company.category || "",
      });
    }
  }, [company]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCategoryChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      category: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name.trim()) {
      toast.error("Company name is required");
      return;
    }

    if (!formData.tin.trim()) {
      toast.error("TIN (Tax Identification Number) is required");
      return;
    }

    setIsSubmitting(true);

    try {
      const companyData = {
        name: formData.name.trim(),
        email: formData.email.trim() || undefined,
        address: formData.address.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        tin: formData.tin.trim(),
        category: formData.category || undefined,
      };

      // Use upsert to create or update company
      await upsertCompany(companyData);

      // Success toast is handled by the hook
    } catch (error) {
      // Error toast is handled by the hook
      console.error("Company upsert error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            {/* Company Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Company Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter company name"
                required
              />
            </div>

            {/* Email Address */}
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>

            {/* Address - Full Width */}
            <div className="space-y-2">
              <Label htmlFor="address" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Business Address
              </Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                placeholder="Enter complete business address"
              />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            {/* TIN */}
            <div className="space-y-2">
              <Label htmlFor="tin" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                TIN (Tax Identification Number) *
              </Label>
              <Input
                id="tin"
                value={formData.tin}
                onChange={(e) => handleInputChange("tin", e.target.value)}
                placeholder="Enter TIN number"
                required
              />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Business Category
              </Label>
              <Select
                value={formData.category}
                onValueChange={handleCategoryChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select business category" />
                </SelectTrigger>
                <SelectContent>
                  {COMPANY_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Phone Number
              </Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                placeholder="+*********** 789"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting || isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Company Information
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
