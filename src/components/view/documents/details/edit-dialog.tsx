"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Tit<PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Upload, X } from "lucide-react";
import type {
  Document,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";

interface DocumentEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: Document;
  onUpdateDocument: (data: Partial<UpdateDocument>) => Promise<void>;
  isLoading: boolean;
}

export function DocumentEditDialog({
  open,
  onOpenChange,
  document,
  onUpdateDocument,
  isLoading,
}: DocumentEditDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    status: "",
    category: "",
    association_entity: "",
    association_id: "",
    proposalId: "",
  });

  // Initialize form data when document changes
  useEffect(() => {
    if (document) {
      setFormData({
        name: document.name,
        status: document.status,
        category: document.category,
        association_entity: document.association_entity,
        association_id: document.association_id,
        proposalId: document.proposalId || "",
      });
    }
  }, [document]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        name: file.name,
        path: `/documents/${file.name}`,
        file_type: file.type,
        size: file.size.toString(),
        file: file,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await onUpdateDocument({
        name: formData.name,
        status: formData.status as Document["status"],
        category: formData.category,
        association_entity: formData.association_entity,
        association_id: formData.association_id,
        proposalId: formData.proposalId || undefined,
      });
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  const handleRemoveFile = () => {
    setFormData((prev) => ({
      ...prev,
      name: "",
      path: "",
      file_type: "",
      size: "",
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Document</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* File Upload */}
          <div className="space-y-6">
            <Label htmlFor="file">File</Label>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="mt-4">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Click to upload a file
                  </span>
                  <span className="mt-1 block text-sm text-gray-500">
                    or drag and drop
                  </span>
                </label>
                <input
                  id="file-upload"
                  type="file"
                  className="sr-only"
                  onChange={handleFileSelect}
                />
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="text-sm">
                  <div className="font-medium text-gray-900">
                    {document.name}
                  </div>
                  <div className="text-gray-500">
                    {(parseInt(document.size) / 1024 / 1024).toFixed(2)} MB
                  </div>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveFile}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Document Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Document Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter document name"
              required
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, category: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="requirements">Requirements</SelectItem>
                <SelectItem value="specifications">Specifications</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !document}>
              {isLoading ? "Updating..." : "Update Document"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
