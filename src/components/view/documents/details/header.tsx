"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { Card, CardContent } from "@/components/common/ui/card";
import {
  ArrowLeft,
  Edit,
  Trash2,
  Download,
  FileText,
  Image,
  File,
  Eye,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import type { Document } from "@/lib/api/validators/schemas/document";
import { useDocuments } from "@/hooks/useDocuments";

interface DocumentDetailsHeaderProps {
  document: Document;
  onEdit: () => void;
  onDelete: () => void;
}

const getStatusColor = (status: Document["status"]) => {
  switch (status) {
    case "created":
      return "bg-gray-100 text-gray-800";
    case "submitted":
      return "bg-blue-100 text-blue-800";
    case "received":
      return "bg-purple-100 text-purple-800";
    case "negotiating":
      return "bg-orange-100 text-orange-800";
    case "agreed":
      return "bg-green-100 text-green-800";
    case "inprogress":
      return "bg-yellow-100 text-yellow-800";
    case "reviewing":
      return "bg-indigo-100 text-indigo-800";
    case "completed":
      return "bg-emerald-100 text-emerald-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getFileIcon = (fileType: string) => {
  if (!fileType) return;

  if (fileType.startsWith("image/")) {
    return Image;
  } else if (fileType === "application/pdf") {
    return FileText;
  } else {
    return File;
  }
};

const formatFileSize = (bytes: string): string => {
  if (!bytes) return "0 B";
  const numBytes = parseInt(bytes);
  if (numBytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(numBytes) / Math.log(k));
  return parseFloat((numBytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export function DocumentDetailsHeader({
  document,
  onEdit,
  onDelete,
}: DocumentDetailsHeaderProps) {
  const router = useRouter();
  const { downloadDocument } = useDocuments();
  const FileIcon = getFileIcon(document.file_type);

  const handleBack = () => {
    router.back();
  };

  const handleDownload = async () => {
    try {
      await downloadDocument(document);
    } catch (error) {
      console.error("Failed to download document:", error);
      // Fallback to opening in new tab
      window.open(document.path, "_blank");
    }
  };

  return (
    <Card>
      <CardContent className="flex flex-col gap-12">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Documents
          </Button>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="destructive" onClick={onDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="flex flex-row items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              <File className="w-8 h-8 text-gray-600" />
            </div>
          </div>

          <div className="flex-1 gap-8 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h4 className="font-bold text-white truncate">{document.name}</h4>
              <Badge
                variant="secondary"
                className={getStatusColor(document.status)}
              >
                {document.status}
              </Badge>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-sm">
              <div>
                <span className="text-gray-500">Category:</span>
                <div className="font-medium capitalize">
                  {document.category}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Size:</span>
                <div className="font-medium">
                  {formatFileSize(document.size)}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Type:</span>
                <div className="font-medium">{document.file_type}</div>
              </div>
              <div>
                <span className="text-gray-500">Updated:</span>
                <div className="font-medium">
                  {document?.updatedAt
                    ? format(new Date(document?.updatedAt || ""), "MMM d, yyyy")
                    : ""}
                </div>
              </div>
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {document.association_entity && (
                <div>
                  <span className="text-gray-500">Associated with:</span>
                  <div className="font-medium capitalize">
                    {document.association_entity} {document.association_id}
                  </div>
                </div>
              )}
              {document.proposalId && (
                <div>
                  <span className="text-gray-500">Proposal ID:</span>
                  <div className="font-medium">{document.proposalId}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
