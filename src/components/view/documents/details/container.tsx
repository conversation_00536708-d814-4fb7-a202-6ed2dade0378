"use client";

import { useState, useEffect } from "react";
import { useDocuments } from "@/hooks/useDocuments";

import { DocumentDetailsHeader } from "./header";
import { DocumentDetailsContent } from "./content";
import { DocumentEditDialog } from "./edit-dialog";
import { DocumentDeleteDialog } from "./delete-dialog";

import { Card, CardContent } from "@/components/common/ui/card";
import { Skeleton } from "@/components/common/ui/skeleton";
import { toast } from "sonner";

interface DocumentDetailsContainerProps {
  documentId: string;
}

export function DocumentDetailsContainer({
  documentId,
}: DocumentDetailsContainerProps) {
  const {
    currentDocument,
    isLoading,
    isUpdating,
    isDeleting,
    error,
    fetchById,
    update,
    remove,
    clearError,
  } = useDocuments();

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  useEffect(() => {
    if (documentId) {
      fetchById(documentId);
    }
  }, [documentId, fetchById]);

  useEffect(() => {
    if (error) {
      console.error("Document details error:", error);
    }
  }, [error]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleUpdateDocument = async (documentData: any) => {
    if (!currentDocument) return;

    try {
      await update(currentDocument.id, documentData);
      setIsEditDialogOpen(false);
      toast.success("Document updated successfully");
    } catch (error) {
      console.error("Failed to update document:", error);
      toast.error("Failed to update document");
    }
  };

  const handleDeleteDocument = async () => {
    if (!currentDocument) return;

    try {
      await remove(currentDocument.id);
      setIsDeleteDialogOpen(false);
      toast.success("Document deleted successfully");
      // Navigate back to documents list
      window.history.back();
    } catch (error) {
      console.error("Failed to delete document:", error);
      toast.error("Failed to delete document");
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="space-y-2">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-48" />
              </div>
              <div className="flex space-x-2">
                <Skeleton className="h-10 w-20" />
                <Skeleton className="h-10 w-20" />
              </div>
            </div>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error loading document
            </h3>
            <p className="text-gray-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentDocument) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Document not found
            </h3>
            <p className="text-gray-500">
              The document you're looking for doesn't exist or has been deleted.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <DocumentDetailsHeader
        document={currentDocument}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      <DocumentDetailsContent document={currentDocument} />

      <DocumentEditDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        document={currentDocument}
        onUpdateDocument={handleUpdateDocument}
        isLoading={isUpdating}
      />

      <DocumentDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        document={currentDocument}
        onDeleteDocument={handleDeleteDocument}
        isLoading={isDeleting}
      />
    </div>
  );
}
