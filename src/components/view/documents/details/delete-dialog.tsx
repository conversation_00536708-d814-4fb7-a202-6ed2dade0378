"use client";

import { <PERSON><PERSON> } from "@/components/common/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/common/ui/dialog";
import { AlertTriangle } from "lucide-react";
import type { Document } from "@/lib/api/validators/schemas/document";

interface DocumentDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: Document;
  onDeleteDocument: () => Promise<void>;
  isLoading: boolean;
}

export function DocumentDeleteDialog({
  open,
  onOpenChange,
  document,
  onDeleteDocument,
  isLoading,
}: DocumentDeleteDialogProps) {
  const handleDelete = async () => {
    try {
      await onDeleteDocument();
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <div className="flex flex-col items-start gap-4">
            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <DialogTitle>Delete Document</DialogTitle>
              <DialogDescription className="mt-1">
                This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-gray-600">
            Are you sure you want to delete <strong>"{document.name}"</strong>?
            This will permanently remove the document and all associated data.
          </p>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Document"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
