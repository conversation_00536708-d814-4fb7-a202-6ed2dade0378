"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Upload, X } from "lucide-react";
import { useEntityAssociation } from "@/hooks/useEntityAssociation";

interface DocumentCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateDocument: (data: FormData) => Promise<void>;
  isLoading: boolean;
}

export function DocumentCreateDialog({
  open,
  onOpenChange,
  onCreateDocument,
  isLoading,
}: DocumentCreateDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    path: "",
    file_type: "",
    size: "",
    category: "",
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const entityAssociation = useEntityAssociation();

  const handleOpenFileSelector = () => {
    document.getElementById("file-upload")?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setFormData((prev) => ({
        ...prev,
        name: file.name,
        path: `/documents/${file.name}`,
        file_type: file.type,
        size: file.size.toString(),
        file: file,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      alert("Please select a file");
      return;
    }

    try {
      // Ensure serializable values for association data
      const associationTable = entityAssociation.result.association_table || "";
      const associationId = entityAssociation.result.association_id || "";

      const documentData: FormData = new FormData();
      documentData.append("file", selectedFile);
      documentData.append("location", "documents");
      documentData.append("category", formData.category);
      documentData.append("association_entity", associationTable);
      documentData.append("association_id", associationId);

      await onCreateDocument(documentData);

      // Reset form
      setFormData({
        name: "",
        path: "",
        file_type: "",
        size: "",
        category: "",
      });
      setSelectedFile(null);
      entityAssociation.reset();
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setFormData((prev) => ({
      ...prev,
      name: "",
      path: "",
      file_type: "",
      size: "",
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl flex flex-col gap-8">
        <DialogHeader>
          <DialogTitle>Upload New Document</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file">File</Label>
            {!selectedFile ? (
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer"
                onClick={() => handleOpenFileSelector()}
              >
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Click to upload a file
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      or drag and drop
                    </span>
                  </label>
                  <input
                    id="file-upload"
                    type="file"
                    className="sr-only"
                    onChange={handleFileSelect}
                  />
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-sm">
                    <div className="font-medium text-gray-900">
                      {selectedFile.name}
                    </div>
                    <div className="text-gray-500">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {/* Document Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Document Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter document name"
              required
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, category: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="other">Other</SelectItem>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="support">Support</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="requirement">Requirement</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
                <SelectItem value="specification">Specification</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !selectedFile}>
              {isLoading ? "Creating..." : "Create Document"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
