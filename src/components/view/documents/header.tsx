"use client";

import { But<PERSON> } from "@/components/common/ui/button";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { Plus } from "lucide-react";
import { Listing } from "@/layouts/dashboard/details/basic";

interface DocumentHeaderProps {
  onCreateDocument: () => void;
  documentsCount: number;
}

export function DocumentHeader({
  onCreateDocument,
  documentsCount,
}: DocumentHeaderProps) {
  return (
    <Listing.Header
      title="Documents"
      caption={`Manage your project documents and files, ${documentsCount} ${
        documentsCount === 1 ? "document" : "documents"
      }`}
      actions={
        <RBACWrapper
          entity={DEFAULT_ENTITIES.DOCUMENT}
          action={PERMISSION_ACTIONS.CREATE}
        >
          <Button onClick={onCreateDocument}>
            <Plus className="h-4 w-4 mr-2" />
            Upload New Document
          </Button>
        </RBACWrapper>
      }
    />
  );
}
