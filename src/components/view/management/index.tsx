"use client";

import React, { useState, useMemo } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { Clock, CheckCircle, AlertCircle, Activity, Plus } from "lucide-react";

// Mock data for recent activities
const recentActivities = [
  {
    id: "1",
    title: "New contract signed",
    description: "Contract #2024-001 with Acme Corp",
    timestamp: "2 hours ago",
    type: "contract",
    status: "completed",
  },
  {
    id: "2",
    title: "Document uploaded",
    description: "Financial report Q4 2024.pdf",
    timestamp: "4 hours ago",
    type: "document",
    status: "completed",
  },
  {
    id: "3",
    title: "Client meeting scheduled",
    description: "Review meeting with TechStart Inc",
    timestamp: "6 hours ago",
    type: "meeting",
    status: "pending",
  },
  {
    id: "4",
    title: "Proposal submitted",
    description: "Marketing campaign proposal for RetailCo",
    timestamp: "1 day ago",
    type: "proposal",
    status: "pending",
  },
  {
    id: "5",
    title: "Payment received",
    description: "$15,000 from GlobalTech Solutions",
    timestamp: "2 days ago",
    type: "payment",
    status: "completed",
  },
];

// Mock data for dashboard statistics
const dashboardStats = [
  {
    // icon: FileText,
    name: "Total Documents",
    value: 1247,
    valueType: "number" as const,
    caption: "+12% from last month",
    color: "blue" as const,
  },
  {
    // icon: Users,
    name: "Active Clients",
    value: 89,
    valueType: "number" as const,
    caption: "+5 new this week",
    color: "green" as const,
  },
  {
    // icon: DollarSign,
    name: "Revenue",
    value: 125000,
    valueType: "dollar" as const,
    caption: "+8% from last month",
    color: "primary" as const,
  },
  {
    // icon: TrendingUp,
    name: "Growth Rate",
    value: 23.5,
    valueType: "percent" as const,
    caption: "Quarterly growth",
    color: "amber" as const,
  },
];

export function UserManagementContainer() {
  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter activities based on search term and status
  const filteredActivities = useMemo(() => {
    let filtered = recentActivities;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (activity) =>
          activity.title.toLowerCase().includes(searchLower) ||
          activity.description.toLowerCase().includes(searchLower) ||
          activity.type.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (activity) => activity.status === statusFilter
      );
    }

    return filtered;
  }, [searchTerm, statusFilter]);

  // Handle refresh action
  const handleRefresh = () => {
    console.log("Refreshing dashboard data...");
    // In a real app, this would refetch data
  };

  // Activity table columns
  const activityColumns = [
    {
      key: "title",
      label: "Activity",
      render: (item: any) => (
        <div className="flex items-center gap-3">
          <p className="font-medium">{item.title}</p>
          <p className="text-sm text-muted-foreground">{item.description}</p>
        </div>
      ),
    },
    {
      key: "timestamp",
      label: "Time",
      render: (item: any) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {item.timestamp}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (item: any) => (
        <Badge
          variant={item.status === "completed" ? "default" : "secondary"}
          className="capitalize"
        >
          {item.status === "completed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {item.status}
        </Badge>
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "User Management", size: "2xl" }}
        caption="Manage and create your client base"
        actions={
          <Button>
            <Plus size={16} className="mr-2" />
            Onboard new staff
          </Button>
        }
      />

      {/* Statistics Cards */}
      <Listing.Statistics columns="grid-cols-4">
        {dashboardStats.map((stat, index) => (
          <Listing.StatCard
            key={index}
            name={stat.name}
            value={stat.value}
            valueType={stat.valueType}
            caption={stat.caption}
            color={stat.color}
          />
        ))}
      </Listing.Statistics>

      {/* Recent Tenders */}
      <Listing className="space-y-1">
        <Listing.Header title={{ text: "Platform Users", size: "md" }} />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={handleRefresh}
          loading={false}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={statusFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("all")}
              >
                All
              </Button>
              <Button
                variant={statusFilter === "completed" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("completed")}
              >
                Completed
              </Button>
              <Button
                variant={statusFilter === "pending" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("pending")}
              >
                Pending
              </Button>
            </div>
          }
        />

        {/* Main Content Grid */}
        <Listing.Table
          data={filteredActivities}
          columns={activityColumns}
          loading={false}
          emptyState={
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || statusFilter !== "all"
                  ? "No activities match your filters"
                  : "No recent activities"}
              </p>
            </div>
          }
        />
      </Listing>
    </Listing>
  );
}
