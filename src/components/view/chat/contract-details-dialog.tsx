"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Separator } from "@/components/common/ui/separator";
import { ScrollArea } from "@/components/common/ui/scroll-area";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { useContracts } from "@/hooks/useContracts";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import {
  Calendar,
  DollarSign,
  FileText,
  Clock,
  CheckCircle,
  X,
  ExternalLink,
  User,
  Building,
} from "lucide-react";
import { useRouter } from "next/navigation";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";

interface ContractDetailsDialogProps {
  contractId: string | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const statusColors = {
  draft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
  active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
  completed: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
  terminated: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
  expired:
    "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
};

export function ContractDetailsDialog({
  contractId,
  isOpen,
  onOpenChange,
}: ContractDetailsDialogProps) {
  const router = useRouter();
  const { currentContract, isLoading, error, fetchContract, clearError } =
    useContracts();

  useEffect(() => {
    if (contractId && isOpen) {
      fetchContract(contractId);
    }
  }, [contractId, isOpen, fetchContract]);

  useEffect(() => {
    if (error) {
      console.error("Contract details error:", error);
    }
  }, [error]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getDaysRemaining = () => {
    if (!currentContract) return 0;
    const endDate = new Date(currentContract.end_date);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getContractDuration = () => {
    if (!currentContract) return 0;
    const startDate = new Date(currentContract.start_date);
    const endDate = new Date(currentContract.end_date);
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleViewFullContract = () => {
    if (currentContract) {
      router.push(`/${slug}/contracts/${currentContract.id}`);
      onOpenChange(false);
    }
  };

  const renderLoadingState = () => (
    <div className="space-y-4 p-6">
      <div className="animate-pulse">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-6"></div>
        <div className="space-y-3">
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    </div>
  );

  const renderErrorState = () => (
    <div className="text-center py-12">
      <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Contract Not Found
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        The contract you're looking for doesn't exist or you don't have
        permission to view it.
      </p>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[95vh]">
        <DialogHeader className="w-full flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">
            Contract Summary
          </DialogTitle>
          <div className="flex items-center gap-2">
            {currentContract && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewFullContract}
                className="gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                View Full Contract
              </Button>
            )}
          </div>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(92vh-120px)]">
          <RBACWrapper
            entity={DEFAULT_ENTITIES.CONTRACT}
            action={PERMISSION_ACTIONS.READ}
          >
            {isLoading && renderLoadingState()}
            {error && renderErrorState()}
            {!isLoading && !error && currentContract && (
              <div className="space-y-6 p-1">
                {/* Contract Header */}
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex flex-col gap-2">
                      <h2 className="text-2xl leading-tight font-bold text-gray-900 dark:text-white">
                        {currentContract.title}
                      </h2>
                      <p className="text-gray-600 dark:text-gray-400">
                        {currentContract.description.slice(0, 50)}
                      </p>
                    </div>
                    <Badge
                      className={
                        statusColors[
                          currentContract.status as keyof typeof statusColors
                        ] || statusColors.draft
                      }
                    >
                      {currentContract.status?.charAt(0).toUpperCase() +
                        currentContract.status?.slice(1)}
                    </Badge>
                  </div>
                </div>

                <Separator />

                {/* Contract Overview */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-green-600" />
                        Financial Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Total Value:
                        </span>
                        <span className="font-semibold">
                          {formatCurrency(currentContract.total_value || 0)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Paid Amount:
                        </span>
                        <span className="font-medium">
                          {formatCurrency(currentContract.paid_value || 0)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Remaining:
                        </span>
                        <span className="font-medium">
                          {formatCurrency(currentContract.remaining_value || 0)}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-blue-600" />
                        Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Start Date:
                        </span>
                        <span className="font-medium">
                          {formatDate(currentContract.start_date)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          End Date:
                        </span>
                        <span className="font-medium">
                          {formatDate(currentContract.end_date)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Duration:
                        </span>
                        <span className="font-medium">
                          {getContractDuration()} days
                        </span>
                      </div>
                      {currentContract.status === "active" && (
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">
                            Days Remaining:
                          </span>
                          <span className="font-medium text-orange-600">
                            {getDaysRemaining()} days
                          </span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Contract Parties */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-5 w-5 text-purple-600" />
                      Contract Parties
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex flex-col gap-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          Client
                        </h4>
                        <p className="text-gray-600 dark:text-gray-400">
                          {currentContract.client?.name} -{" "}
                          {currentContract.client?.email}
                        </p>
                      </div>
                      <div className="flex flex-col gap-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          Contract ID
                        </h4>
                        <p className="text-gray-600 dark:text-gray-400">
                          {currentContract.id}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </RBACWrapper>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
