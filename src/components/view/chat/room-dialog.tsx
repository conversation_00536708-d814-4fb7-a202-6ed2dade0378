"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/common/ui/button";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { ContractCombobox } from "./comboboxes";
import { MessageCircleMore, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useRooms } from "@/hooks/useRooms";
import type { CreateRoom } from "@/lib/api/validators/schemas/chat";

interface RoomDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode?: "create" | "edit";
  contractId?: string;
  room?: {
    id: string;
    name: string;
    about?: string;
    contractId?: string;
    members: Array<{
      id: string;
      accountId: string;
    }>;
  };
}

export function RoomDialog({
  open,
  onOpenChange,
  mode = "create",
  contractId,
  room,
}: RoomDialogProps) {
  const { createRoom, updateRoom, isCreatingRoom } = useRooms();

  const [formData, setFormData] = useState({
    name: "",
    about: "",
    contractId: contractId || "",
    members: [] as string[],
  });

  const [selectedMembers, setSelectedMembers] = useState<
    Array<{
      id: string;
      name?: string;
      email: string;
      image?: string;
    }>
  >([]);

  // Initialize form data when dialog opens or room changes
  useEffect(() => {
    if (open) {
      if (mode === "edit" && room) {
        setFormData({
          name: room.name,
          about: room.about || "",
          contractId: room.contractId || contractId || "",
          members: [],
        });

        setSelectedMembers([]);
      } else {
        setFormData({
          name: "",
          about: "",
          contractId: contractId || "",
          members: [],
        });
        setSelectedMembers([]);
      }
    }
  }, [open, mode, room, contractId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.contractId.trim()) {
      toast.error("Room name is required");
      return;
    }

    try {
      if (mode === "create") {
        const roomData: CreateRoom = {
          contractId: formData.contractId || contractId || undefined,
        };

        await createRoom(roomData);

        toast.success(
          selectedMembers.length > 0
            ? `Room created successfully! ${selectedMembers.length} member(s) selected (manual addition required).`
            : "Room created successfully!"
        );
      } else {
        if (!room) return;
        updateRoom(room.id, formData);
        toast.info("Room editing functionality coming soon");
      }
    } catch (error) {
      console.error("Failed to create/update room:", error);
      toast.error(
        mode === "create" ? "Failed to create room" : "Failed to update room"
      );
    } finally {
      // Reset form
      setFormData({
        name: "",
        about: "",
        contractId: contractId || "",
        members: [],
      });
      setSelectedMembers([]);
      onOpenChange(false);
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const isLoading = isCreatingRoom;
  const title = mode === "create" ? "Create Room" : "Edit Room";
  const submitText = mode === "create" ? "Create Room" : "Update Room";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <MessageCircleMore className="h-5 w-5" />
            <span>{title}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {!contractId && (
            <div className="space-y-2">
              <Label htmlFor="contract-select">
                Link to Contract (Optional)
              </Label>
              <ContractCombobox
                value={formData.contractId || undefined}
                onValueChange={(value) =>
                  handleInputChange("contractId", value || "")
                }
                placeholder="Select a contract to link this room..."
                disabled={isLoading}
              />
            </div>
          )}

          {/* Contract Association Info */}
          {contractId && mode === "create" && (
            <div className="rounded-md bg-blue-50 dark:bg-blue-950 p-3">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                This room will be associated with the current contract.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.contractId.trim()}
            >
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {submitText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
