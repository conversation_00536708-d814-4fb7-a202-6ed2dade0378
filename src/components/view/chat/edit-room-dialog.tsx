"use client";

import { RoomDialog } from "./room-dialog";

interface EditRoomDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  room: {
    id: string;
    name: string;
    about?: string;
    contractId?: string;
  };
}

export function EditRoomDialog({
  open,
  onOpenChange,
  room,
}: EditRoomDialogProps) {
  return (
    <RoomDialog
      open={open}
      onOpenChange={onOpenChange}
      mode="edit"
      room={room}
    />
  );
}
