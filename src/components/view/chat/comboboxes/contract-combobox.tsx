"use client";

import { useState } from "react";
import { useContracts } from "@/hooks/useContracts";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@/components/common/ui/combobox";
import { Badge } from "@/components/common/ui/badge";
import { Building2, Calendar, DollarSign } from "lucide-react";
import type { Contract } from "@/lib/api/validators/schemas/contract";

interface ContractComboboxProps {
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function ContractCombobox({
  value,
  onValueChange,
  placeholder = "Select a contract...",
  disabled = false,
}: ContractComboboxProps) {
  const { contracts, isLoading, error } = useContracts();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // Find selected contract
  const selectedContract = contracts.find(
    (contract: Contract) => contract.id === value
  );

  // Filter contracts based on search
  const filteredContracts = contracts.filter(
    (contract: Contract) =>
      contract?.proposal?.name
        .toLowerCase()
        .includes(searchValue.toLowerCase()) ||
      contract?.client?.name
        .toLowerCase()
        .includes(searchValue.toLowerCase()) ||
      contract?.status.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Group contracts by status
  const groupedContracts = filteredContracts.reduce(
    (acc: Record<string, Contract[]>, contract: Contract) => {
      const status = contract.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(contract);
      return acc;
    },
    {}
  );

  const handleSelect = (contractId: string) => {
    if (contractId === value) {
      onValueChange(undefined); // Deselect if same contract is clicked
    } else {
      onValueChange(contractId);
    }
    setOpen(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "terminated":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "expired":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (error) {
    return (
      <div className="text-sm text-red-600 dark:text-red-400">
        Failed to load contracts
      </div>
    );
  }

  return (
    <Combobox open={open} onOpenChange={setOpen}>
      <ComboboxTrigger
        disabled={disabled || isLoading}
        placeholder={isLoading ? "Loading contracts..." : placeholder}
      >
        {selectedContract ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="truncate">{selectedContract.title}</span>
            </div>
            <Badge
              variant="secondary"
              className={`text-xs ${getStatusColor(selectedContract.status)}`}
            >
              {selectedContract.status}
            </Badge>
          </div>
        ) : null}
      </ComboboxTrigger>

      <ComboboxContent className="w-[400px]">
        <ComboboxCommand>
          <ComboboxInput
            placeholder="Search contracts..."
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <ComboboxList>
            <ComboboxEmpty>
              {isLoading ? "Loading contracts..." : "No contracts found."}
            </ComboboxEmpty>

            {Object.entries(groupedContracts).map(
              ([status, statusContracts]) => (
                <ComboboxGroup
                  key={status}
                  heading={status.charAt(0).toUpperCase() + status.slice(1)}
                >
                  {(statusContracts as Contract[]).map((contract: Contract) => (
                    <ComboboxItem
                      key={contract.id}
                      value={contract.id}
                      onSelect={() => handleSelect(contract.id)}
                      className="flex flex-col items-start space-y-1 p-3"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center space-x-2">
                          <Building2 className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{contract.title}</span>
                        </div>
                        <ComboboxItemIndicator
                          isSelected={contract.id === value}
                        />
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-muted-foreground w-full">
                        <div className="flex items-center space-x-1">
                          <Building2 className="h-3 w-3" />
                          <span>{contract.client_name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-3 w-3" />
                          <span>{formatCurrency(contract.contract_value)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(contract.start_date)}</span>
                        </div>
                      </div>

                      {contract.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 w-full">
                          {contract.description}
                        </p>
                      )}
                    </ComboboxItem>
                  ))}
                </ComboboxGroup>
              )
            )}
          </ComboboxList>
        </ComboboxCommand>
      </ComboboxContent>
    </Combobox>
  );
}
