"use client";

import { RoomDialog } from "./room-dialog";

interface CreateRoomDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contractId?: string;
}

export function CreateRoomDialog({
  open,
  onOpenChange,
  contractId,
}: CreateRoomDialogProps) {
  return (
    <RoomDialog
      open={open}
      onOpenChange={onOpenChange}
      mode="create"
      contractId={contractId}
    />
  );
}
