"use client";

import React, { useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { But<PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import { Clock, CheckCircle, AlertCircle, Package } from "lucide-react";
import { useTenders } from "@/hooks/useTenders";
import type { TenderResponse } from "@/store/actions/tender";

export function TendersContainer() {
  // Filter state for tenders
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  // Use tenders hook with integrated search and pagination
  const {
    tenders,
    searchTerm,
    pagination,
    isLoading,
    isSearching,
    error,
    refreshTenders,
    search: searchTenders,
    clearSearch,
    goToPage,
    nextPage,
    previousPage,
  } = useTenders({
    page: 1,
    limit: 20,
    status: statusFilter !== "all" ? statusFilter : undefined,
    category: categoryFilter !== "all" ? categoryFilter : undefined,
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // Handle search for tenders
  const handleTenderSearch = async (query: string) => {
    if (query.trim()) {
      await searchTenders(query, {
        status: statusFilter !== "all" ? statusFilter : undefined,
        category: categoryFilter !== "all" ? categoryFilter : undefined,
      });
    } else {
      clearSearch();
    }
  };

  // Handle refresh action for tenders
  const handleRefresh = () => {
    refreshTenders();
  };

  // Reset tender filters
  const resetTenderFilters = () => {
    clearSearch();
    setStatusFilter("all");
    setCategoryFilter("all");
    goToPage(1);
  };

  // Tender table columns
  const tenderColumns = [
    {
      key: "ocid",
      label: "OCID",
      render: (tender: TenderResponse) => (
        <div className="flex flex-col">
          <p className="font-medium text-sm">{tender.ocid}</p>
          {tender.category && (
            <Badge variant="outline" className="w-fit mt-1 text-xs">
              {tender.category}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "description",
      label: "Description",
      render: (tender: TenderResponse) => (
        <div className="max-w-md">
          <p className="text-sm line-clamp-2">
            {tender.description || "No description available"}
          </p>
          {tender.procuring_entity && (
            <p className="text-xs text-muted-foreground mt-1 capitalize">
              {tender.procuring_entity}
            </p>
          )}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (tender: TenderResponse) => (
        <Badge
          variant={
            tender.status === "active" || tender.status === "online"
              ? "default"
              : tender.status === "completed" || tender.status === "closed"
              ? "secondary"
              : "outline"
          }
          className="capitalize"
        >
          {tender.status === "active" || tender.status === "online" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : tender.status === "completed" || tender.status === "closed" ? (
            <CheckCircle className="h-3 w-3 mr-1" />
          ) : (
            <AlertCircle className="h-3 w-3 mr-1" />
          )}
          {tender.status || "unknown"}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (tender: TenderResponse) => (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          {new Date(tender.createdAt).toLocaleDateString()}
        </div>
      ),
    },
  ];

  return (
    <Listing className="p-6 space-y-6">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "Tender Management", size: "2xl" }}
        caption="Manage and create new tenders"
        actions={
          <Button variant="secondary">
            <LottieIconPlayer
              icon={LottieIconLib.add}
              size={16}
              className="mr-2"
            />
            Create New Tender
          </Button>
        }
      />

      {/* Recent Tenders */}
      <Listing className="space-y-1">
        <Listing.Header title={{ text: "Active Tenders", size: "md" }} />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={handleTenderSearch}
          onRefresh={handleRefresh}
          loading={isLoading || isSearching}
          customActions={
            <div className="flex items-center gap-2 flex-wrap">
              {/* Status Filters */}
              <div className="flex items-center gap-1">
                <Button
                  variant={statusFilter === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter("all")}
                >
                  All Status
                </Button>
                <Button
                  variant={statusFilter === "active" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter("active")}
                >
                  Active
                </Button>
                <Button
                  variant={statusFilter === "completed" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter("completed")}
                >
                  Completed
                </Button>
                <Button
                  variant={statusFilter === "pending" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter("pending")}
                >
                  Pending
                </Button>
                <Button
                  variant={statusFilter === "closed" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter("closed")}
                >
                  Closed
                </Button>
              </div>

              {/* Reset Filters */}
              <Button
                variant="ghost"
                size="sm"
                onClick={resetTenderFilters}
                className="text-muted-foreground"
              >
                Reset
              </Button>
            </div>
          }
        />

        {/* Tenders Table */}
        <Listing.Table
          data={tenders}
          columns={tenderColumns}
          loading={isLoading}
          emptyState={
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {error
                  ? "Failed to load tenders"
                  : searchTerm ||
                    statusFilter !== "all" ||
                    categoryFilter !== "all"
                  ? "No tenders match your search criteria"
                  : "No tenders available"}
              </p>
              {error ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="mt-2"
                >
                  Try Again
                </Button>
              ) : searchTerm ||
                statusFilter !== "all" ||
                categoryFilter !== "all" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetTenderFilters}
                  className="mt-2"
                >
                  Clear Filters
                </Button>
              ) : null}
            </div>
          }
        />

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex justify-between items-center mt-4 pt-4 border-t">
            <span className="text-sm text-muted-foreground">
              Showing {tenders.length} of {pagination.total} tenders
              {searchTerm && ` (filtered by "${searchTerm}")`}
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={previousPage}
                disabled={!pagination.hasPreviousPage || isLoading}
              >
                Previous
              </Button>
              <span className="px-3 py-1 text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={nextPage}
                disabled={!pagination.hasNextPage || isLoading}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Listing>
    </Listing>
  );
}
