{"v": "5.12.1", "fr": 60, "ip": 0, "op": 60, "w": 240, "h": 240, "nm": "system-solid-72-photo", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "guide", "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 8, "s": [0], "h": 1}, {"t": 48, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.333], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.539], "y": [1]}, "o": {"x": [0.377], "y": [0]}, "t": 8, "s": [10]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [2]}, {"t": 60, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [120, 58.056, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -61.944, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Union 3", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -61.944, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -61.944, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [9.665, 0], [0, 0], [0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665]], "o": [[0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665], [0, 0], [9.665, 0], [0, 0]], "v": [[100, -62.5], [82.5, -80], [-82.5, -80], [-100, -62.5], [-100, 62.5], [-82.5, 80], [82.5, 80], [100, 62.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-29.267, -27.783], [-18.939, -27.517], [25.745, 17.942], [49.498, -6.375], [49.769, -6.643], [54.904, -8.677], [60.31, -6.375], [84.981, 21.729], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-85, 28.042], [-84.953, 27.892], [-29.547, -27.517]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.523, -28.069], [-22.196, -27.804], [22.982, 17.376], [47.094, -7.698], [47.365, -7.967], [52.5, -10], [57.906, -7.698], [85, 20.479], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-85, 25.374], [-85, 24.393], [-32.804, -27.804]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 5.523], [-5.523, 0], [0, -5.523], [5.523, 0]], "o": [[0, -5.523], [5.523, 0], [0, 5.523], [-5.523, 0]], "v": [[10, -40], [20, -50], [30, -40], [20, -30]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-solid-72-photo').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 49, "op": 75, "st": 49, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Union 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 8, "s": [0], "h": 1}, {"t": 48, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.333], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.539], "y": [1]}, "o": {"x": [0.377], "y": [0]}, "t": 8, "s": [10]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 41, "s": [2]}, {"t": 60, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [120, 58.056, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, -61.944, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [9.665, 0], [0, 0], [0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665]], "o": [[0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665], [0, 0], [9.665, 0], [0, 0]], "v": [[100, -62.5], [82.5, -80], [-82.5, -80], [-100, -62.5], [-100, 62.5], [-82.5, 80], [82.5, 80], [100, 62.5]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [9.665, 0], [0, 0], [0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665]], "o": [[0, -9.665], [0, 0], [-9.665, 0], [0, 0], [0, 9.665], [0, 0], [9.665, 0], [0, 0]], "v": [[100, -62.5], [82.5, -80], [-82.5, -80], [-100, -62.5], [-100, 62.5], [-82.5, 80], [82.5, 80], [100, 62.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.523, -28.069], [-22.196, -27.804], [22.982, 17.376], [47.094, -7.698], [47.365, -7.967], [52.5, -10], [57.906, -7.698], [85, 20.479], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-85, 24.393], [-32.804, -27.804]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0, 0], [-2.836, -2.808], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0]], "o": [[3.794, -4.297], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.863, -23.831], [-27.228, -26.654], [18.836, 18.487], [44.268, -7.113], [44.539, -7.381], [49.674, -9.415], [55.08, -7.113], [89.357, 28.179], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-90.674, 27.757], [-40.693, -23.04]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [-2.835, -2.798], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0]], "o": [[2.474, -2.378], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0]], "v": [[-42.885, -23.844], [-35.099, -22.876], [13.453, 20.199], [39.983, -7.714], [40.255, -7.982], [45.389, -10.015], [50.796, -7.714], [87.547, 33.484], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-93.932, 22.686], [-43.898, -22.878]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, 1.381], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, 1.381], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.523, -28.069], [-22.196, -27.804], [22.982, 17.376], [47.094, -7.698], [47.365, -7.967], [52.5, -10], [57.906, -7.698], [85, 20.479], [85, 62.5], [82.5, 65], [-82.5, 65], [-85, 62.5], [-85, 24.393], [-32.804, -27.804]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 5.523], [-5.523, 0], [0, -5.523], [5.523, 0]], "o": [[0, -5.523], [5.523, 0], [0, 5.523], [-5.523, 0]], "v": [[10, -40], [20, -50], [30, -40], [20, -30]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 5.523], [-5.523, 0], [0, -5.523], [5.523, 0]], "o": [[0, -5.523], [5.523, 0], [0, 5.523], [-5.523, 0]], "v": [[10, -40], [20, -50], [30, -40], [20, -30]], "c": true}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-solid-72-photo').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 9, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "box", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.523, 0], [0, 0], [0, -5.523], [0, 0], [5.523, 0], [0, 0], [0, 5.523], [0, 0]], "o": [[0, 0], [5.523, 0], [0, 0], [0, 5.523], [0, 0], [-5.523, 0], [0, 0], [0, -5.523]], "v": [[-82.5, -72.5], [82.5, -72.5], [92.5, -62.5], [92.5, 62.5], [82.5, 72.5], [-82.5, 72.5], [-92.5, 62.5], [-92.5, -62.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 10, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-solid-72-photo').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "box", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 49, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Solid 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-0.441, 2.523], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [-4.043, -0.203], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [-0.931, -4.136], [0.351, -2.01], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [4.962, 0.249], [0, 0], [0, 0], [0, 0]], "v": [[47.387, -7.995], [58.089, -7.604], [86.703, 22.303], [86.534, 26.217], [86.806, 25.948], [86.753, 21.165], [87.598, 19.843], [86.733, 19.971], [86.633, -67.969], [84.133, -71.045], [-89.473, -72.449], [-91.973, -69.372], [-92.341, 28.66], [-37.973, -22.301], [-27.707, -29.922], [-17.539, -23.17], [22.776, 17.549], [47.107, -7.729]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.913, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [-4.341, 0.13], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [4.601, -0.138], [0, 0], [0, 0], [0, 0]], "v": [[45.204, -7.184], [55.531, -6.918], [89.494, 29.003], [89.944, 24.862], [90.215, 24.593], [91.487, 21.428], [91.769, 20.543], [90.904, 20.671], [86.633, -67.969], [84.133, -71.045], [-86.723, -72.199], [-89.223, -69.122], [-91.971, 30.254], [-42.572, -20.488], [-31.628, -27.72], [-21.897, -21.701], [19.144, 19.469], [44.924, -6.918]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [-3.629, 0.033], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [4.068, -0.037], [0, 0], [0, 0], [0, 0]], "v": [[15.931, -15.061], [26.258, -14.795], [65.628, 25.138], [82.495, 6.377], [82.767, 6.108], [87.902, 4.075], [93.308, 6.377], [89.455, 19.172], [89.102, -68.174], [86.602, -71.251], [-89.791, -70.895], [-92.291, -67.818], [-93.515, 0.938], [-74.014, -17.408], [-68.912, -19.061], [-61.445, -14.836], [-22.811, 24.067], [15.651, -14.795]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.369, -21.468], [-0.042, -21.202], [42.299, 21.229], [63.209, -0.449], [63.481, -0.718], [68.615, -2.751], [74.022, -0.449], [91.683, 18.519], [91.123, -68.343], [88.623, -71.419], [-87.941, -71.115], [-90.441, -68.039], [-93.022, -40.906], [-92.135, -5.131], [-92.141, -3.233], [-89.162, -0.563], [-60.638, 28.542], [-10.65, -21.202]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-27.507, -26.405], [-17.18, -26.139], [27.194, 18.619], [50.852, -5.223], [51.124, -5.492], [56.258, -7.525], [61.665, -5.223], [91.976, 26.223], [89.3, -67.891], [86.8, -70.967], [-83.166, -70.898], [-85.666, -67.822], [-93.106, -40.708], [-92.873, 8.579], [-92.861, 11.086], [-92.333, 13.135], [-87.27, 32.781], [-27.788, -26.139]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [-2.837, -2.837], [0, 0], [0, 0], [0, 0], [-1.912, 0], [-1.414, -1.47], [0, 0], [0, 0], [1.381, 0], [0, 0], [0, -1.699], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[2.944, -2.66], [0, 0], [0, 0], [0, 0], [1.387, -1.303], [2.04, 0], [0, 0], [0, 0], [0, -1.699], [0, 0], [-1.381, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.523, -28.069], [-22.196, -27.804], [22.982, 17.376], [47.094, -7.698], [47.365, -7.967], [52.5, -10], [57.906, -7.698], [87.75, 26.457], [87.561, -67.859], [85.061, -70.935], [-86.439, -70.828], [-88.939, -67.752], [-88.955, -42.804], [-88.968, -23.007], [-88.968, -22], [-88.971, -17.288], [-89, 27.893], [-32.804, -27.804]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 5.523], [-5.523, 0], [0, -5.523], [5.523, 0]], "o": [[0, -5.523], [5.523, 0], [0, 5.523], [-5.523, 0]], "v": [[10, -40], [20, -50], [30, -40], [20, -30]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-solid-72-photo').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-solid-72-photo').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}], "ip": 0, "op": 50, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [1, 1, 1], "ix": 1}}]}], "ip": 0, "op": 131, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [120, 120, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [120, 120, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 240, "h": 240, "ip": 0, "op": 70, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 60}], "props": {}}