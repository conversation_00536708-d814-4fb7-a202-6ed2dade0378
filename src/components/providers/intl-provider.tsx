"use client";

import { NextIntlClientProvider } from "next-intl";
import { ReactNode } from "react";
import { usePreference } from "@/hooks/usePreference";

interface IntlProviderProps {
  children: ReactNode;
  messages: any;
  locale: string;
}

function IntlProviderInner({ children, messages, locale }: IntlProviderProps) {
  const { timezone, dateFormat } = usePreference();

  return (
    <NextIntlClientProvider
      messages={messages}
      locale={locale}
      timeZone={timezone || "UTC"}
      formats={{
        dateTime: {
          short: {
            day: "numeric",
            month: "short",
            year: "numeric",
          },
          medium: {
            day: "numeric",
            month: "long",
            year: "numeric",
          },
          long: {
            day: "numeric",
            month: "long",
            year: "numeric",
            weekday: "long",
          },
        },
        number: {
          currency: {
            style: "currency",
            currency: "USD",
          },
        },
      }}
    >
      {children}
    </NextIntlClientProvider>
  );
}

export function IntlProvider({
  children,
  messages,
  locale,
}: IntlProviderProps) {
  return (
    <IntlProviderInner messages={messages} locale={locale}>
      {children}
    </IntlProviderInner>
  );
}
