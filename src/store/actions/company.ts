"use client";

import { api } from "@/lib/common/requests";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { setLoading } from "../slices/auth";

// Company data interface
export interface CompanyData {
  name: string;
  tin: string;
  category?: string;
  email?: string;
  address?: string;
  phone?: string;
}

export interface CompanyResponse {
  id: string;
  name: string;
  tin: string;
  category?: string;
  email?: string;
  address?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Create company
export const createCompany = createAsyncThunk(
  "company/create",
  async (companyData: CompanyData, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await api.post("company", companyData);

      if (result.success && !result.error) {
        return result.data as CompanyResponse;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to create company",
        });
      }
    } catch (error) {
      console.error("Failed to create company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Update company
export const updateCompany = createAsyncThunk(
  "company/update",
  async (
    { id, ...companyData }: CompanyData & { id: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await api.put(`company/${id}`, companyData);

      if (result.success && !result.error) {
        return result.data as CompanyResponse;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to update company",
        });
      }
    } catch (error) {
      console.error("Failed to update company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Get company by ID
export const getCompany = createAsyncThunk(
  "company/get",
  async (id: string, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await api.get(`company/${id}`);

      if (result.success && !result.error) {
        return result.data as CompanyResponse;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to get company",
        });
      }
    } catch (error) {
      console.error("Failed to get company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Get current user's company
export const getCurrentUserCompany = createAsyncThunk(
  "company/getCurrentUser",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await api.get("company/current");

      if (result.success && !result.error) {
        return result.data as CompanyResponse;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to get current user's company",
        });
      }
    } catch (error) {
      console.error("Failed to get current user's company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get current user's company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Delete company
export const deleteCompany = createAsyncThunk(
  "company/delete",
  async (id: string, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await api.delete(`company/${id}`);

      if (result.success && !result.error) {
        return { id };
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to delete company",
        });
      }
    } catch (error) {
      console.error("Failed to delete company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Upsert company (create or update)
export const upsertCompany = createAsyncThunk(
  "company/upsert",
  async (companyData: CompanyData, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await api.post("company/upsert", companyData);

      if (result.success && !result.error) {
        return result.data as CompanyResponse;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to upsert company",
        });
      }
    } catch (error) {
      console.error("Failed to upsert company:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to upsert company";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Get all companies (admin only)
export const getAllCompanies = createAsyncThunk(
  "company/getAll",
  async (
    params: { page?: number; limit?: number; search?: string } = {},
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.search) queryParams.append("search", params.search);

      const result = await api.get(`company?${queryParams.toString()}`);

      if (result.success && !result.error) {
        return result.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to get companies",
        });
      }
    } catch (error) {
      console.error("Failed to get companies:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get companies";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);
