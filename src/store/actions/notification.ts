import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { api } from "@/lib/common/requests";

// Base API URL
const API_BASE = "/api/notification";

// Types for notification actions
export interface NotificationQuery {
  page?: number;
  limit?: number;
  type?: string;
  category?: string;
  isRead?: boolean;
  isDeleted?: boolean;
  priority?: string;
  sortBy?: "createdAt" | "updatedAt" | "priority";
  sortOrder?: "asc" | "desc";
}

export interface NotificationUpdate {
  isRead?: boolean;
  isDeleted?: boolean;
  readAt?: Date | null;
  deletedAt?: Date | null;
}

export interface BulkActionRequest {
  action:
    | "markAllAsRead"
    | "markAsRead"
    | "markAsUnread"
    | "delete"
    | "softDelete"
    | "getCounts";
  notificationIds?: string[];
}

// Async thunk actions
export const fetchNotifications = createAsyncThunk(
  "notifications/fetchNotifications",
  async (query: NotificationQuery = {}, { rejectWithValue }) => {
    try {
      const data = await api.get("notification", query);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to fetch notifications");
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const fetchNotification = createAsyncThunk(
  "notifications/fetchNotification",
  async (id: string, { rejectWithValue }) => {
    try {
      const data = await api.get(`notification/${id}`);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to fetch notification");
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const createNotification = createAsyncThunk(
  "notifications/createNotification",
  async (notificationData: any, { rejectWithValue }) => {
    try {
      const data = await api.post("notification", notificationData);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to create notification");
      }

      toast.success("Notification created successfully");
      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const updateNotification = createAsyncThunk(
  "notifications/updateNotification",
  async (
    { id, updates }: { id: string; updates: NotificationUpdate },
    { rejectWithValue }
  ) => {
    try {
      const data = await api.patch(`notification/${id}`, updates);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to update notification");
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const deleteNotification = createAsyncThunk(
  "notifications/deleteNotification",
  async (id: string, { rejectWithValue }) => {
    try {
      const data = await api.delete(`notification/${id}`);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to delete notification");
      }

      toast.success("Notification deleted successfully");
      return id;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const markAsRead = createAsyncThunk(
  "notifications/markAsRead",
  async (id: string, { rejectWithValue }) => {
    try {
      const data = await api.patch(`notification/${id}`, {
        isRead: true,
        readAt: new Date(),
      });

      if (!data.success) {
        return rejectWithValue(
          data.message || "Failed to mark notification as read"
        );
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const markAsUnread = createAsyncThunk(
  "notifications/markAsUnread",
  async (id: string, { rejectWithValue }) => {
    try {
      const data = await api.patch(`notification/${id}`, {
        isRead: false,
        readAt: null,
      });

      if (!data.success) {
        return rejectWithValue(
          data.message || "Failed to mark notification as unread"
        );
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const performBulkAction = createAsyncThunk(
  "notifications/performBulkAction",
  async (request: BulkActionRequest, { rejectWithValue }) => {
    try {
      const data = await api.post("notification/actions", request);

      if (!data.success) {
        return rejectWithValue(data.message || "Failed to perform bulk action");
      }

      // Show success toast for certain actions
      if (request.action === "markAllAsRead") {
        toast.success("All notifications marked as read");
      } else if (request.action === "delete") {
        toast.success("Selected notifications deleted");
      } else if (request.action === "markAsRead") {
        toast.success("Selected notifications marked as read");
      } else if (request.action === "markAsUnread") {
        toast.success("Selected notifications marked as unread");
      }

      return { action: request.action, data: data.data };
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

export const getNotificationCounts = createAsyncThunk(
  "notifications/getNotificationCounts",
  async (_, { rejectWithValue }) => {
    try {
      const data = await api.post("notification/actions", {
        action: "getCounts",
      });

      if (!data.success) {
        return rejectWithValue(
          data.message || "Failed to get notification counts"
        );
      }

      return data.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Network error occurred"
      );
    }
  }
);

// Convenience actions
export const markAllAsRead = () =>
  performBulkAction({ action: "markAllAsRead" });
export const markMultipleAsRead = (notificationIds: string[]) =>
  performBulkAction({ action: "markAsRead", notificationIds });
export const markMultipleAsUnread = (notificationIds: string[]) =>
  performBulkAction({ action: "markAsUnread", notificationIds });
export const deleteMultiple = (notificationIds: string[]) =>
  performBulkAction({ action: "delete", notificationIds });
export const softDeleteMultiple = (notificationIds: string[]) =>
  performBulkAction({ action: "softDelete", notificationIds });
