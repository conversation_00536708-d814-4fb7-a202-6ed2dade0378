"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import type {
  CreateContract,
  UpdateContract,
  ContractResponse,
  ContractsListResponse,
  ContractStatisticsResponse,
} from "@/lib/api/validators/schemas/contract";

// Fetch all contracts
export const fetchContracts = createAsyncThunk(
  "contracts/fetchContracts",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<ContractsListResponse>("contract");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contracts",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch contracts";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single contract
export const fetchContract = createAsyncThunk(
  "contracts/fetchContract",
  async (contractId: string, { rejectWithValue }) => {
    try {
      const response = await api.get<ContractResponse>(
        `contract?id=${contractId}`
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contract",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch contract";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create contract
export const createContract = createAsyncThunk(
  "contracts/createContract",
  async (data: CreateContract | FormData, { rejectWithValue }) => {
    try {
      let response: any;
      if (data instanceof FormData) {
        response = await api.upload<ContractResponse>("contract", data);
      } else {
        response = await api.post<ContractResponse>("contract", data);
      }

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create contract",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create contract";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update contract
export const updateContract = createAsyncThunk(
  "contracts/updateContract",
  async (
    { id, data }: { id: string; data: Partial<UpdateContract> },
    { rejectWithValue }
  ) => {
    try {
      let response: any;
      if (data instanceof FormData) {
        response = await api.upPatch<ContractResponse>(`contract/${id}`, data);
      } else {
        response = await api.patch<ContractResponse>(`contract/${id}`, data);
      }

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update contract",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update contract";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete contract
export const deleteContract = createAsyncThunk(
  "contracts/deleteContract",
  async (contractId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<ContractResponse>(
        `contract/${contractId}`
      );

      if (response.success && !response.error) {
        return contractId;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete contract",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete contract";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Search contracts with single query parameter
export const searchContracts = createAsyncThunk(
  "contracts/searchContracts",
  async (
    searchParams: {
      query?: string;
      page?: number;
      limit?: number;
    },
    { rejectWithValue }
  ) => {
    try {
      // Build query string from search parameters
      const queryParams = new URLSearchParams();

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, String(value));
        }
      });

      const queryString = queryParams.toString();
      const endpoint = queryString
        ? `contract/search?${queryString}`
        : "contract/search";

      const response = await api.get<any>(endpoint);

      if (response.success && !response.error) {
        return {
          contracts: response.data?.contracts || response.data || [],
          pagination: response.data?.pagination || response.pagination,
          searchQuery: searchParams.query,
        };
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search contracts",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search contracts";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch contract statistics
export const fetchContractStatistics = createAsyncThunk(
  "contracts/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<ContractStatisticsResponse>(
        "contract/statistics"
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch contract statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch contract statistics";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
