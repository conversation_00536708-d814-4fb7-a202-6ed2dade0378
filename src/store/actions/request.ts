"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { api } from "@/lib/common/requests";
import type {
  CreateRequest,
  UpdateRequest,
  RequestQuery,
  BulkRequest,
} from "@/lib/api/validators/schemas/request";

// Base API URL
const API_BASE = "/api/request";

// Types for request actions
export interface RequestSearchParams {
  query?: string;
  page?: number;
  limit?: number;
  status?: string;
  userId?: string;
  tender_id?: string;
}

// Async thunk actions

/**
 * Search requests with query parameters
 */
export const searchRequests = createAsyncThunk(
  "requests/searchRequests",
  async (searchParams: RequestSearchParams, { rejectWithValue }) => {
    try {
      // Build query string from search parameters
      const queryParams = new URLSearchParams();

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, String(value));
        }
      });

      const queryString = queryParams.toString();
      const endpoint = queryString ? `request?${queryString}` : "request";

      const response = await api.get<any>(endpoint);

      if (response.success && !response.error) {
        return {
          requests: response.data || [],
          pagination: response.pagination,
          searchQuery: searchParams.query,
        };
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search requests",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search requests";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get single request by ID
 */
export const getRequest = createAsyncThunk(
  "requests/getRequest",
  async (requestId: string, { rejectWithValue }) => {
    try {
      const response = await api.get<any>(`request?id=${requestId}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch request",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch request";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Create a new request
 */
export const createRequest = createAsyncThunk(
  "requests/createRequest",
  async (requestData: CreateRequest, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("request", requestData);

      if (response.success && !response.error) {
        toast.success("Request created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create request");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create request",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create request";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Update an existing request
 */
export const updateRequest = createAsyncThunk(
  "requests/updateRequest",
  async (requestData: UpdateRequest, { rejectWithValue }) => {
    try {
      const response = await api.put<any>("request", requestData);

      if (response.success && !response.error) {
        toast.success("Request updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update request");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update request",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update request";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Delete a request
 */
export const deleteRequest = createAsyncThunk(
  "requests/deleteRequest",
  async (requestId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`request?id=${requestId}`);

      if (response.success && !response.error) {
        toast.success("Request deleted successfully");
        return { id: requestId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete request");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete request",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete request";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Perform bulk actions on requests
 */
export const performBulkAction = createAsyncThunk(
  "requests/performBulkAction",
  async (bulkData: BulkRequest, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("request/bulk", bulkData);

      if (response.success && !response.error) {
        // Show success toast based on action
        switch (bulkData.action) {
          case "delete":
            toast.success(`${response.data.deleted} requests deleted successfully`);
            break;
          case "updateStatus":
            toast.success(`${response.data.updated} requests updated successfully`);
            break;
          case "export":
            toast.success(`${response.data.count} requests exported successfully`);
            break;
        }
        return response.data;
      } else {
        toast.error(response.message || "Failed to perform bulk action");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to perform bulk action",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to perform bulk action";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get request statistics
 */
export const getRequestStatistics = createAsyncThunk(
  "requests/getRequestStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<any>("request/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch request statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch request statistics";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Export types for use in components
export type { CreateRequest, UpdateRequest, RequestQuery, BulkRequest };
