"use client";

import { api } from "@/lib/common/requests";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { signIn, signOut, getSession } from "next-auth/react";
import { setSession, setLoading, resetAuthState } from "../slices/auth";

// Async thunk for refreshing session
export const refreshSession = createAsyncThunk(
  "auth/refreshSession",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const session: any = await getSession();
      dispatch(setSession(session));
      return session;
    } catch (error) {
      console.error("Failed to refresh session:", error);
      dispatch(setSession(null));
      const errorMessage =
        error instanceof Error ? error.message : "Failed to refresh session";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const registerUserWithCredentials = createAsyncThunk(
  "auth/registerUser",
  async (
    credentials: {
      firstName: string;
      lastName: string;
      phone: string;
      email: string;
      password: string;
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));

      const result = await api.post("auth/register", {
        ...credentials,
      });

      if (result.success && !result.error) {
        const { email, password, ...restWithOutLoginCredentials } = credentials;
        let session = await dispatch(
          loginUserWithCredentials({ email, password })
        ).unwrap();

        return result;
      } else {
        return rejectWithValue(result);
      }
    } catch (error: any) {
      const errorMessage = error
        ? error?.response?.data?.message
        : error?.message;
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const updateUserAvatar = createAsyncThunk(
  "auth/updateUserAvatar",
  async (
    { id, avatar }: { id: string; avatar: FormData },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.upload("auth/avatar?id=" + id, avatar);
      if (result.success) {
        return result.data;
      }
      return rejectWithValue(result.message || "Failed to update avatar");
    } catch (error: any) {
      console.error("Failed to update avatar:", error);
      const errorMessage = error?.message || "Failed to update avatar";
      return rejectWithValue(errorMessage);
    }
  }
);

// Async thunk for login
export const loginUserWithCredentials = createAsyncThunk(
  "auth/loginUser",
  async (
    credentials: { email: string; password: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await signIn("credentials", {
        ...credentials,
        redirect: false,
      });

      if (!result) {
        return rejectWithValue({
          success: false,
          error: true,
          message: "Login failed",
        });
      }

      if (result.error) {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.error || "Login failed",
        });
      }

      // Refresh session after successful login
      const session = await getSession();
      dispatch(setSession(session));

      return session;
    } catch (error) {
      console.error("Login failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Async thunk for OAuth login
export const loginUserWithOAuth = createAsyncThunk(
  "auth/loginUserOAuth",
  async (provider: string = "google", { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await signIn(provider, { redirect: false });

      if (!result) {
        return rejectWithValue({
          success: false,
          error: true,
          message: "OAuth login failed",
        });
      }

      if (result.error) {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.error,
        });
      }

      // Refresh session after successful login
      const session = await getSession();
      dispatch(setSession(session));

      return session;
    } catch (error) {
      console.error("OAuth login failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "OAuth login failed";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Async thunk for logout
export const logoutUser = createAsyncThunk(
  "auth/logoutUser",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      await signOut({ redirect: false });
      // Complete reset of all auth state
      dispatch(resetAuthState());
      return {
        success: true,
        error: false,
        message: "Successfully signed out!",
      };
    } catch (error) {
      console.error("Logout failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Logout failed";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Update user profile
export const updateUserAction = createAsyncThunk(
  "auth/updateUser",
  async (
    userData: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await api.put("auth/user", userData);

      if (result.success && !result.error) {
        return result.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to update user profile",
        });
      }
    } catch (error: any) {
      const errorMessage = error
        ? error?.response?.data?.message
        : error?.message;
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Update password
export const updatePassword = createAsyncThunk(
  "auth/updatePassword",
  async (
    passwordData: { currentPassword: string; newPassword: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await api.post("auth/password", passwordData);

      if (result.success && !result.error) {
        return result.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: result.message || "Failed to update password",
        });
      }
    } catch (error) {
      console.error("Failed to update password:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update password";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);
