"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";

// Types for dashboard data
export interface DashboardSummary {
  contractsCount: number;
  proposalsCount: number;
  recentContracts: any[];
  recentProposals: any[];
  contractStatistics: any;
  proposalStatistics: any;
}

// Fetch dashboard summary data
export const fetchDashboardSummary = createAsyncThunk(
  "dashboard/fetchSummary",
  async (_, { rejectWithValue }) => {
    try {
      // Fetch all required data in parallel
      const [
        contractsResponse,
        proposalsResponse,
        contractStatsResponse,
        proposalStatsResponse,
      ] = await Promise.all([
        api.get("contract"),
        api.get("proposal"),
        api.get("contract/statistics"),
        api.get("proposal/statistics"),
      ]);

      // Check if all requests were successful
      if (
        !contractsResponse.success ||
        !proposalsResponse.success ||
        !contractStatsResponse.success ||
        !proposalStatsResponse.success
      ) {
        const errorMessage = "Failed to fetch dashboard data";
        toast.error(errorMessage);
        return rejectWithValue({
          success: false,
          error: true,
          message: errorMessage,
        });
      }

      // Extract data and prepare summary
      const contracts = contractsResponse.data || [];
      const proposals = proposalsResponse.data?.proposals || [];

      // Get recent items (last 5)
      const recentContracts = contracts
        .sort(
          (a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, 5);

      const recentProposals = proposals
        .sort(
          (a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, 5);

      const summary: DashboardSummary = {
        contractsCount: contracts.length,
        proposalsCount: proposals.length,
        recentContracts,
        recentProposals,
        contractStatistics: contractStatsResponse.data,
        proposalStatistics: proposalStatsResponse.data,
      };

      return summary;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch dashboard summary";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch recent contracts
export const fetchRecentContracts = createAsyncThunk(
  "dashboard/fetchRecentContracts",
  async (limit: number = 5, { rejectWithValue }) => {
    try {
      const response = await api.get("contract");

      if (response.success && !response.error) {
        const contracts = response.data || [];
        const recentContracts = contracts
          .sort(
            (a: any, b: any) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          )
          .slice(0, limit);

        return recentContracts;
      } else {
        toast.error(response.message || "Failed to fetch recent contracts");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch recent contracts",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch recent contracts";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch recent proposals
export const fetchRecentProposals = createAsyncThunk(
  "dashboard/fetchRecentProposals",
  async (limit: number = 5, { rejectWithValue }) => {
    try {
      const response = await api.get("proposal");

      if (response.success && !response.error) {
        const proposals = response.data?.proposals || [];
        const recentProposals = proposals
          .sort(
            (a: any, b: any) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          )
          .slice(0, limit);

        return recentProposals;
      } else {
        toast.error(response.message || "Failed to fetch recent proposals");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch recent proposals",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch recent proposals";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch dashboard statistics
export const fetchDashboardStatistics = createAsyncThunk(
  "dashboard/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const [contractStatsResponse, proposalStatsResponse] = await Promise.all([
        api.get("contract/statistics"),
        api.get("proposal/statistics"),
      ]);

      if (!contractStatsResponse.success || !proposalStatsResponse.success) {
        const errorMessage = "Failed to fetch dashboard statistics";
        toast.error(errorMessage);
        return rejectWithValue({
          success: false,
          error: true,
          message: errorMessage,
        });
      }

      return {
        contractStatistics: contractStatsResponse.data,
        proposalStatistics: proposalStatsResponse.data,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch dashboard statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
