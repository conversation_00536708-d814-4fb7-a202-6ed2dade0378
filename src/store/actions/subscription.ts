"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { api } from "@/lib/common/requests";
import type {
  CreateSubscription,
  UpdateSubscription,
  SubscriptionQuery,
  BulkSubscription,
} from "@/lib/api/validators/schemas/subscription";

// Base API URL
const API_BASE = "/api/subscription";

// Types for subscription actions
export interface SubscriptionSearchParams {
  query?: string;
  page?: number;
  limit?: number;
  status?: string;
  minPrice?: number;
  maxPrice?: number;
  minSubscribers?: number;
  maxSubscribers?: number;
  sortBy?: "name" | "price" | "subscribers" | "status" | "createdAt" | "updatedAt";
  sortOrder?: "asc" | "desc";
  includeFeatures?: boolean;
}

// Async thunk actions

/**
 * Search subscriptions with filtering and pagination
 */
export const searchSubscriptions = createAsyncThunk(
  "subscriptions/searchSubscriptions",
  async (params: SubscriptionSearchParams = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value.toString());
        }
      });

      const response = await api.get<any>(`subscription?${queryParams.toString()}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search subscriptions",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search subscriptions";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get subscription by ID
 */
export const getSubscription = createAsyncThunk(
  "subscriptions/getSubscription",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.get<any>(`subscription/${id}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to get subscription",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get subscription";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Create a new subscription
 */
export const createSubscription = createAsyncThunk(
  "subscriptions/createSubscription",
  async (subscriptionData: CreateSubscription, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("subscription", subscriptionData);

      if (response.success && !response.error) {
        toast.success("Subscription created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create subscription");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create subscription",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create subscription";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Update an existing subscription
 */
export const updateSubscription = createAsyncThunk(
  "subscriptions/updateSubscription",
  async (
    { id, data }: { id: string; data: Partial<UpdateSubscription> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put<any>(`subscription/${id}`, data);

      if (response.success && !response.error) {
        toast.success("Subscription updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update subscription");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update subscription",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update subscription";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Delete a subscription
 */
export const deleteSubscription = createAsyncThunk(
  "subscriptions/deleteSubscription",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`subscription/${id}`);

      if (response.success && !response.error) {
        toast.success("Subscription deleted successfully");
        return { id, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete subscription");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete subscription",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete subscription";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Perform bulk action on subscriptions
 */
export const performBulkAction = createAsyncThunk(
  "subscriptions/performBulkAction",
  async (bulkData: BulkSubscription, { rejectWithValue }) => {
    try {
      const response = await api.post<any>("subscription/bulk", bulkData);

      if (response.success && !response.error) {
        toast.success(`Bulk ${bulkData.action} completed successfully`);
        return response.data;
      } else {
        toast.error(response.message || `Failed to perform bulk ${bulkData.action}`);
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || `Failed to perform bulk ${bulkData.action}`,
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : `Failed to perform bulk ${bulkData.action}`;
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

/**
 * Get subscription statistics
 */
export const getSubscriptionStatistics = createAsyncThunk(
  "subscriptions/getSubscriptionStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<any>("subscription/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch subscription statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch subscription statistics";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Export types for use in components
export type { CreateSubscription, UpdateSubscription, SubscriptionQuery, BulkSubscription };
