"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";
import type {
  NestTenderData,
  UpdateNestTenderData,
  NestTenderQuery,
} from "@/lib/api/services/tenders/nest";

// Types for tender actions
export interface TenderCreateData extends NestTenderData {}

export interface TenderUpdateData extends UpdateNestTenderData {}

export interface TenderSearchQuery extends NestTenderQuery {}

export interface TenderResponse {
  id: string;
  ocid: string;
  language?: string;
  initiation_type?: string;
  description?: string;
  address?: any;
  timeline?: any;
  procuring_entity?: string;
  procuring_method?: string;
  items?: any[];
  parties?: any[];
  category?: string;
  status?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TendersListResponse {
  data: TenderResponse[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Create tender
export const createTender = createAsyncThunk(
  "tenders/createTender",
  async (tenderData: TenderCreateData, { rejectWithValue }) => {
    try {
      const response = await api.post<{
        success: boolean;
        error: boolean;
        message: string;
        data: TenderResponse;
      }>("tender", tenderData);

      if (response.success && !response.error) {
        toast.success(response.message || "Tender created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create tender");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create tender",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create tender";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update tender
export const updateTender = createAsyncThunk(
  "tenders/updateTender",
  async ({ id, ...tenderData }: TenderUpdateData, { rejectWithValue }) => {
    try {
      const response = await api.put<{
        success: boolean;
        error: boolean;
        message: string;
        data: TenderResponse;
      }>(`tender/${id}`, tenderData);

      if (response.success && !response.error) {
        toast.success(response.message || "Tender updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update tender");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update tender",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update tender";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete tender
export const deleteTender = createAsyncThunk(
  "tenders/deleteTender",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<{
        success: boolean;
        error: boolean;
        message: string;
      }>(`tender/${id}`);

      if (response.success && !response.error) {
        toast.success(response.message || "Tender deleted successfully");
        return { id };
      } else {
        toast.error(response.message || "Failed to delete tender");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete tender",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete tender";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Upsert tender (create or update based on OCID)
export const upsertTender = createAsyncThunk(
  "tenders/upsertTender",
  async (tenderData: TenderCreateData, { rejectWithValue }) => {
    try {
      const response = await api.put<{
        success: boolean;
        error: boolean;
        message: string;
        data: TenderResponse;
      }>("tender", tenderData);

      if (response.success && !response.error) {
        toast.success(response.message || "Tender saved successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to save tender");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to save tender",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save tender";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Search tenders
export const searchTenders = createAsyncThunk(
  "tenders/searchTenders",
  async (query: TenderSearchQuery, { rejectWithValue }) => {
    try {
      const response = await api.get<{
        success: boolean;
        error: boolean;
        message: string;
        data: TenderResponse[];
      }>("tender/search", query);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search tenders",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search tenders";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Seed tenders from external API
export const seedTenders = createAsyncThunk(
  "tenders/seedTenders",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post<{
        success: boolean;
        error: boolean;
        message: string;
        data: {
          processed: number;
          created: number;
          updated: number;
          errors: number;
        };
      }>("tender/seed");

      if (response.success && !response.error) {
        toast.success(response.message || "Tenders seeded successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to seed tenders");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to seed tenders",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to seed tenders";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Bulk upsert tenders
export const bulkUpsertTenders = createAsyncThunk(
  "tenders/bulkUpsertTenders",
  async (tenders: TenderCreateData[], { rejectWithValue }) => {
    try {
      const response = await api.post<{
        success: boolean;
        error: boolean;
        message: string;
        data: {
          processed: number;
          created: number;
          updated: number;
          errors: number;
          results: TenderResponse[];
        };
      }>("tender/bulk", { tenders });

      if (response.success && !response.error) {
        toast.success(
          response.message || "Bulk operation completed successfully"
        );
        return response.data;
      } else {
        toast.error(response.message || "Failed to perform bulk operation");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to perform bulk operation",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to perform bulk operation";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
