"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";
import type {
  CreateDocument,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";

// Fetch all documents
export const fetchDocuments = createAsyncThunk(
  "documents/fetchDocuments",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get("document");

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch documents");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch documents",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch documents";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single document
export const fetchSignedURL = createAsyncThunk(
  "documents/fetchDocument",
  async (path: string, { rejectWithValue }) => {
    try {
      const response = await api.post("document/signedURL", { path });

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch document",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single document
export const fetchDocument = createAsyncThunk(
  "documents/fetchDocument",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`document/${id}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch document",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create document
export const createDocument = createAsyncThunk(
  "documents/createDocument",
  async (data: CreateDocument, { rejectWithValue }) => {
    try {
      // Check if data is FormData for file uploads
      let response;
      if (data instanceof FormData) {
        response = await api.upload("document", data);
      } else {
        response = await api.post("document", data);
      }

      if (response.success && !response.error) {
        toast.success(response.message || "Document created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create document",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

export const bulkCreateDocuments = createAsyncThunk(
  "documents/bulkCreateDocuments",
  async (data: CreateDocument[], { rejectWithValue }) => {
    try {
      const response = await api.post("document/bulk", data);

      if (response.success && !response.error) {
        toast.success(response.message || "Documents created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create documents");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create documents",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create documents";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update document
export const updateDocument = createAsyncThunk(
  "documents/updateDocument",
  async (
    { id, data }: { id: string; data: Partial<UpdateDocument> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put(`document/${id}`, data);

      if (response.success && !response.error) {
        toast.success(response.message || "Document updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update document",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete document
export const deleteDocument = createAsyncThunk(
  "documents/deleteDocument",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.delete(`document/${id}`);

      if (response.success && !response.error) {
        toast.success(response.message || "Document deleted successfully");
        return id;
      } else {
        toast.error(response.message || "Failed to delete document");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete document",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete document";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Search documents with single query parameter
export const searchDocuments = createAsyncThunk(
  "documents/searchDocuments",
  async (
    searchParams: {
      query?: string;
      page?: number;
      limit?: number;
    },
    { rejectWithValue }
  ) => {
    try {
      // Build query string from search parameters
      const queryParams = new URLSearchParams();

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, String(value));
        }
      });

      const queryString = queryParams.toString();
      const endpoint = queryString
        ? `document/search?${queryString}`
        : "document/search";

      const response = await api.get(endpoint);

      if (response.success && !response.error) {
        return {
          documents: response.data?.documents || response.data || [],
          pagination: response.pagination,
          searchQuery: searchParams.query,
        };
      } else {
        toast.error(response.message || "Failed to search documents");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search documents",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to search documents";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch document statistics
export const fetchDocumentStatistics = createAsyncThunk(
  "documents/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get("document/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch document statistics");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch document statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch document statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
