import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  initializeBraintree,
  createTransaction,
  createCustomer,
  createPaymentMethod,
  voidTransaction,
  refundTransaction,
  testConnection,
  searchTransactions,
  type BraintreeTransaction,
  type BraintreeCustomer,
  type BraintreePaymentMethod,
} from "../actions/braintree";

// Braintree state interface
export interface BraintreeState {
  // Connection state
  isReady: boolean;
  clientToken: string | null;
  isConnected: boolean;

  // Customer data
  customer: BraintreeCustomer | null;

  // Payment methods
  paymentMethods: BraintreePaymentMethod[];

  // Transactions
  transactions: BraintreeTransaction[];

  // Loading states
  isLoading: boolean;
  isInitializing: boolean;
  isProcessingTransaction: boolean;
  isCreatingCustomer: boolean;
  isCreatingPaymentMethod: boolean;
  isVoidingTransaction: boolean;
  isRefundingTransaction: boolean;
  isTesting: boolean;

  // Error state
  error: string | null;
}

// Initial state
const initialState: BraintreeState = {
  // Connection state
  isReady: false,
  clientToken: null,
  isConnected: false,

  // Customer data
  customer: null,

  // Payment methods
  paymentMethods: [],

  // Transactions
  transactions: [],

  // Loading states
  isLoading: false,
  isInitializing: false,
  isProcessingTransaction: false,
  isCreatingCustomer: false,
  isCreatingPaymentMethod: false,
  isVoidingTransaction: false,
  isRefundingTransaction: false,
  isTesting: false,

  // Error state
  error: null,
};

// Create the Braintree slice
const braintreeSlice = createSlice({
  name: "braintree",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Reset state
    resetState: (state) => {
      return { ...initialState };
    },

    // Set client token manually
    setClientToken: (state, action: PayloadAction<string>) => {
      state.clientToken = action.payload;
      state.isReady = true;
    },

    // Add transaction manually
    addTransaction: (state, action: PayloadAction<BraintreeTransaction>) => {
      state.transactions.unshift(action.payload);
    },

    // Update transaction status
    updateTransactionStatus: (
      state,
      action: PayloadAction<{
        id: string;
        status: BraintreeTransaction["status"];
      }>
    ) => {
      const transaction = state.transactions.find(
        (t) => t.id === action.payload.id
      );
      if (transaction) {
        transaction.status = action.payload.status;
      }
    },
  },
  extraReducers: (builder) => {
    // Initialize Braintree
    builder
      .addCase(initializeBraintree.pending, (state) => {
        state.isInitializing = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeBraintree.fulfilled, (state, action) => {
        state.isInitializing = false;
        state.isLoading = false;
        state.isReady = action.payload.isReady;
        state.clientToken = action.payload.clientToken;
        state.isConnected = true;
        state.error = null;
      })
      .addCase(initializeBraintree.rejected, (state, action) => {
        state.isInitializing = false;
        state.isLoading = false;
        state.isReady = false;
        state.clientToken = null;
        state.isConnected = false;
        state.error =
          action.payload?.message || "Failed to initialize Braintree";
      });

    // Create transaction
    builder
      .addCase(createTransaction.pending, (state) => {
        state.isProcessingTransaction = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.isProcessingTransaction = false;
        state.isLoading = false;
        state.transactions.unshift(action.payload);
        state.error = null;
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.isProcessingTransaction = false;
        state.isLoading = false;
        state.error = action.payload?.message || "Transaction failed";
      });

    // Create customer
    builder
      .addCase(createCustomer.pending, (state) => {
        state.isCreatingCustomer = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCustomer.fulfilled, (state, action) => {
        state.isCreatingCustomer = false;
        state.isLoading = false;
        state.customer = action.payload;
        state.error = null;
      })
      .addCase(createCustomer.rejected, (state, action) => {
        state.isCreatingCustomer = false;
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to create customer";
      });

    // Create payment method
    builder
      .addCase(createPaymentMethod.pending, (state) => {
        state.isCreatingPaymentMethod = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPaymentMethod.fulfilled, (state, action) => {
        state.isCreatingPaymentMethod = false;
        state.isLoading = false;
        state.paymentMethods.push(action.payload);
        state.error = null;
      })
      .addCase(createPaymentMethod.rejected, (state, action) => {
        state.isCreatingPaymentMethod = false;
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to create payment method";
      });

    // Void transaction
    builder
      .addCase(voidTransaction.pending, (state) => {
        state.isVoidingTransaction = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(voidTransaction.fulfilled, (state, action) => {
        state.isVoidingTransaction = false;
        state.isLoading = false;
        // Update transaction status in the list
        const transaction = state.transactions.find(
          (t) => t.id === action.payload.id
        );
        if (transaction) {
          transaction.status = "voided";
        }
        state.error = null;
      })
      .addCase(voidTransaction.rejected, (state, action) => {
        state.isVoidingTransaction = false;
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to void transaction";
      });

    // Refund transaction
    builder
      .addCase(refundTransaction.pending, (state) => {
        state.isRefundingTransaction = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refundTransaction.fulfilled, (state, action) => {
        state.isRefundingTransaction = false;
        state.isLoading = false;
        // Add refund transaction to the list
        state.transactions.unshift(action.payload);
        state.error = null;
      })
      .addCase(refundTransaction.rejected, (state, action) => {
        state.isRefundingTransaction = false;
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to refund transaction";
      });

    // Test connection
    builder
      .addCase(testConnection.pending, (state) => {
        state.isTesting = true;
        state.isLoading = true;
        state.error = null;
      })
      .addCase(testConnection.fulfilled, (state, action) => {
        state.isTesting = false;
        state.isLoading = false;
        state.isConnected = action.payload.connected;
        state.error = null;
      })
      .addCase(testConnection.rejected, (state, action) => {
        state.isTesting = false;
        state.isLoading = false;
        state.isConnected = false;
        state.error = action.payload?.message || "Connection test failed";
      })
      // Search transactions cases
      .addCase(searchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update transactions with search results (scoped to company merchant account)
        state.transactions = action.payload;
        state.error = null;
      })
      .addCase(searchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Transaction search failed";
      });
  },
});

// Export actions
export const {
  clearError,
  resetState,
  setClientToken,
  addTransaction,
  updateTransactionStatus,
} = braintreeSlice.actions;

// Export reducer
export default braintreeSlice;
