import { Session } from "next-auth";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  updateUserAvatar,
  updateUserAction,
  updatePassword,
  registerUserWithCredentials,
} from "../actions/auth";
import {
  createCompany,
  updateCompany,
  deleteCompany,
  getCompany,
} from "../actions/company";

export interface User extends Session {
  user: Session["user"] & {
    customerId: string;
    role: Role;
    company?: Company;
    account: { id: string };
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface Company {
  id: string;
  name: string;
  tin: string;
  category?: string;
  email?: string;
  address?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  status: string;
  permissions: string[];
}

// Define the auth state interface
export interface AuthState {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: any;
  isDashboardView: boolean;
  isUserRegistered: boolean;
  user: {
    id?: string;
    firstName?: string | null;
    lastName?: string | null;
    phone?: string | null;
    customerId: string;
    email?: string | null;
    image?: string | null;
    role: Role;
    company?: Company;
    account: { id: string };
    createdAt: Date;
    updatedAt: Date;
  } | null;
}

// Initial state
const initialState: AuthState = {
  session: null,
  isAuthenticated: false,
  isLoading: true,
  isUserRegistered: false,
  error: null,
  isDashboardView: false,
  user: null,
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Set session data from next-auth
    setSession: (state, action: PayloadAction<User | any>) => {
      state.session = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;

      if (action?.payload?.user) {
        state.user = action.payload.user;
      }
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear session (logout)
    clearSession: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.user = null;
    },

    // Reset all auth state to initial state (complete reset)
    resetAuthState: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.error = null;
      state.isDashboardView = false;
      state.isUserRegistered = false;
      state.user = null;
    },

    // Update user profile data with recursive safe assignment
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      // Use safe user update utility for comprehensive validation
      state.user = action.payload;
    },

    // Set dashboard view state
    setDashboardView: (state, action: PayloadAction<boolean>) => {
      state.isDashboardView = action.payload;
    },

    // Toggle dashboard view state
    toggleDashboardView: (state) => {
      state.isDashboardView = !state.isDashboardView;
    },
    // Set user registration state
    setUserRegistered: (state, action) => {
      state.isUserRegistered = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Registration
    builder
      .addCase(registerUserWithCredentials.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(registerUserWithCredentials.fulfilled, (state) => {
        state.isLoading = false;
        state.isUserRegistered = true;
      })
      .addCase(registerUserWithCredentials.rejected, (state) => {
        state.isLoading = false;
        state.isUserRegistered = false;
      });

    // Avatar update
    builder
      .addCase(updateUserAvatar.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserAvatar.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload?.image !== undefined) {
          // Use safe assignment for image update
          state.user.image = action.payload.image;
        }
      })
      .addCase(updateUserAvatar.rejected, (state) => {
        state.isLoading = false;
      });

    // Update user profile
    builder
      .addCase(updateUserAction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserAction.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload) {
          // Update user fields with new data
          if (action.payload.firstName !== undefined) {
            state.user.firstName = action.payload.firstName;
          }
          if (action.payload.lastName !== undefined) {
            state.user.lastName = action.payload.lastName;
          }
          if (action.payload.email !== undefined) {
            state.user.email = action.payload.email;
          }
          if (action.payload.phone !== undefined) {
            state.user.phone = action.payload.phone;
          }
        }
      })
      .addCase(updateUserAction.rejected, (state) => {
        state.isLoading = false;
      });

    // Update password
    builder
      .addCase(updatePassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePassword.fulfilled, (state) => {
        state.isLoading = false;
        // Password update doesn't change user state, just success
      })
      .addCase(updatePassword.rejected, (state) => {
        state.isLoading = false;
      });

    // Company actions
    builder
      .addCase(createCompany.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createCompany.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload) {
          state.user.company = action.payload;
        }
      })
      .addCase(createCompany.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(updateCompany.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCompany.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload) {
          state.user.company = action.payload;
        }
      })
      .addCase(updateCompany.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(getCompany.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCompany.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user && action.payload) {
          state.user.company = action.payload;
        }
      })
      .addCase(getCompany.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(deleteCompany.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteCompany.fulfilled, (state) => {
        state.isLoading = false;
        if (state.user) {
          state.user.company = undefined;
        }
      })
      .addCase(deleteCompany.rejected, (state) => {
        state.isLoading = false;
      });
  },
});

// Export actions
export const {
  setSession,
  setLoading,
  clearSession,
  resetAuthState,
  updateUser,
  setDashboardView,
  toggleDashboardView,
  setUserRegistered,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectSessionAccount = (state: { auth: AuthState }) =>
  (state.auth?.session?.user as any)?.account;
export const selectPermissions = (state: { auth: AuthState }) =>
  state.auth.user?.role?.permissions;
export const selectUserAccount = (state: { auth: AuthState }) =>
  state.auth?.user?.account;

export const selectCompany = (state: { auth: AuthState }) =>
  state.auth.user?.company;
export const selectIsUserRegistered = (state: { auth: AuthState }) =>
  state.auth.isUserRegistered;
export const selectPersonlizedRoute = (state: { auth: AuthState }) => {
  const user = state.auth.user;
  if (!user?.firstName) return null;
  const fullName = `${user.firstName} ${user.lastName || ""}`.trim();
  return fullName.replace(/\s+/g, "-").toLowerCase();
};
export const selectSession = (state: { auth: AuthState }) => state.auth.session;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;
export const selectIsDashboardView = (state: { auth: AuthState }) =>
  state.auth.isDashboardView;

// Export reducer
export default authSlice;
