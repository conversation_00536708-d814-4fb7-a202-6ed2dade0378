import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Pagination state for a single table context
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  cursor?: string | null;
  orderBy?: Record<string, "asc" | "desc">;
  loading: boolean;
}

// State for all pagination contexts (keyed by table ID)
export interface PaginationSliceState {
  contexts: Record<string, PaginationState>;
}

// Initial state for a single pagination context
const initialPaginationState: PaginationState = {
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPreviousPage: false,
  cursor: null,
  orderBy: { createdAt: "desc" },
  loading: false,
};

// Initial state for the entire slice
const initialState: PaginationSliceState = {
  contexts: {},
};

// Action payload types
interface SetPaginationPayload {
  tableId: string;
  pagination: Partial<PaginationState>;
}

interface SetPagePayload {
  tableId: string;
  page: number;
}

interface SetLimitPayload {
  tableId: string;
  limit: number;
}

interface SetOrderByPayload {
  tableId: string;
  orderBy: Record<string, "asc" | "desc">;
}

interface SetCursorPayload {
  tableId: string;
  cursor: string | null;
}

interface SetLoadingPayload {
  tableId: string;
  loading: boolean;
}

interface UpdateTotalPayload {
  tableId: string;
  total: number;
}

interface ResetPaginationPayload {
  tableId: string;
}

// Helper function to get or create pagination context
const getOrCreateContext = (
  state: PaginationSliceState,
  tableId: string
): PaginationState => {
  if (!state.contexts[tableId]) {
    state.contexts[tableId] = { ...initialPaginationState };
  }
  return state.contexts[tableId];
};

// Helper function to calculate pagination metadata
const calculatePaginationMeta = (context: PaginationState) => {
  context.totalPages = Math.ceil(context.total / context.limit);
  context.hasNextPage = context.page < context.totalPages;
  context.hasPreviousPage = context.page > 1;
};

export const paginationSlice = createSlice({
  name: "pagination",
  initialState,
  reducers: {
    // Initialize or update pagination context
    setPagination: (state, action: PayloadAction<SetPaginationPayload>) => {
      const { tableId, pagination } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      Object.assign(context, pagination);
      calculatePaginationMeta(context);
    },

    // Set current page
    setPage: (state, action: PayloadAction<SetPagePayload>) => {
      const { tableId, page } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.page = Math.max(1, page);
      calculatePaginationMeta(context);
    },

    // Set items per page limit
    setLimit: (state, action: PayloadAction<SetLimitPayload>) => {
      const { tableId, limit } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.limit = Math.max(1, limit);
      context.page = 1; // Reset to first page when changing limit
      calculatePaginationMeta(context);
    },

    // Set sort order
    setOrderBy: (state, action: PayloadAction<SetOrderByPayload>) => {
      const { tableId, orderBy } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.orderBy = orderBy;
      context.page = 1; // Reset to first page when changing sort
    },

    // Set cursor for cursor-based pagination
    setCursor: (state, action: PayloadAction<SetCursorPayload>) => {
      const { tableId, cursor } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.cursor = cursor;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<SetLoadingPayload>) => {
      const { tableId, loading } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.loading = loading;
    },

    // Update total count and recalculate pagination metadata
    updateTotal: (state, action: PayloadAction<UpdateTotalPayload>) => {
      const { tableId, total } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.total = Math.max(0, total);
      calculatePaginationMeta(context);
    },

    // Go to next page
    nextPage: (state, action: PayloadAction<{ tableId: string }>) => {
      const { tableId } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      if (context.hasNextPage) {
        context.page += 1;
        calculatePaginationMeta(context);
      }
    },

    // Go to previous page
    previousPage: (state, action: PayloadAction<{ tableId: string }>) => {
      const { tableId } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      if (context.hasPreviousPage) {
        context.page -= 1;
        calculatePaginationMeta(context);
      }
    },

    // Go to first page
    firstPage: (state, action: PayloadAction<{ tableId: string }>) => {
      const { tableId } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.page = 1;
      calculatePaginationMeta(context);
    },

    // Go to last page
    lastPage: (state, action: PayloadAction<{ tableId: string }>) => {
      const { tableId } = action.payload;
      const context = getOrCreateContext(state, tableId);
      
      context.page = context.totalPages || 1;
      calculatePaginationMeta(context);
    },

    // Reset pagination context to initial state
    resetPagination: (state, action: PayloadAction<ResetPaginationPayload>) => {
      const { tableId } = action.payload;
      state.contexts[tableId] = { ...initialPaginationState };
    },

    // Remove pagination context
    removePaginationContext: (state, action: PayloadAction<{ tableId: string }>) => {
      const { tableId } = action.payload;
      delete state.contexts[tableId];
    },
  },
});

// Export actions
export const {
  setPagination,
  setPage,
  setLimit,
  setOrderBy,
  setCursor,
  setLoading,
  updateTotal,
  nextPage,
  previousPage,
  firstPage,
  lastPage,
  resetPagination,
  removePaginationContext,
} = paginationSlice.actions;

// Selectors
export const selectPaginationContext = (state: { pagination: PaginationSliceState }, tableId: string): PaginationState => {
  return state.pagination.contexts[tableId] || initialPaginationState;
};

export const selectAllPaginationContexts = (state: { pagination: PaginationSliceState }) => {
  return state.pagination.contexts;
};

// Export reducer
export default paginationSlice.reducer;
