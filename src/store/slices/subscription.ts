import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  searchSubscriptions,
  getSubscription,
  createSubscription,
  updateSubscription,
  deleteSubscription,
  performBulkAction,
  getSubscriptionStatistics,
} from "@/store/actions/subscription";
import type { 
  Subscription, 
  SubscriptionStatistics 
} from "@/lib/api/validators/schemas/subscription";

// Subscription state interface
export interface SubscriptionState {
  // Data
  subscriptions: Subscription[];
  currentSubscription: Subscription | null;
  statistics: SubscriptionStatistics | null;

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkActionLoading: boolean;
  isStatisticsLoading: boolean;

  // Error states
  error: string | null;

  // Search and filtering
  searchQuery: string;
  filters: {
    status?: string;
    minPrice?: number;
    maxPrice?: number;
    minSubscribers?: number;
    maxSubscribers?: number;
  };

  // Pagination
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  // UI state
  selectedSubscriptionIds: string[];
}

// Initial state
const initialState: SubscriptionState = {
  // Data
  subscriptions: [],
  currentSubscription: null,
  statistics: null,

  // Loading states
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isBulkActionLoading: false,
  isStatisticsLoading: false,

  // Error states
  error: null,

  // Search and filtering
  searchQuery: "",
  filters: {},

  // Pagination
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  },

  // UI state
  selectedSubscriptionIds: [],
};

// Subscription slice
const subscriptionSlice = createSlice({
  name: "subscription",
  initialState,
  reducers: {
    // Loading states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCreating: (state, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
    },
    setDeleting: (state, action: PayloadAction<boolean>) => {
      state.isDeleting = action.payload;
    },
    setBulkActionLoading: (state, action: PayloadAction<boolean>) => {
      state.isBulkActionLoading = action.payload;
    },
    setStatisticsLoading: (state, action: PayloadAction<boolean>) => {
      state.isStatisticsLoading = action.payload;
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },

    // Data management
    setCurrentSubscription: (state, action: PayloadAction<Subscription | null>) => {
      state.currentSubscription = action.payload;
    },
    clearCurrentSubscription: (state) => {
      state.currentSubscription = null;
    },

    // Search and filtering
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<SubscriptionState["filters"]>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
      state.searchQuery = "";
    },

    // Pagination
    setPagination: (state, action: PayloadAction<Partial<SubscriptionState["pagination"]>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },

    // UI state
    setSelectedSubscriptionIds: (state, action: PayloadAction<string[]>) => {
      state.selectedSubscriptionIds = action.payload;
    },
    toggleSubscriptionSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.selectedSubscriptionIds.indexOf(id);
      if (index > -1) {
        state.selectedSubscriptionIds.splice(index, 1);
      } else {
        state.selectedSubscriptionIds.push(id);
      }
    },
    clearSelection: (state) => {
      state.selectedSubscriptionIds = [];
    },

    // Reset state
    resetState: (state) => {
      return { ...initialState };
    },
  },
  extraReducers: (builder) => {
    // Search subscriptions
    builder
      .addCase(searchSubscriptions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchSubscriptions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subscriptions = action.payload.data || [];
        if (action.payload.pagination) {
          state.pagination = action.payload.pagination;
        }
      })
      .addCase(searchSubscriptions.rejected, (state, action: any) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to search subscriptions";
      });

    // Get subscription
    builder
      .addCase(getSubscription.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubscription.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(getSubscription.rejected, (state, action: any) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to get subscription";
      });

    // Create subscription
    builder
      .addCase(createSubscription.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.isCreating = false;
        state.subscriptions.unshift(action.payload);
      })
      .addCase(createSubscription.rejected, (state, action: any) => {
        state.isCreating = false;
        state.error = action.payload?.message || "Failed to create subscription";
      });

    // Update subscription
    builder
      .addCase(updateSubscription.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateSubscription.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.subscriptions.findIndex(sub => sub.id === action.payload.id);
        if (index !== -1) {
          state.subscriptions[index] = action.payload;
        }
        if (state.currentSubscription?.id === action.payload.id) {
          state.currentSubscription = action.payload;
        }
      })
      .addCase(updateSubscription.rejected, (state, action: any) => {
        state.isUpdating = false;
        state.error = action.payload?.message || "Failed to update subscription";
      });

    // Delete subscription
    builder
      .addCase(deleteSubscription.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteSubscription.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.subscriptions = state.subscriptions.filter(sub => sub.id !== action.payload.id);
        if (state.currentSubscription?.id === action.payload.id) {
          state.currentSubscription = null;
        }
        // Remove from selection if selected
        state.selectedSubscriptionIds = state.selectedSubscriptionIds.filter(id => id !== action.payload.id);
      })
      .addCase(deleteSubscription.rejected, (state, action: any) => {
        state.isDeleting = false;
        state.error = action.payload?.message || "Failed to delete subscription";
      });

    // Bulk action
    builder
      .addCase(performBulkAction.pending, (state) => {
        state.isBulkActionLoading = true;
        state.error = null;
      })
      .addCase(performBulkAction.fulfilled, (state, action) => {
        state.isBulkActionLoading = false;
        // Clear selection after successful bulk action
        state.selectedSubscriptionIds = [];
        // Note: The actual data updates would depend on the specific bulk action
        // For now, we'll let the UI refresh the data
      })
      .addCase(performBulkAction.rejected, (state, action: any) => {
        state.isBulkActionLoading = false;
        state.error = action.payload?.message || "Failed to perform bulk action";
      });

    // Get statistics
    builder
      .addCase(getSubscriptionStatistics.pending, (state) => {
        state.isStatisticsLoading = true;
        state.error = null;
      })
      .addCase(getSubscriptionStatistics.fulfilled, (state, action) => {
        state.isStatisticsLoading = false;
        state.statistics = action.payload;
      })
      .addCase(getSubscriptionStatistics.rejected, (state, action: any) => {
        state.isStatisticsLoading = false;
        state.error = action.payload?.message || "Failed to get subscription statistics";
      });
  },
});

// Export actions
export const {
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setStatisticsLoading,
  setError,
  clearError,
  setCurrentSubscription,
  clearCurrentSubscription,
  setSearchQuery,
  setFilters,
  clearFilters,
  setPagination,
  setSelectedSubscriptionIds,
  toggleSubscriptionSelection,
  clearSelection,
  resetState,
} = subscriptionSlice.actions;

// Selectors
export const selectSubscriptions = (state: { subscription: SubscriptionState }) => state.subscription.subscriptions;
export const selectCurrentSubscription = (state: { subscription: SubscriptionState }) => state.subscription.currentSubscription;
export const selectSubscriptionStatistics = (state: { subscription: SubscriptionState }) => state.subscription.statistics;
export const selectIsLoading = (state: { subscription: SubscriptionState }) => state.subscription.isLoading;
export const selectIsCreating = (state: { subscription: SubscriptionState }) => state.subscription.isCreating;
export const selectIsUpdating = (state: { subscription: SubscriptionState }) => state.subscription.isUpdating;
export const selectIsDeleting = (state: { subscription: SubscriptionState }) => state.subscription.isDeleting;
export const selectIsBulkActionLoading = (state: { subscription: SubscriptionState }) => state.subscription.isBulkActionLoading;
export const selectIsStatisticsLoading = (state: { subscription: SubscriptionState }) => state.subscription.isStatisticsLoading;
export const selectError = (state: { subscription: SubscriptionState }) => state.subscription.error;
export const selectSearchQuery = (state: { subscription: SubscriptionState }) => state.subscription.searchQuery;
export const selectPagination = (state: { subscription: SubscriptionState }) => state.subscription.pagination;
export const selectFilters = (state: { subscription: SubscriptionState }) => state.subscription.filters;
export const selectSelectedSubscriptionIds = (state: { subscription: SubscriptionState }) => state.subscription.selectedSubscriptionIds;

// Export reducer
export default subscriptionSlice;
