import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

// Define the proposal state interface
export interface ProposalState {
  currentProposal: Proposal | null;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  error: string | null;
}

// Initial state
const initialState: ProposalState = {
  currentProposal: null,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
};

// Create the proposal slice
const proposalSlice = createSlice({
  name: "proposal",
  initialState,
  reducers: {
    // Set loading states for operations
    setCreating: (state, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
    },
    setDeleting: (state, action: PayloadAction<boolean>) => {
      state.isDeleting = action.payload;
    },

    // Set error
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Set current proposal
    setCurrentProposal: (state, action: PayloadAction<Proposal | null>) => {
      state.currentProposal = action.payload;
    },
  },
});

// Export actions
export const {
  setCreating,
  setUpdating,
  setDeleting,
  setError,
  clearError,
  setCurrentProposal,
} = proposalSlice.actions;

// Export selectors
export const selectCurrentProposal = (state: { proposal: ProposalState }) =>
  state.proposal.currentProposal;
export const selectIsCreating = (state: { proposal: ProposalState }) =>
  state.proposal.isCreating;
export const selectIsUpdating = (state: { proposal: ProposalState }) =>
  state.proposal.isUpdating;
export const selectIsDeleting = (state: { proposal: ProposalState }) =>
  state.proposal.isDeleting;
export const selectError = (state: { proposal: ProposalState }) =>
  state.proposal.error;

// Export reducer
export default proposalSlice;
