import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type {
  Contract,
  ContractStatistics,
} from "@/lib/api/validators/schemas/contract";
import {
  fetchContracts,
  fetchContract,
  createContract,
  updateContract,
  deleteContract,
  fetchContractStatistics,
} from "../actions/contracts";

interface ContractsState {
  contracts: Contract[];
  currentContract: Contract | null;
  statistics: ContractStatistics;
  isLoading: boolean;
  error: string | null;
}

const initialStatistics: ContractStatistics = {
  total: 0,
  active: 0,
  completed: 0,
  draft: 0,
  terminated: 0,
  expired: 0,
  totalValue: 0,
  averageValue: 0,
};

const initialState: ContractsState = {
  contracts: [],
  currentContract: null,
  statistics: initialStatistics,
  isLoading: false,
  error: null,
};

const contractSlice = createSlice({
  name: "contracts",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    // Set current contract
    setCurrentContract: (state, action: PayloadAction<Contract | null>) => {
      state.currentContract = action.payload;
    },
    // Clear current contract
    clearCurrentContract: (state) => {
      state.currentContract = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch contracts
    builder
      .addCase(fetchContracts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContracts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contracts = action.payload;
        state.error = null;
      })
      .addCase(fetchContracts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to fetch contracts";
      });

    // Fetch single contract
    builder
      .addCase(fetchContract.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContract.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentContract = action.payload;
        state.error = null;
      })
      .addCase(fetchContract.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to fetch contract";
      });

    // Create contract
    builder
      .addCase(createContract.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createContract.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contracts.push(action.payload);
        state.error = null;
      })
      .addCase(createContract.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to create contract";
      });

    // Update contract
    builder
      .addCase(updateContract.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateContract.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.contracts.findIndex(
          (contract) => contract.id === action.payload.id
        );
        if (index !== -1) {
          state.contracts[index] = action.payload;
        }
        if (state.currentContract?.id === action.payload.id) {
          state.currentContract = action.payload;
        }
        state.error = null;
      })
      .addCase(updateContract.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to update contract";
      });

    // Delete contract
    builder
      .addCase(deleteContract.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteContract.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contracts = state.contracts.filter(
          (contract) => contract.id !== action.payload
        );
        if (state.currentContract?.id === action.payload) {
          state.currentContract = null;
        }
        state.error = null;
      })
      .addCase(deleteContract.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to delete contract";
      });

    // Fetch contract statistics
    builder
      .addCase(fetchContractStatistics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContractStatistics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.statistics = action.payload;
        state.error = null;
      })
      .addCase(fetchContractStatistics.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to fetch contract statistics";
      });
  },
});

export const { clearError, setCurrentContract, clearCurrentContract } =
  contractSlice.actions;

export default contractSlice;
