import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Notification preferences interface
// CRUD operations interface
export interface CRUDOperations {
  create: boolean;
  update: boolean;
  delete: boolean;
}

// Context-based notification preferences
export interface NotificationPreferences {
  email: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
    weeklyDigest: boolean;
  };
  push: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
  };
  inApp: {
    room: CRUDOperations;
    chat: CRUDOperations;
    proposal: CRUDOperations;
    contract: CRUDOperations;
    systemAlerts: boolean;
    roleChanges: boolean;
  };
}

export interface MiscellaneousSettings {
  layout: "basic" | "advanced";
  theme: "light" | "dark";
  language: string;
  timezone: string;
  dateFormat: string;
}

// Settings state interface
export interface SettingsState {
  preferences: MiscellaneousSettings;
  notifications: NotificationPreferences;
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
}

// Default notification preferences
const defaultNotificationPreferences: NotificationPreferences = {
  email: {
    room: { create: true, update: true, delete: false },
    chat: { create: true, update: false, delete: false },
    proposal: { create: true, update: true, delete: false },
    contract: { create: true, update: true, delete: false },
    systemAlerts: true,
    weeklyDigest: true,
  },
  push: {
    room: { create: true, update: false, delete: false },
    chat: { create: true, update: false, delete: false },
    proposal: { create: true, update: true, delete: false },
    contract: { create: true, update: true, delete: false },
    systemAlerts: true,
  },
  inApp: {
    room: { create: true, update: true, delete: true },
    chat: { create: true, update: false, delete: false },
    proposal: { create: true, update: true, delete: true },
    contract: { create: true, update: true, delete: true },
    systemAlerts: true,
    roleChanges: true,
  },
};

const defaultPreferences: MiscellaneousSettings = {
  layout: "basic",
  theme: "light",
  language: "en",
  timezone: "UTC",
  dateFormat: "MM/dd/yyyy",
};

// Initial state
const initialState: SettingsState = {
  preferences: defaultPreferences,
  notifications: defaultNotificationPreferences,
  isLoading: false,
  isSaving: false,
  error: null,
};

// Create the settings slice
const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    // Set all settings
    setSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload };
    },

    // Set theme preference
    setPreferenceSettings: (
      state,
      action: PayloadAction<MiscellaneousSettings>
    ) => {
      state.preferences = action.payload;
    },

    updatePreferenceSettings: (
      state,
      action: PayloadAction<Partial<MiscellaneousSettings>>
    ) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },

    resetPreferenceSettings: (state) => {
      state.preferences = defaultPreferences;
    },

    // Set notification preferences
    setNotificationSettings: (
      state,
      action: PayloadAction<NotificationPreferences>
    ) => {
      state.notifications = action.payload;
    },

    // Update specific notification preference
    updateNotificationSettings: (
      state,
      action: PayloadAction<{
        category: keyof NotificationPreferences;
        context?: string;
        operation?: keyof CRUDOperations;
        key: string;
        value: boolean;
      }>
    ) => {
      const { category, context, operation, key, value } = action.payload;

      if (!state.notifications[category]) return;

      // Handle nested CRUD operations
      if (context && operation) {
        const categorySettings = state.notifications[category] as any;
        if (
          categorySettings[context] &&
          typeof categorySettings[context] === "object"
        ) {
          categorySettings[context][operation] = value;
        }
      }
      // Handle direct boolean properties (systemAlerts, weeklyDigest, roleChanges)
      else if (key in state.notifications[category]) {
        (state.notifications[category] as any)[key] = value;
      }
    },

    // Update entire context CRUD operations
    updateContextNotificationSettings: (
      state,
      action: PayloadAction<{
        category: keyof NotificationPreferences;
        context: string;
        operations: Partial<CRUDOperations>;
      }>
    ) => {
      const { category, context, operations } = action.payload;
      const categorySettings = state.notifications[category] as any;

      if (
        categorySettings[context] &&
        typeof categorySettings[context] === "object"
      ) {
        categorySettings[context] = {
          ...categorySettings[context],
          ...operations,
        };
      }
    },

    // Reset notification preferences to defaults
    resetNotificationSettings: (state) => {
      state.notifications = defaultNotificationPreferences;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Set saving state
    setSaving: (state, action: PayloadAction<boolean>) => {
      state.isSaving = action.payload;
    },

    // Set error
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Reset all settings to defaults
    resetSettings: () => {
      return initialState;
    },
  },
});

// Export actions
export const {
  setSettings,
  setPreferenceSettings,
  updatePreferenceSettings,
  resetPreferenceSettings,
  // Notifications
  setNotificationSettings,
  updateNotificationSettings,
  updateContextNotificationSettings,
  resetNotificationSettings,
  setLoading,
  setSaving,
  setError,
  clearError,
  resetSettings,
} = settingsSlice.actions;

// Export selectors
export const selectSettings = (state: { settings: SettingsState }) =>
  state.settings;

// Preference selectors (now nested under preferences)
export const selectPreferences = (state: { settings: SettingsState }) =>
  state.settings.preferences;
export const selectLayout = (state: { settings: SettingsState }) =>
  state.settings.preferences.layout;
export const selectTheme = (state: { settings: SettingsState }) =>
  state.settings.preferences.theme;
export const selectLanguage = (state: { settings: SettingsState }) =>
  state.settings.preferences.language;
export const selectTimezone = (state: { settings: SettingsState }) =>
  state.settings.preferences.timezone;
export const selectDateFormat = (state: { settings: SettingsState }) =>
  state.settings.preferences.dateFormat;

// Notification selectors
export const selectNotificationPreferences = (state: {
  settings: SettingsState;
}) => state.settings.notifications;

// Loading and error state selectors
export const selectIsLoading = (state: { settings: SettingsState }) =>
  state.settings.isLoading;
export const selectIsSaving = (state: { settings: SettingsState }) =>
  state.settings.isSaving;
export const selectError = (state: { settings: SettingsState }) =>
  state.settings.error;

// Contextual Notification Selectors
export const selectRoomNotifications = (state: { settings: SettingsState }) => {
  return {
    email: state.settings.notifications.email.room,
    push: state.settings.notifications.push.room,
    inApp: state.settings.notifications.inApp.room,
  };
};

export const selectChatNotifications = (state: { settings: SettingsState }) => {
  return {
    email: state.settings.notifications.email.chat,
    push: state.settings.notifications.push.chat,
    inApp: state.settings.notifications.inApp.chat,
  };
};

export const selectContractNotifications = (state: {
  settings: SettingsState;
}) => {
  return {
    email: state.settings.notifications.email.contract,
    push: state.settings.notifications.push.contract,
    inApp: state.settings.notifications.inApp.contract,
  };
};

export const selectProposalNotifications = (state: {
  settings: SettingsState;
}) => {
  return {
    email: state.settings.notifications.email.proposal,
    push: state.settings.notifications.push.proposal,
    inApp: state.settings.notifications.inApp.proposal,
  };
};

export const selectSystemAlertsNotifications = (state: {
  settings: SettingsState;
}) => {
  return {
    email: state.settings.notifications.email.systemAlerts,
    push: state.settings.notifications.push.systemAlerts,
    inApp: state.settings.notifications.inApp.systemAlerts,
  };
};

export const selectWeeklyDigestNotifications = (state: {
  settings: SettingsState;
}) => {
  return {
    email: state.settings.notifications.email.weeklyDigest,
    push: false, // Weekly digest is email-only
    inApp: false,
  };
};

export const selectRoleChangesNotifications = (state: {
  settings: SettingsState;
}) => {
  return {
    email: false, // Role changes are inApp-only
    push: false,
    inApp: state.settings.notifications.inApp.roleChanges,
  };
};

// Export reducer
export default settingsSlice.reducer;
