import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  searchRequests,
  getRequest,
  createRequest,
  updateRequest,
  deleteRequest,
  performBulkAction,
  getRequestStatistics,
} from "@/store/actions/request";
import type { Request } from "@/lib/api/validators/schemas/request";

// Request state interface
export interface RequestState {
  // Data
  requests: Request[];
  currentRequest: Request | null;
  statistics: any | null;

  // UI State
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkActionLoading: boolean;
  isStatisticsLoading: boolean;

  // Error handling
  error: string | null;

  // Search and pagination
  searchQuery: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null;

  // Filters
  filters: {
    status?: string;
    userId?: string;
    tender_id?: string;
  };
}

// Initial state
const initialState: RequestState = {
  requests: [],
  currentRequest: null,
  statistics: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isBulkActionLoading: false,
  isStatisticsLoading: false,
  error: null,
  searchQuery: "",
  pagination: null,
  filters: {},
};

// Request slice
const requestSlice = createSlice({
  name: "request",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Set loading states
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCreating: (state, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
    },
    setUpdating: (state, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
    },
    setDeleting: (state, action: PayloadAction<boolean>) => {
      state.isDeleting = action.payload;
    },
    setBulkActionLoading: (state, action: PayloadAction<boolean>) => {
      state.isBulkActionLoading = action.payload;
    },

    // Set error
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },

    // Set current request
    setCurrentRequest: (state, action: PayloadAction<Request | null>) => {
      state.currentRequest = action.payload;
    },

    // Set search query
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    // Set filters
    setFilters: (
      state,
      action: PayloadAction<Partial<RequestState["filters"]>>
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // Clear filters
    clearFilters: (state) => {
      state.filters = {};
    },

    // Reset state
    resetState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    // Search requests
    builder
      .addCase(searchRequests.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchRequests.fulfilled, (state, action) => {
        state.isLoading = false;
        state.requests = action.payload.requests;
        state.pagination = action.payload.pagination;
        state.searchQuery = action.payload.searchQuery || "";
      })
      .addCase(searchRequests.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          (action.payload as any)?.message || "Failed to search requests";
      });

    // Get single request
    builder
      .addCase(getRequest.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentRequest = action.payload;
      })
      .addCase(getRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          (action.payload as any)?.message || "Failed to fetch request";
      });

    // Create request
    builder
      .addCase(createRequest.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createRequest.fulfilled, (state, action) => {
        state.isCreating = false;
        state.requests.unshift(action.payload);
        state.currentRequest = action.payload;
      })
      .addCase(createRequest.rejected, (state, action) => {
        state.isCreating = false;
        state.error =
          (action.payload as any)?.message || "Failed to create request";
      });

    // Update request
    builder
      .addCase(updateRequest.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateRequest.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.requests.findIndex(
          (r) => r.id === action.payload.id
        );
        if (index !== -1) {
          state.requests[index] = action.payload;
        }
        if (state.currentRequest?.id === action.payload.id) {
          state.currentRequest = action.payload;
        }
      })
      .addCase(updateRequest.rejected, (state, action) => {
        state.isUpdating = false;
        state.error =
          (action.payload as any)?.message || "Failed to update request";
      });

    // Delete request
    builder
      .addCase(deleteRequest.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteRequest.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.requests = state.requests.filter(
          (r) => r.id !== action.payload.id
        );
        if (state.currentRequest?.id === action.payload.id) {
          state.currentRequest = null;
        }
      })
      .addCase(deleteRequest.rejected, (state, action) => {
        state.isDeleting = false;
        state.error =
          (action.payload as any)?.message || "Failed to delete request";
      });

    // Bulk actions
    builder
      .addCase(performBulkAction.pending, (state) => {
        state.isBulkActionLoading = true;
        state.error = null;
      })
      .addCase(performBulkAction.fulfilled, (state, action) => {
        state.isBulkActionLoading = false;
        // Handle different bulk actions
        // Note: This would need to be updated based on the specific action performed
      })
      .addCase(performBulkAction.rejected, (state, action) => {
        state.isBulkActionLoading = false;
        state.error =
          (action.payload as any)?.message || "Failed to perform bulk action";
      });

    // Get statistics
    builder
      .addCase(getRequestStatistics.pending, (state) => {
        state.isStatisticsLoading = true;
        state.error = null;
      })
      .addCase(getRequestStatistics.fulfilled, (state, action) => {
        state.isStatisticsLoading = false;
        state.statistics = action.payload;
      })
      .addCase(getRequestStatistics.rejected, (state, action) => {
        state.isStatisticsLoading = false;
        state.error =
          (action.payload as any)?.message || "Failed to fetch statistics";
      });
  },
});

// Export actions
export const {
  clearError,
  setLoading,
  setCreating,
  setUpdating,
  setDeleting,
  setBulkActionLoading,
  setError,
  setCurrentRequest,
  setSearchQuery,
  setFilters,
  clearFilters,
  resetState,
} = requestSlice.actions;

// Export selectors
export const selectRequests = (state: { request: RequestState }) =>
  state.request.requests;
export const selectCurrentRequest = (state: { request: RequestState }) =>
  state.request.currentRequest;
export const selectRequestStatistics = (state: { request: RequestState }) =>
  state.request.statistics;
export const selectIsLoading = (state: { request: RequestState }) =>
  state.request.isLoading;
export const selectIsCreating = (state: { request: RequestState }) =>
  state.request.isCreating;
export const selectIsUpdating = (state: { request: RequestState }) =>
  state.request.isUpdating;
export const selectIsDeleting = (state: { request: RequestState }) =>
  state.request.isDeleting;
export const selectIsBulkActionLoading = (state: { request: RequestState }) =>
  state.request.isBulkActionLoading;
export const selectIsStatisticsLoading = (state: { request: RequestState }) =>
  state.request.isStatisticsLoading;
export const selectError = (state: { request: RequestState }) =>
  state.request.error;
export const selectSearchQuery = (state: { request: RequestState }) =>
  state.request.searchQuery;
export const selectPagination = (state: { request: RequestState }) =>
  state.request.pagination;
export const selectFilters = (state: { request: RequestState }) =>
  state.request.filters;

// Export reducer
export default requestSlice;
