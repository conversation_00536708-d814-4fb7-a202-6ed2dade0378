import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type {
  Room,
  Message,
  MemberWithState,
  UserState,
} from "@/lib/api/validators/schemas/chat";
import type { ChatStatistics } from "@/data/chat-mock";
import {
  sendMessage,
  createRoom,
  updateUserState,
  fetchTypingUsers,
  fetchChatStatistics,
} from "../actions/chat";

interface EnrichedRoom extends Room {
  members: MemberWithState[];
  lastMessage?: Message;
  unreadCount: number;
}

interface EnrichedMessage extends Message {
  sender?: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
}

interface ChatState {
  currentRoomId: string | null;
  messages: Record<string, EnrichedMessage[]>; // roomId -> messages
  typingUsers: Record<string, unknown[]>; // roomId -> typing users
  userStates: Record<string, UserState>; // accountId -> state (all room members)
  isLoading: boolean;
  isLoadingMessages: boolean;
  isSendingMessage: boolean;
  isCreatingRoom: boolean;
  error: string | null;
}

const initialStatistics: ChatStatistics = {
  totalRooms: 0,
  totalMessages: 0,
  activeUsers: 0,
  onlineUsers: 0,
};

const initialState: ChatState = {
  currentRoomId: null,
  messages: {},
  typingUsers: {},
  userStates: {},
  isLoading: false,
  isLoadingMessages: false,
  isSendingMessage: false,
  isCreatingRoom: false,
  error: null,
};

const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    // Set current room
    setCurrentRoom: (state, action: PayloadAction<string | null>) => {
      state.currentRoomId = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Add message optimistically (for real-time updates)
    addMessageOptimistic: (state, action: PayloadAction<EnrichedMessage>) => {
      const message = action.payload;
      if (message.roomId) {
        if (!state.messages[message.roomId]) {
          state.messages[message.roomId] = [];
        }
        state.messages[message.roomId].push(message);
        // Note: Room's last message update now handled by SWR
      }
    },

    // Update typing users for a room
    setTypingUsers: (
      state,
      action: PayloadAction<{ roomId: string; users: unknown[] }>
    ) => {
      const { roomId, users } = action.payload;
      state.typingUsers[roomId] = users;
    },

    // Update user state
    setUserState: (
      state,
      action: PayloadAction<{ accountId: string; userState: UserState }>
    ) => {
      const { accountId, userState } = action.payload;
      state.userStates[accountId] = userState;
      // Note: Member states in rooms now handled by SWR
    },

    // Initialize user states for all room members
    initializeRoomMemberStates: (
      state,
      action: PayloadAction<{ members: Array<{ accountId: string }> }>
    ) => {
      const { members } = action.payload;
      // Initialize all room members with 'offline' state by default
      members.forEach((member) => {
        if (!state.userStates[member.accountId]) {
          state.userStates[member.accountId] = "offline";
        }
      });
    },

    // Clear user states (when leaving room or switching rooms)
    clearUserStates: (state) => {
      state.userStates = {};
    },

    // Bulk update user states for multiple users
    bulkUpdateUserStates: (
      state,
      action: PayloadAction<Record<string, UserState>>
    ) => {
      const updates = action.payload;
      Object.entries(updates).forEach(([accountId, userState]) => {
        state.userStates[accountId] = userState;
      });
    },

    // Mark messages as read (placeholder - room unread count now handled by SWR)
    markMessagesAsRead: (state, action: PayloadAction<string>) => {
      const roomId = action.payload;
      // This would need to be handled by the API/SWR layer
      console.log("Marking messages as read for room:", roomId);
    },
  },
  extraReducers: (builder) => {
    // Note: fetchRooms and fetchMessages are now handled by SWR

    // Send message
    builder
      .addCase(sendMessage.pending, (state) => {
        state.isSendingMessage = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isSendingMessage = false;
        const message = action.payload;

        // Add message to the appropriate room
        if (message.roomId) {
          if (!state.messages[message.roomId]) {
            state.messages[message.roomId] = [];
          }

          // Check if message already exists (to avoid duplicates from optimistic updates)
          const existingMessage = state.messages[message.roomId].find(
            (m) => m.id === message.id
          );
          if (!existingMessage) {
            state.messages[message.roomId].push(message);
          }
          // Note: Room's last message update now handled by SWR
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isSendingMessage = false;
        state.error = action.payload as string;
      });

    // Create room
    builder
      .addCase(createRoom.pending, (state) => {
        state.isCreatingRoom = true;
        state.error = null;
      })
      .addCase(createRoom.fulfilled, (state, action) => {
        state.isCreatingRoom = false;
        // Note: Room list update now handled by SWR mutate in useChat hook
      })
      .addCase(createRoom.rejected, (state, action) => {
        state.isCreatingRoom = false;
        state.error = action.payload as string;
      });

    // Update user state
    builder.addCase(updateUserState.fulfilled, (state, action) => {
      const { userId: accountId, state: userState } = action.payload;
      state.userStates[accountId] = userState;
      // Note: Member states in rooms now handled by SWR
    });

    // Fetch typing users
    builder.addCase(fetchTypingUsers.fulfilled, (state, action) => {
      const { roomId, typingUsers } = action.payload;
      state.typingUsers[roomId] = typingUsers;
    });

    // Note: Statistics are now handled by SWR in useChat hook
  },
});

// Export actions
export const {
  setCurrentRoom,
  clearError,
  addMessageOptimistic,
  setTypingUsers,
  setUserState,
  initializeRoomMemberStates,
  clearUserStates,
  bulkUpdateUserStates,
  markMessagesAsRead,
} = chatSlice.actions;

// Export reducer
export default chatSlice;
