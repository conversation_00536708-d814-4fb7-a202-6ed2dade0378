export { default as adminPermissions } from "./admin.json";
export { default as managerPermissions } from "./manager.json";
export { default as clientPermissions } from "./client.json";

import adminPermissions from "./admin.json";
import managerPermissions from "./manager.json";
import clientPermissions from "./client.json";

interface RolePayload {
  name: string;
  description: string;
  permissions: Record<string, string[]>;
}

export const PERMISSIONS: Record<string, RolePayload> = {
  ADMIN: {
    name: "admin",
    description: "Admin with super cow powers",
    permissions: adminPermissions,
  },
  MANAGER: {
    name: "manager",
    description: "Manager Role",
    permissions: managerPermissions,
  },
  CLIENT: {
    name: "client",
    description: "Client Role",
    permissions: clientPermissions,
  },
};
