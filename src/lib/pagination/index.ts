/**
 * Pagination utility class supporting both cursor-based and offset-based pagination
 * Compatible with Prisma's pagination patterns
 */

export interface PaginationOptions {
  page?: number;
  limit?: number;
  cursor?: string | null;
  orderBy?: Record<string, "asc" | "desc">;
}

export interface PaginationResult<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextCursor?: string | null;
    previousCursor?: string | null;
  };
}

export interface CursorPaginationOptions {
  cursor?: string | null;
  limit?: number;
  orderBy?: Record<string, "asc" | "desc">;
}

export interface CursorPaginationResult<T = any> {
  data: T[];
  pagination: {
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextCursor?: string | null;
    previousCursor?: string | null;
    startCursor?: string | null;
    endCursor?: string | null;
  };
}

export class Pagination {
  private _page: number = 1;
  private _limit: number = 10;
  private _total: number = 0;
  private _cursor: string | null = null;
  private _orderBy: Record<string, "asc" | "desc"> = { id: "desc" };

  constructor(options: PaginationOptions = {}) {
    this._page = options.page || 1;
    this._limit = options.limit || 10;
    this._cursor = options.cursor || null;
    this._orderBy = options.orderBy || { id: "desc" };
  }

  // Getters
  get page(): number {
    return this._page;
  }

  get limit(): number {
    return this._limit;
  }

  get total(): number {
    return this._total;
  }

  get cursor(): string | null {
    return this._cursor;
  }

  get orderBy(): Record<string, "asc" | "desc"> {
    return this._orderBy;
  }

  get offset(): number {
    return (this._page - 1) * this._limit;
  }

  get totalPages(): number {
    return Math.ceil(this._total / this._limit);
  }

  get hasNextPage(): boolean {
    return this._page < this.totalPages;
  }

  get hasPreviousPage(): boolean {
    return this._page > 1;
  }

  get isFirstPage(): boolean {
    return this._page === 1;
  }

  get isLastPage(): boolean {
    return this._page === this.totalPages;
  }

  // Setters
  set page(value: number) {
    this._page = Math.max(1, value);
  }

  set limit(value: number) {
    this._limit = Math.max(1, value);
  }

  set total(value: number) {
    this._total = Math.max(0, value);
  }

  set cursor(value: string | null) {
    this._cursor = value;
  }

  set orderBy(value: Record<string, "asc" | "desc">) {
    this._orderBy = value;
  }

  /**
   * Handle pagination options by setting the appropriate properties
   * @param options - Pagination options to apply
   */
  handlePagination(options: PaginationOptions = {}): void {
    if (options.page) this.page = options.page;
    if (options.limit) this.limit = options.limit;
    if (options.cursor) this.cursor = options.cursor;
    if (options.orderBy) this.orderBy = options.orderBy;
  }

  // Page actions
  nextPage(): Pagination {
    if (this.hasNextPage) {
      this._page += 1;
    }
    return this;
  }

  previousPage(): Pagination {
    if (this.hasPreviousPage) {
      this._page -= 1;
    }
    return this;
  }

  firstPage(): Pagination {
    this._page = 1;
    return this;
  }

  lastPage(): Pagination {
    this._page = this.totalPages;
    return this;
  }

  goToPage(page: number): Pagination {
    this._page = Math.max(1, Math.min(page, this.totalPages));
    return this;
  }

  // Prisma offset-based pagination query builder
  getPrismaOffsetQuery(): {
    skip: number;
    take: number;
    orderBy: Record<string, "asc" | "desc">;
  } {
    return {
      skip: this.offset,
      take: this._limit,
      orderBy: this._orderBy,
    };
  }

  // Prisma cursor-based pagination query builder
  getPrismaCursorQuery(): {
    cursor?: { id: string };
    take: number;
    orderBy: Record<string, "asc" | "desc">;
  } {
    const query: any = {
      take: this._limit,
      orderBy: this._orderBy,
    };

    if (this._cursor) {
      query.cursor = { id: this._cursor };
      query.skip = 1; // Skip the cursor item itself
    }

    return query;
  }

  // Create pagination result for offset-based pagination
  createResult<T>(data: T[]): PaginationResult<T> {
    return {
      data,
      pagination: {
        page: this._page,
        limit: this._limit,
        total: this._total,
        totalPages: this.totalPages,
        hasNextPage: this.hasNextPage,
        hasPreviousPage: this.hasPreviousPage,
      },
    };
  }

  // Create pagination result for cursor-based pagination
  createCursorResult<T>(
    data: T[],
    hasNextPage: boolean = false,
    hasPreviousPage: boolean = false
  ): CursorPaginationResult<T> {
    const startCursor = data.length > 0 ? this.extractCursor(data[0]) : null;
    const endCursor =
      data.length > 0 ? this.extractCursor(data[data.length - 1]) : null;

    return {
      data,
      pagination: {
        limit: this._limit,
        hasNextPage,
        hasPreviousPage,
        nextCursor: hasNextPage ? endCursor : null,
        previousCursor: hasPreviousPage ? startCursor : null,
        startCursor,
        endCursor,
      },
    };
  }

  // Extract cursor from data item (assumes 'id' field)
  private extractCursor(item: any): string | null {
    return item?.id || null;
  }

  // Reset pagination to initial state
  reset(): Pagination {
    this._page = 1;
    this._cursor = null;
    this._total = 0;
    return this;
  }

  // Clone pagination instance
  clone(): Pagination {
    return new Pagination({
      page: this._page,
      limit: this._limit,
      cursor: this._cursor,
      orderBy: { ...this._orderBy },
    });
  }

  // Convert to URL search params
  toURLSearchParams(): URLSearchParams {
    const params = new URLSearchParams();
    params.set("page", this._page.toString());
    params.set("limit", this._limit.toString());

    if (this._cursor) {
      params.set("cursor", this._cursor);
    }

    return params;
  }

  // Create from URL search params
  static fromURLSearchParams(params: URLSearchParams): Pagination {
    return new Pagination({
      page: parseInt(params.get("page") || "1"),
      limit: parseInt(params.get("limit") || "10"),
      cursor: params.get("cursor"),
    });
  }

  // Utility method to get page range for pagination UI
  getPageRange(maxPages: number = 5): number[] {
    const totalPages = this.totalPages;
    const currentPage = this._page;

    if (totalPages <= maxPages) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const half = Math.floor(maxPages / 2);
    let start = Math.max(1, currentPage - half);
    let end = Math.min(totalPages, start + maxPages - 1);

    if (end - start + 1 < maxPages) {
      start = Math.max(1, end - maxPages + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }
}

// Factory functions for common pagination scenarios
export const createPagination = (
  options: PaginationOptions = {}
): Pagination => {
  return new Pagination(options);
};

export const createCursorPagination = (
  options: CursorPaginationOptions = {}
): Pagination => {
  return new Pagination({
    cursor: options.cursor,
    limit: options.limit,
    orderBy: options.orderBy,
  });
};

// Types are already exported above with their interface declarations

/**
 * Usage Examples:
 *
 * // Basic offset-based pagination
 * const pagination = createPagination({ page: 1, limit: 10 });
 * const prismaQuery = pagination.getPrismaOffsetQuery();
 * // Use with Prisma: await prisma.user.findMany(prismaQuery)
 *
 * // Cursor-based pagination
 * const cursorPagination = createCursorPagination({ cursor: 'user_123', limit: 20 });
 * const cursorQuery = cursorPagination.getPrismaCursorQuery();
 * // Use with Prisma: await prisma.user.findMany(cursorQuery)
 *
 * // Page navigation
 * pagination.nextPage().goToPage(5).previousPage();
 *
 * // Create paginated results
 * const result = pagination.createResult(data);
 * const cursorResult = cursorPagination.createCursorResult(data, true, false);
 */
