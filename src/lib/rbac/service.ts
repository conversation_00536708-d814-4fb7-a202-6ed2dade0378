/**
 * RBAC Service
 *
 * Core service for Role-Based Access Control functionality.
 * Handles permission checking, role management, and user authorization.
 */

import {
  Permission,
  Role,
  RBACUser,
  PermissionContext,
  PermissionResult,
  RBACConfig,
  RBACOperationResult,
  EntityType,
  PermissionAction,
  RBACEvent,
  RBACEventType,
} from "./types";

export class RBACService {
  private config: RBACConfig;
  private permissionCache: Map<string, PermissionResult> = new Map();
  private cacheTimestamps: Map<string, number> = new Map();

  constructor(config: RBACConfig = {}) {
    this.config = {
      enableLogging: config.enableLogging ?? true,
      cachePermissions: config.cachePermissions ?? true,
      cacheTTL: config.cacheTTL ?? 300000, // 5 minutes default
      defaultRoles: config.defaultRoles ?? [],
    };
  }

  /**
   * Check if a user has permission for a specific action on an entity
   */
  async checkPermission(context: PermissionContext): Promise<PermissionResult> {
    const cacheKey = this.getCacheKey(context);

    // Check cache first
    if (this.config.cachePermissions && this.isCacheValid(cacheKey)) {
      const cached = this.permissionCache.get(cacheKey);
      if (cached) {
        this.logEvent("permission_check", context.userId, {
          entity: context.entity,
          action: context.action,
          granted: cached.granted,
          cached: true,
        });
        return cached;
      }
    }

    try {
      // Get user with roles and permissions
      const user = await this.getUserWithRoles(context.userId);
      if (!user || !user.isActive) {
        const result: PermissionResult = {
          granted: false,
          reason: "User not found or inactive",
        };
        this.cacheResult(cacheKey, result);
        return result;
      }

      // Check permissions across all user roles
      const matchedPermissions: Permission[] = [];

      for (const role of user.roles) {
        // Handle new JSON-based permissions structure
        const entityPermissions = role.permissions[context.entity];
        if (entityPermissions && entityPermissions.includes(context.action)) {
          // Create a Permission object for compatibility
          const permission: Permission = {
            id: `${role.id}-${context.entity}-${context.action}`,
            entity: context.entity,
            action: context.action,
            description: `${context.action} permission for ${context.entity}`,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
          matchedPermissions.push(permission);
        }
      }

      const granted = matchedPermissions.length > 0;
      const result: PermissionResult = {
        granted,
        reason: granted
          ? "Permission granted"
          : "No matching permissions found",
        matchedPermissions,
      };

      this.cacheResult(cacheKey, result);

      this.logEvent("permission_check", context.userId, {
        entity: context.entity,
        action: context.action,
        granted,
        matchedPermissions: matchedPermissions.length,
      });

      return result;
    } catch (error) {
      const result: PermissionResult = {
        granted: false,
        reason: `Permission check failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      };

      this.logEvent("permission_check", context.userId, {
        entity: context.entity,
        action: context.action,
        granted: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      return result;
    }
  }

  /**
   * Quick permission check - returns boolean
   */
  async hasPermission(
    userId: string,
    entity: EntityType,
    action: PermissionAction,
    resourceId?: string
  ): Promise<boolean> {
    const result = await this.checkPermission({
      userId,
      entity,
      action,
      resourceId,
    });
    return result.granted;
  }

  /**
   * Get all permissions for a user (flattened from all roles)
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const user = await this.getUserWithRoles(userId);
    if (!user) return [];

    const permissions: Permission[] = [];
    const seen = new Set<string>();

    for (const role of user.roles) {
      // Handle new JSON-based permissions structure
      Object.entries(role.permissions).forEach(([entity, actions]) => {
        actions.forEach((action) => {
          const permissionId = `${role.id}-${entity}-${action}`;
          if (!seen.has(permissionId)) {
            const permission: Permission = {
              id: permissionId,
              entity,
              action: action as PermissionAction,
              description: `${action} permission for ${entity}`,
              createdAt: new Date(),
              updatedAt: new Date(),
            };
            permissions.push(permission);
            seen.add(permissionId);
          }
        });
      });
    }

    return permissions;
  }

  /**
   * Assign a role to a user
   */
  async assignRoleToUser(
    userId: string,
    roleId: string,
    assignedBy: string
  ): Promise<RBACOperationResult> {
    try {
      // Implementation would interact with your database
      // This is a placeholder for the actual database operations

      this.logEvent("user_role_assigned", assignedBy, {
        targetUserId: userId,
        roleId,
      });

      return {
        success: true,
        data: { userId, roleId, assignedBy, assignedAt: new Date() },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to assign role",
        code: "ROLE_ASSIGNMENT_FAILED",
      };
    }
  }

  /**
   * Revoke a role from a user
   */
  async revokeRoleFromUser(
    userId: string,
    roleId: string,
    revokedBy: string
  ): Promise<RBACOperationResult> {
    try {
      // Implementation would interact with your database

      this.logEvent("user_role_revoked", revokedBy, {
        targetUserId: userId,
        roleId,
      });

      return {
        success: true,
        data: { userId, roleId, revokedBy, revokedAt: new Date() },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to revoke role",
        code: "ROLE_REVOCATION_FAILED",
      };
    }
  }

  /**
   * Create a new role
   */
  async createRole(
    name: string,
    description?: string,
    createdBy?: string
  ): Promise<RBACOperationResult<Role>> {
    try {
      const role: Role = {
        id: this.generateId(),
        name,
        description,
        permissions: {}, // Empty object for new JSON-based structure
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Database implementation would go here

      this.logEvent("role_created", createdBy || "system", {
        roleId: role.id,
        roleName: name,
      });

      return {
        success: true,
        data: role,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create role",
        code: "ROLE_CREATION_FAILED",
      };
    }
  }

  /**
   * Create a new permission
   */
  async createPermission(
    entity: EntityType,
    action: PermissionAction,
    description?: string,
    createdBy?: string
  ): Promise<RBACOperationResult<Permission>> {
    try {
      const permission: Permission = {
        id: this.generateId(),
        entity,
        action,
        description,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Database implementation would go here

      this.logEvent("permission_created", createdBy || "system", {
        permissionId: permission.id,
        entity,
        action,
      });

      return {
        success: true,
        data: permission,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create permission",
        code: "PERMISSION_CREATION_FAILED",
      };
    }
  }

  /**
   * Clear permission cache
   */
  clearCache(): void {
    this.permissionCache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * Clear cache for specific user
   */
  clearUserCache(userId: string): void {
    const keysToDelete: string[] = [];
    for (const key of this.permissionCache.keys()) {
      if (key.startsWith(`${userId}:`)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach((key) => {
      this.permissionCache.delete(key);
      this.cacheTimestamps.delete(key);
    });
  }

  // Private helper methods
  private getCacheKey(context: PermissionContext): string {
    return `${context.userId}:${context.entity}:${context.action}:${
      context.resourceId || "global"
    }`;
  }

  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey);
    if (!timestamp) return false;

    return Date.now() - timestamp < this.config.cacheTTL!;
  }

  private cacheResult(cacheKey: string, result: PermissionResult): void {
    if (this.config.cachePermissions) {
      this.permissionCache.set(cacheKey, result);
      this.cacheTimestamps.set(cacheKey, Date.now());
    }
  }

  private generateId(): string {
    return `rbac_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private async getUserWithRoles(_userId: string): Promise<RBACUser | null> {
    // This would be implemented to fetch from your database
    // For now, returning null as placeholder
    return null;
  }

  private logEvent(
    type: RBACEventType,
    userId: string,
    metadata?: Record<string, any>
  ): void {
    if (!this.config.enableLogging) return;

    const event: RBACEvent = {
      id: this.generateId(),
      type,
      userId,
      metadata,
      timestamp: new Date(),
    };

    // In a real implementation, you would persist this to your logging system
    console.log("[RBAC Event]", event);
  }
}
