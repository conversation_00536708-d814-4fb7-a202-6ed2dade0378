"use client";

/**
 * RBAC Context and Provider
 *
 * React context for providing RBAC permission validation functionality
 * throughout the application. Management functionality has been removed.
 */

import React, { createContext, useContext, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { createPermissionId, parsePermissionId } from "@/lib/rbac";

interface RBACContextValue {
  // User data
  currentUser: any;

  // Legacy permission checking (backward compatibility)
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;

  // New entity-based permission checking
  hasEntityPermission: (entity: string, action: string) => boolean;
  hasAnyEntityPermission: (entity: string, actions: string[]) => boolean;
  hasAllEntityPermissions: (entity: string, actions: string[]) => boolean;

  // Loading states
  isLoading: boolean;
  error?: string;
}

const RBACContext = createContext<RBACContextValue | null>(null);

interface RBACProviderProps {
  children: React.ReactNode;
}

export function RBACProvider({ children }: RBACProviderProps) {
  const { user: currentUser, isLoading } = useAuth();

  // Entity-based permission checking functions
  const hasEntityPermission = useCallback(
    (entity: string, action: string): boolean => {
      if (!currentUser?.role?.permissions) return false;
      const entityPermissions = (currentUser.role.permissions as any)[entity];
      return entityPermissions ? entityPermissions.includes(action) : false;
    },
    [currentUser]
  );

  // Legacy string-based permission checking (backward compatibility)
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!currentUser?.role?.permissions) return false;

      // Try to parse as entity:action format
      const parsed = parsePermissionId(permission);
      if (parsed) {
        return hasEntityPermission(parsed.entity, parsed.action);
      }

      // Fallback: check if permissions is still array format (legacy)
      if (Array.isArray(currentUser.role.permissions)) {
        return currentUser.role.permissions.includes(permission);
      }

      return false;
    },
    [currentUser, hasEntityPermission]
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.some((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.every((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  // Entity-based permission checking for multiple permissions
  const hasAnyEntityPermission = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.some((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const hasAllEntityPermissions = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.every((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const contextValue: RBACContextValue = {
    currentUser,
    // Legacy functions
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    // New entity-based functions
    hasEntityPermission,
    hasAnyEntityPermission,
    hasAllEntityPermissions,
    isLoading,
    error: undefined, // No error state needed for validation-only context
  };

  return (
    <RBACContext.Provider value={contextValue}>{children}</RBACContext.Provider>
  );
}

// Hook to use RBAC context
export function useRBAC(): RBACContextValue {
  const context = useContext(RBACContext);
  if (!context) {
    throw new Error("useRBAC must be used within an RBACProvider");
  }
  return context;
}

// Export usePermission hook from hooks file for backward compatibility
export { usePermissions as usePermission } from "@/hooks/useRBAC";
