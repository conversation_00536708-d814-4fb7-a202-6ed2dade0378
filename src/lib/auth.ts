import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { AuthService } from "@/lib/api/services/auth";
import { prisma } from "@/lib/common/prisma";

import type { NextAuthConfig } from "next-auth";
import Google from "next-auth/providers/google";
import LinkedIn from "next-auth/providers/linkedin";
import Credentials from "next-auth/providers/credentials";

const authOptions: NextAuthConfig = {
  adapter: PrismaAdapter(prisma),
  session: { strategy: "jwt" },
  providers: [
    Credentials({
      credentials: {
        username: {},
        password: {},
      },
      async authorize(
        credentials: Record<string, string> | undefined,
        request: Request
      ) {
        const authService = new AuthService();
        const { email, password } = credentials || {};
        const response = await authService.login(email, password);

        if (response?.success) return response?.data;

        return null;
      },
    }),
    Google({
      clientId: process.env.GOOGLE_ID!,
      clientSecret: process.env.GOOGLE_SECRET!,
    }),
    LinkedIn({
      clientId: process.env.LINKEDIN_ID!,
      clientSecret: process.env.LINKEDIN_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({
      token,
      user,
      trigger,
      session,
    }: {
      token: any;
      user: any;
      trigger?: "signIn" | "signUp" | "update";
      session?: any;
    }) {
      // Handle initial sign-in/sign-up
      if (user) {
        token.id = user.id;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
        token.phone = user.phone;
        token.image = user.image;
        token.email = user.email;
        token.role = user.role;
        token.customerId = user.customerId;
        token.createdAt = user.createdAt;
        token.updatedAt = user.updatedAt;
        token.company = user.company;
        token.account = user.account;
        token.payment_methods = user.payment_methods;
      }

      // Handle session updates via update() method
      if (trigger === "update" && session) {
        // Update token with new session data
        if (session.user) {
          // Merge existing token with updated user data
          token.id = session.user.id ?? token.id;
          token.firstName = session.user.firstName ?? token.firstName;
          token.lastName = session.user.lastName ?? token.lastName;
          token.phone = session.user.phone ?? token.phone;
          token.image = session.user.image ?? token.image;
          token.email = session.user.email ?? token.email;
          token.role = session.user.role ?? token.role;
          token.customerId = session.user.customerId ?? token.customerId;
          token.createdAt = session.user.createdAt ?? token.createdAt;
          token.updatedAt = session.user.updatedAt ?? token.updatedAt;
          token.company = session.user.company ?? token.company;
          token.account = session.user.account ?? token.account;
          token.payment_methods =
            session.user.payment_methods ?? token.payment_methods;
        }

        // Handle direct property updates (for avatar updates)
        if (session.image) {
          token.image = session.image;
        }

        // Handle company updates
        if (session.company) {
          token.company = {
            ...token.company,
            ...session.company,
          };
        }

        // Handle account updates
        if (session.account) {
          token.account = {
            ...token.account,
            ...session.account,
          };
        }

        // Handle payment methods updates
        if (session.payment_methods) {
          token.payment_methods = session.payment_methods;
        }

        // Update timestamp to reflect the change
        token.updatedAt = new Date().toISOString();
      }

      return token;
    },
    async session({
      session,
      token,
      trigger,
      newSession,
    }: {
      session: any;
      token: any;
      trigger?: "signIn" | "signUp" | "update";
      newSession?: any;
    }) {
      if (token) {
        // Always sync session with token data
        session.user.id = token.id as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.phone = token.phone as string;
        session.user.image = token.image as string;
        session.user.email = token.email as string;
        session.user.role = token.role;
        session.user.customerId = token.customerId as string;
        session.user.createdAt = token.createdAt as string;
        session.user.updatedAt = token.updatedAt as string;

        // Compute display name from firstName and lastName for compatibility
        session.user.name =
          token.firstName && token.lastName
            ? `${token.firstName} ${token.lastName}`.trim()
            : token.firstName || token.email || "User";

        // Handle complex objects with proper type checking
        session.user.company =
          typeof token.company === "object" && token.company !== null
            ? token.company
            : null;

        session.user.account =
          typeof token.account === "object" && token.account !== null
            ? token.account
            : null;

        session.user.payment_methods = Array.isArray(token.payment_methods)
          ? token.payment_methods
          : [];
      }

      // Handle session updates triggered by update() method
      if (trigger === "update" && newSession) {
        console.log("Session update triggered:", newSession);

        // Merge new session data
        session = {
          ...session,
          ...newSession,
          user: {
            ...session.user,
            ...newSession.user,
          },
        };
      }

      return session;
    },
  },
  debug: true,
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/",
    signOut: "/",
    error: "/error",
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions);
