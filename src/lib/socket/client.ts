"use client";

import Pusher from "pusher-js";
import type { NotificationSocketData } from "./server";
import { api } from "@/lib/common/requests";

// Types for client-side pusher events
export interface SocketClientEvents {
  notification: (data: NotificationSocketData) => void;
  authenticated: (data: { success: boolean; userId: string }) => void;
  authentication_error: (data: { error: string }) => void;
  joined_context: (data: {
    contextType: string;
    contextId: string;
    room: string;
  }) => void;
  left_context: (data: {
    contextType: string;
    contextId: string;
    room: string;
  }) => void;
  user_state_changed: (data: {
    userId: string;
    userName?: string;
    state: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  user_state_updated: (data: {
    success: boolean;
    userId: string;
    state: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  bulk_user_states: (data: {
    roomId?: string;
    states: Record<string, string>;
    timestamp: string;
  }) => void;
  user_activity_detected: (data: {
    userId: string;
    roomId?: string;
    timestamp: string;
  }) => void;
  force_user_offline: (data: {
    userId: string;
    reason?: string;
    timestamp: string;
  }) => void;
  connect: () => void;
  disconnect: () => void;
  connect_error: (error: Error) => void;
}

// Pusher doesn't use server events in the same way as Socket.IO
// Instead, we'll use HTTP API calls for server-side actions
export interface PusherServerActions {
  authenticate: (data: { sessionToken: string }) => Promise<void>;
  joinContext: (data: {
    contextType: string;
    contextId: string;
  }) => Promise<void>;
  leaveContext: (data: {
    contextType: string;
    contextId: string;
  }) => Promise<void>;
  notificationReceived: (data: { notificationId: string }) => Promise<void>;
  updateUserState: (data: {
    userId: string;
    state: string;
    roomId?: string;
    userName?: string;
  }) => Promise<void>;
  requestRoomMemberStates: (data: { roomId: string }) => Promise<void>;
  userActivity: (data: { roomId?: string }) => Promise<void>;
  forceUserOffline: (data: {
    targetUserId: string;
    reason?: string;
  }) => Promise<void>;
}

/**
 * Singleton Pusher Manager
 * Ensures only one pusher connection per application instance
 */
class PusherManager {
  private static instance: PusherManager;
  private pusher: Pusher | null = null;
  private currentUserId: string | null = null;
  private connectionPromise: Promise<Pusher> | null = null;
  private eventListeners = new Map<string, Set<Function>>();
  private subscribedChannels = new Map<string, any>();
  private contexts = new Set<string>();
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  private constructor() {
    // Private constructor for singleton
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PusherManager {
    if (!PusherManager.instance) {
      PusherManager.instance = new PusherManager();
    }
    return PusherManager.instance;
  }

  /**
   * Initialize or get existing pusher connection
   */
  public async connect(userId?: string): Promise<Pusher> {
    // If already connected with same user, return existing pusher
    if (this.pusher && this.currentUserId === userId) {
      return this.pusher;
    }

    // If connection is in progress, wait for it
    if (this.connectionPromise) {
      return await this.connectionPromise;
    }

    // If user changed, disconnect and reconnect
    if (this.pusher && this.currentUserId !== userId) {
      this.disconnect();
    }

    // Create new connection
    this.connectionPromise = this.createConnection(userId);

    try {
      const pusher = await this.connectionPromise;
      this.connectionPromise = null;
      return pusher;
    } catch (error) {
      this.connectionPromise = null;
      throw error;
    }
  }

  /**
   * Create new pusher connection
   */
  private async createConnection(userId?: string): Promise<Pusher> {
    return new Promise((resolve, reject) => {
      this.isConnecting = true;
      this.currentUserId = userId || null;

      // Get Pusher configuration from environment variables
      const pusherKey =
        process.env.NEXT_PUBLIC_PUSHER_KEY || "ed8fcd69cf73132cc77f";
      const pusherCluster = process.env.NEXT_PUBLIC_PUSHER_CLUSTER || "mt1";

      this.pusher = new Pusher(pusherKey, {
        cluster: pusherCluster,
        forceTLS: true,
        authEndpoint: "/api/pusher/auth",
        auth: {
          headers: {
            Authorization: `Bearer ${userId}`,
          },
        },
      });

      // Connection success handler
      const onConnect = () => {
        console.log("🔍 [Pusher Client] Connected:", {
          pusherKey,
          cluster: pusherCluster,
          userId,
          willAuthenticate: !!userId,
        });

        this.isConnecting = false;
        this.reconnectAttempts = 0;

        // Auto-authenticate if userId provided
        if (userId && this.pusher) {
          console.log("🔍 [Pusher Client] Authenticating user:", { userId });
          this.authenticate(userId);
        }

        // Re-join contexts after reconnection
        this.rejoinContexts();

        // Trigger connect listeners
        this.triggerEventListeners("connect");
        resolve(this.pusher!);
      };

      // Connection error handler
      const onConnectError = (error: any) => {
        this.isConnecting = false;
        this.triggerEventListeners("connect_error", error);

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(
            new Error(
              `Failed to connect after ${this.maxReconnectAttempts} attempts`
            )
          );
        } else {
          this.reconnectAttempts++;
        }
      };

      // Disconnect handler
      const onDisconnect = () => {
        this.isConnecting = false;
        this.triggerEventListeners("disconnect");
      };

      // Setup Pusher event handlers
      this.pusher.connection.bind("connected", onConnect);
      this.pusher.connection.bind("error", onConnectError);
      this.pusher.connection.bind("disconnected", onDisconnect);

      // Setup application event handlers
      this.setupEventHandlers();
    });
  }

  /**
   * Setup application-specific event handlers
   */
  private setupEventHandlers(): void {
    if (!this.pusher) return;

    // Subscribe to user-specific channel for notifications and authentication
    if (this.currentUserId) {
      const userChannel = this.pusher.subscribe(
        `private-user-${this.currentUserId}`
      );
      this.subscribedChannels.set(`user-${this.currentUserId}`, userChannel);

      // Authentication events
      userChannel.bind("authenticated", (data: any) => {
        console.log("🔍 [Pusher Client] Authentication successful:", data);
        this.triggerEventListeners("authenticated", data);
      });

      userChannel.bind("authentication_error", (data: any) => {
        console.error("🔍 [Pusher Client] Authentication failed:", data);
        this.triggerEventListeners("authentication_error", data);
      });

      // Notification events
      userChannel.bind("notification", (data: any) => {
        // Auto-acknowledge via HTTP API
        this.acknowledgeNotification(data.id);
        this.triggerEventListeners("notification", data);
      });

      // User state events
      userChannel.bind("user_state_changed", (data: any) => {
        this.triggerEventListeners("user_state_changed", data);
      });

      userChannel.bind("user_state_updated", (data: any) => {
        this.triggerEventListeners("user_state_updated", data);
      });

      // Bulk user states event
      userChannel.bind("bulk_user_states", (data: any) => {
        console.log("🔍 [Pusher Client] Received bulk user states:", data);
        this.triggerEventListeners("bulk_user_states", data);
      });

      // User activity detected event
      userChannel.bind("user_activity_detected", (data: any) => {
        console.log("🔍 [Pusher Client] User activity detected:", data);
        this.triggerEventListeners("user_activity_detected", data);
      });

      // Force user offline event
      userChannel.bind("force_user_offline", (data: any) => {
        console.log("🔍 [Pusher Client] Force user offline:", data);
        this.triggerEventListeners("force_user_offline", data);
      });
    }
  }

  /**
   * Disconnect pusher
   */
  public disconnect(): void {
    if (this.pusher) {
      // Unsubscribe from all channels
      this.subscribedChannels.forEach((channel, channelName) => {
        channel.unbind_all();
        this.pusher?.unsubscribe(channelName);
      });

      this.pusher.disconnect();
      this.pusher = null;
    }

    this.currentUserId = null;
    this.isConnecting = false;
    this.connectionPromise = null;
    this.contexts.clear();
    this.subscribedChannels.clear();
    this.eventListeners.clear();
    this.reconnectAttempts = 0;
  }

  /**
   * Get current pusher instance
   */
  public getPusher(): Pusher | null {
    return this.pusher;
  }

  /**
   * Check if pusher is connected
   */
  public isConnected(): boolean {
    return this.pusher?.connection.state === "connected" || false;
  }

  /**
   * Get connection status
   */
  public getStatus() {
    return {
      connected: this.isConnected(),
      connectionState: this.pusher?.connection.state,
      userId: this.currentUserId,
      contexts: Array.from(this.contexts),
      subscribedChannels: Array.from(this.subscribedChannels.keys()),
      eventListenerCount: this.eventListeners.size,
    };
  }

  /**
   * Add event listener
   */
  public addEventListener<K extends keyof SocketClientEvents>(
    event: K,
    listener: SocketClientEvents[K]
  ): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  /**
   * Remove event listener
   */
  public removeEventListener<K extends keyof SocketClientEvents>(
    event: K,
    listener: SocketClientEvents[K]
  ): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);

      if (listeners.size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * Trigger event listeners
   */
  private triggerEventListeners(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(...args);
        } catch (error) {
          // Silently handle listener errors
        }
      });
    }
  }

  /**
   * Join context
   */
  public joinContext(contextType: string, contextId: string): void {
    const roomKey = `${contextType}:${contextId}`;

    if (this.contexts.has(roomKey)) {
      return;
    }

    if (!this.pusher || this.pusher.connection.state !== "connected") {
      return;
    }

    // Subscribe to context-specific channel
    const channelName = `private-${contextType}-${contextId}`;
    const channel = this.pusher.subscribe(channelName);
    this.subscribedChannels.set(channelName, channel);

    // Bind context-specific events
    channel.bind("joined_context", (data: any) => {
      this.triggerEventListeners("joined_context", data);
    });

    channel.bind("left_context", (data: any) => {
      this.triggerEventListeners("left_context", data);
    });

    // Send join request via HTTP API
    this.sendContextAction("join", { contextType, contextId });
    this.contexts.add(roomKey);
  }

  /**
   * Leave context
   */
  public leaveContext(contextType: string, contextId: string): void {
    const roomKey = `${contextType}:${contextId}`;

    if (!this.contexts.has(roomKey)) {
      return;
    }

    if (!this.pusher) {
      return;
    }

    // Unsubscribe from context-specific channel
    const channelName = `private-${contextType}-${contextId}`;
    const channel = this.subscribedChannels.get(channelName);
    if (channel) {
      channel.unbind_all();
      this.pusher.unsubscribe(channelName);
      this.subscribedChannels.delete(channelName);
    }

    // Send leave request via HTTP API
    this.sendContextAction("leave", { contextType, contextId });
    this.contexts.delete(roomKey);
  }

  /**
   * Send context action via HTTP API
   */
  private async sendContextAction(
    action: "join" | "leave",
    data: { contextType: string; contextId: string }
  ): Promise<void> {
    try {
      await api.postWithHeaders(
        "pusher/context",
        {
          action,
          ...data,
        },
        {
          Authorization: `Bearer ${this.currentUserId}`,
        }
      );
    } catch (error) {
      console.error(`Failed to ${action} context:`, error);
    }
  }

  /**
   * Acknowledge notification via HTTP API
   */
  private async acknowledgeNotification(notificationId: string): Promise<void> {
    try {
      await api.postWithHeaders(
        "pusher/notification-ack",
        {
          notificationId,
        },
        {
          Authorization: `Bearer ${this.currentUserId}`,
        }
      );
    } catch (error) {
      console.error("Failed to acknowledge notification:", error);
    }
  }

  /**
   * Authenticate user via HTTP API
   */
  public async authenticate(userId: string): Promise<void> {
    try {
      await api.postWithHeaders(
        "pusher/authenticate",
        {
          userId,
        },
        {
          Authorization: `Bearer ${userId}`,
        }
      );
    } catch (error) {
      console.error("Failed to authenticate user:", error);
    }
  }

  /**
   * Re-join all contexts after reconnection
   */
  private rejoinContexts(): void {
    if (
      !this.pusher ||
      this.pusher.connection.state !== "connected" ||
      this.contexts.size === 0
    )
      return;

    this.contexts.forEach((roomKey) => {
      const [contextType, contextId] = roomKey.split(":");
      if (contextType && contextId) {
        // Re-subscribe to the channel
        const channelName = `private-${contextType}-${contextId}`;
        if (!this.subscribedChannels.has(channelName)) {
          const channel = this.pusher!.subscribe(channelName);
          this.subscribedChannels.set(channelName, channel);
        }
      }
    });
  }

  /**
   * Update user state
   */
  public updateUserState(
    userId: string,
    state: string,
    roomId?: string,
    userName?: string
  ): void {
    this.sendServerAction("updateUserState", {
      userId,
      state,
      roomId,
      userName,
    });
  }

  /**
   * Request room member states (triggers bulk sync)
   */
  public requestRoomMemberStates(roomId: string): void {
    console.log("🔍 [Pusher Client] Requesting room member states:", roomId);
    this.sendServerAction("requestRoomMemberStates", { roomId });
  }

  /**
   * Send user activity signal
   */
  public sendUserActivity(roomId?: string): void {
    console.log("🔍 [Pusher Client] Sending user activity:", roomId);
    this.sendServerAction("userActivity", { roomId });
  }

  /**
   * Force user offline (admin action)
   */
  public forceUserOffline(targetUserId: string, reason?: string): void {
    console.log("🔍 [Pusher Client] Force user offline:", {
      targetUserId,
      reason,
    });
    this.sendServerAction("forceUserOffline", { targetUserId, reason });
  }

  /**
   * Send server action via HTTP API
   */
  private async sendServerAction(action: string, data: any): Promise<void> {
    try {
      await api.postWithHeaders(
        "pusher/action",
        {
          action,
          data,
        },
        {
          Authorization: `Bearer ${this.currentUserId}`,
        }
      );
    } catch (error) {
      console.error(`Failed to send server action ${action}:`, error);
    }
  }

  /**
   * Get current contexts
   */
  public getContexts(): string[] {
    return Array.from(this.contexts);
  }

  /**
   * Clear all contexts
   */
  public clearContexts(): void {
    this.contexts.clear();
  }
}

// Export singleton instance
export const pusherManager = PusherManager.getInstance();

// Export convenience functions
export const connectSocket = (userId?: string) => pusherManager.connect(userId);
export const disconnectSocket = () => pusherManager.disconnect();
export const getSocket = () => pusherManager.getPusher();
export const isSocketConnected = () => pusherManager.isConnected();
export const getSocketStatus = () => pusherManager.getStatus();
export const addEventListener = <K extends keyof SocketClientEvents>(
  event: K,
  listener: SocketClientEvents[K]
) => pusherManager.addEventListener(event, listener);
export const removeEventListener = <K extends keyof SocketClientEvents>(
  event: K,
  listener: SocketClientEvents[K]
) => pusherManager.removeEventListener(event, listener);
export const joinContext = (contextType: string, contextId: string) =>
  pusherManager.joinContext(contextType, contextId);
export const leaveContext = (contextType: string, contextId: string) =>
  pusherManager.leaveContext(contextType, contextId);
export const updateUserState = (
  userId: string,
  state: string,
  roomId?: string,
  userName?: string
) => pusherManager.updateUserState(userId, state, roomId, userName);
export const requestRoomMemberStates = (roomId: string) =>
  pusherManager.requestRoomMemberStates(roomId);
export const sendUserActivity = (roomId?: string) =>
  pusherManager.sendUserActivity(roomId);
export const forceUserOffline = (targetUserId: string, reason?: string) =>
  pusherManager.forceUserOffline(targetUserId, reason);
export const authenticateSocket = (userId: string) =>
  pusherManager.authenticate(userId);

// Re-export types
export type { NotificationSocketData };
