import Pusher from "pusher";
import {
  BaseService,
  type ServiceResponse,
  type DatabaseOptions,
} from "@/lib/api/services/base";
import { prisma } from "@/lib/common/prisma";
import type { status } from "@prisma/client";

// Socket-specific status values (subset of the full status enum)
type SocketStatus = Extract<
  status,
  "online" | "offline" | "active" | "inactive"
>;

// Types for socket events
export interface NotificationSocketData {
  id: string;
  title: string;
  message: string;
  type: string;
  category: string;
  userId: string;
  data?: any;
  priority: string;
  createdAt: string;
}

export interface SocketUser {
  userId: string;
  socketId: string;
  sessionId?: string;
}

/**
 * Socket state interface for database persistence
 */
interface SocketState {
  id: string;
  socketId: string;
  sessionId?: string;
  status: status; // Use the full Prisma status enum
  userId: string;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Singleton Pusher Server Manager with Database Persistence
 * Ensures persistent pusher instance across Next.js API routes and app contexts
 * Extends BaseService for database operations and standardized responses
 */
class PusherServerManager extends BaseService {
  // Variables
  protected appId: string;
  protected key: string;
  protected secret: string;
  protected cluster: string;

  // Singleton instance
  private static instance: PusherServerManager;
  private pusher: Pusher | null = null;
  private isInitialized = false;

  // Global pusher server reference for singleton pattern
  private static globalPusherServer: any = null;

  // Store active user connections (in-memory for performance)
  private activeUsers = new Map<string, Set<string>>(); // userId -> Set of connectionIds
  private connectionToUser = new Map<string, string>(); // connectionId -> userId

  private constructor() {
    // Initialize BaseService with pusher-specific configuration
    super({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });

    this.appId = process.env.PUSHER_APP_ID || "";
    this.key = process.env.PUSHER_KEY || "";
    this.secret = process.env.PUSHER_SECRET || "";
    this.cluster = process.env.PUSHER_CLUSTER || "";

    // Set the socket model for database operations
    this.setModel(prisma.socket as any);

    console.log(
      "🔍 [PusherServerManager] Instance created with database persistence"
    );
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PusherServerManager {
    if (!PusherServerManager.instance) {
      PusherServerManager.instance = new PusherServerManager();
    }
    return PusherServerManager.instance;
  }

  /**
   * Set global pusher server reference (from Pages API)
   */
  public static setGlobalPusherServer(pusherServer: any): void {
    PusherServerManager.globalPusherServer = pusherServer;
    console.log("🔗 [PusherServerManager] Global pusher server reference set");
  }

  /**
   * Get global pusher server reference
   */
  public static getGlobalPusherServer(): any {
    return PusherServerManager.globalPusherServer;
  }

  /**
   * Check if global pusher server is available
   */
  public static hasGlobalPusherServer(): boolean {
    return PusherServerManager.globalPusherServer !== null;
  }

  /**
   * Get the current Pusher server instance
   * Creates/restores from persistence if pusher doesn't exist
   * Uses global pusher server from Pages API if available
   */
  public async getServer(): Promise<Pusher | null> {
    // First, try to get from global pusher server (Pages API pattern)
    if (!this.pusher && PusherServerManager.hasGlobalPusherServer()) {
      const globalServer = PusherServerManager.getGlobalPusherServer();
      if (globalServer?.pusher) {
        this.log("info", "Using Pusher server from global Pages API reference");
        this.pusher = globalServer.pusher;

        // Restore state from database if not already done
        if (!this.isInitialized) {
          await this.initializeFromPersistedState();
          this.isInitialized = true;
        }

        return this.pusher;
      }
    }

    if (!this.pusher) {
      if (!this.isInitialized) {
        this.log(
          "info",
          "Pusher server not initialized, creating new instance with persistence restoration"
        );

        // Initialize new pusher instance
        this.pusher = new Pusher({
          appId: this.appId,
          key: this.key,
          secret: this.secret,
          cluster: this.cluster,
          useTLS: true,
        });

        this.isInitialized = true;

        // Initialize state from database
        await this.initializeFromPersistedState();

        this.log(
          "info",
          "Pusher server created and initialized with persisted state"
        );
      } else if (!this.isInitialized) {
        this.log(
          "info",
          "Pusher server not initialized, attempting to restore from persisted state only"
        );

        // Sync state from database on first access (without creating server)
        await this.syncStateFromDatabase();

        // Clean up stale states
        await this.cleanupStaleSocketStates();

        this.log(
          "info",
          `Restored pusher state: ${this.activeUsers.size} users, ${this.connectionToUser.size} connections`
        );
      }
    }

    return this.pusher;
  }

  /**
   * Get the current Pusher server instance (synchronous version for backward compatibility)
   * Uses global pusher server from Pages API if available
   */
  public getServerSync(): Pusher | null {
    // First, try to get from global pusher server (Pages API pattern)
    if (!this.pusher && PusherServerManager.hasGlobalPusherServer()) {
      const globalServer = PusherServerManager.getGlobalPusherServer();
      if (globalServer?.pusher) {
        this.log("info", "Using Pusher server from global Pages API reference");
        this.pusher = globalServer.pusher;
        return this.pusher;
      }
    }

    // Create new instance if not exists
    if (!this.pusher) {
      this.log(
        "info",
        "Pusher server not initialized, creating new instance synchronously"
      );

      // Initialize new pusher instance synchronously
      this.pusher = new Pusher({
        appId: this.appId,
        key: this.key,
        secret: this.secret,
        cluster: this.cluster,
        useTLS: true,
      });

      this.isInitialized = true;

      // Initialize state from database asynchronously (non-blocking)
      this.initializeFromPersistedState().catch((error) => {
        this.log("error", "Failed to initialize from persisted state", {
          error,
        });
      });

      this.log(
        "info",
        "Pusher server created synchronously, state restoration in progress"
      );
    }

    return this.pusher;
  }

  /**
   * Check if server is initialized
   */
  public isServerInitialized(): boolean {
    return this.isInitialized && this.pusher !== null;
  }

  /**
   * Check if server is available for operations
   */
  public isServerAvailable(): boolean {
    return this.pusher !== null;
  }

  /**
   * Get server status information
   */
  public getServerStatus(): {
    initialized: boolean;
    available: boolean;
    activeUsers: number;
    totalConnections: number;
  } {
    return {
      initialized: this.isInitialized,
      available: this.pusher !== null,
      activeUsers: this.activeUsers.size,
      totalConnections: this.connectionToUser.size,
    };
  }

  /**
   * Initialize Pusher server (Singleton)
   */
  public initializeServer(): Pusher {
    if (this.pusher && this.isInitialized) {
      console.log(
        "🔍 [PusherServerManager] Server already initialized, returning existing instance"
      );
      return this.pusher;
    }

    // Validate environment variables
    if (!this.appId || !this.key || !this.secret || !this.cluster) {
      const missingVars = [];
      if (!this.appId) missingVars.push("PUSHER_APP_ID");
      if (!this.key) missingVars.push("PUSHER_KEY");
      if (!this.secret) missingVars.push("PUSHER_SECRET");
      if (!this.cluster) missingVars.push("PUSHER_CLUSTER");

      const errorMsg = `Missing required Pusher environment variables: ${missingVars.join(
        ", "
      )}`;
      console.error("❌ [PusherServerManager]", errorMsg);
      throw new Error(errorMsg);
    }

    console.log("🔍 [PusherServerManager] Initializing new Pusher server");
    try {
      this.pusher = new Pusher({
        appId: this.appId,
        key: this.key,
        secret: this.secret,
        cluster: this.cluster,
        useTLS: true,
      });

      this.isInitialized = true;

      // Initialize state from database
      this.initializeFromPersistedState();

      console.log(
        "🔍 [PusherServerManager] Pusher server initialized successfully"
      );
      return this.pusher;
    } catch (error) {
      console.error(
        "❌ [PusherServerManager] Failed to initialize Pusher server:",
        error
      );
      throw error;
    }
  }

  /**
   * Get user ID by connection ID (for external functions)
   */
  public getUserByConnectionId(connectionId: string): string | undefined {
    return this.connectionToUser.get(connectionId);
  }

  /**
   * Get active users count
   */
  public getActiveUsersCount(): number {
    return this.activeUsers.size;
  }

  /**
   * Get active users for a specific context
   */
  public getActiveUsersInContext(
    contextType: string,
    contextId: string
  ): string[] {
    // In Pusher, we don't have direct access to channel members
    // This would need to be implemented using presence channels or database queries
    return [];
  }

  /**
   * Check if user is online
   */
  public isUserOnline(userId: string): boolean {
    return (
      this.activeUsers.has(userId) && this.activeUsers.get(userId)!.size > 0
    );
  }

  /**
   * Send notification to specific user
   */
  public sendNotificationToUser(
    userId: string,
    notification: NotificationSocketData
  ): boolean {
    console.log("\nPusher Object: ", this.pusher);

    if (!this.pusher) {
      this.log(
        "warn",
        "Pusher server not initialized, cannot send notification to user",
        { userId }
      );
      return false;
    }

    try {
      const userChannel = `private-user-${userId}`;
      console.log("🔍 [PusherServerManager] Sending notification to user:", {
        userId,
        notification,
      });

      // Send to user-specific channel
      this.pusher.trigger(userChannel, "notification", notification);
      return true;
    } catch (error) {
      console.error(
        "🔍 [PusherServerManager] Failed to send notification:",
        error
      );
      return false;
    }
  }

  /**
   * Get user ID by socket ID (for external functions - legacy compatibility)
   */
  public getUserBySocketId(socketId: string): string | undefined {
    return this.connectionToUser.get(socketId);
  }

  /**
   * Get socket states for a user (public method)
   */
  public async getSocketStatesForUser(
    userId: string
  ): Promise<ServiceResponse<SocketState[]>> {
    return await this.getUserSocketStates(userId);
  }

  /**
   * Get all online users from database (public method)
   */
  public async getOnlineUsers(): Promise<ServiceResponse<string[]>> {
    return await this.getOnlineUsersFromDB();
  }

  /**
   * Force cleanup of stale socket states (public method)
   */
  public async forceCleanupStaleStates(): Promise<void> {
    await this.cleanupStaleSocketStates();
  }

  /**
   * Manually sync state from database (public method)
   */
  public async forceSyncFromDatabase(): Promise<void> {
    await this.syncStateFromDatabase();
  }

  // ==================== DATABASE PERSISTENCE METHODS ====================

  /**
   * Persist socket state to database
   */
  private async persistSocketState(
    userId: string,
    socketId: string,
    sessionId?: string,
    status: SocketStatus = "online"
  ): Promise<ServiceResponse<SocketState>> {
    try {
      this.log(
        "info",
        `Persisting socket state for user: ${userId}, socket: ${socketId}`
      );

      const socketState = await this.upsertRecord<SocketState>({
        where: {
          userId_socketId: {
            userId,
            socketId,
          },
        },
        create: {
          socketId,
          sessionId,
          status,
          userId,
          // createdAt is auto-generated by Prisma @default(now())
        },
        update: {
          sessionId,
          status,
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      this.log(
        "info",
        `Socket state persisted successfully: ${socketState.id}`
      );
      return this.createSuccessResponse(
        socketState,
        200,
        "Socket state persisted"
      );
    } catch (error) {
      this.log("error", `Failed to persist socket state`, {
        userId,
        socketId,
        error,
      });
      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        500
      );
    }
  }

  /**
   * Remove socket state from database
   */
  private async removeSocketState(
    userId: string,
    socketId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      this.log(
        "info",
        `Removing socket state for user: ${userId}, socket: ${socketId}`
      );

      await this.deleteRecord({
        where: {
          userId_socketId: {
            userId,
            socketId,
          },
        },
      });

      this.log("info", `Socket state removed successfully`);
      return this.createSuccessResponse(true, 200, "Socket state removed");
    } catch (error) {
      this.log("error", `Failed to remove socket state`, {
        userId,
        socketId,
        error,
      });
      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        500
      );
    }
  }

  /**
   * Get all active socket states for a user from database
   */
  private async getUserSocketStates(
    userId: string
  ): Promise<ServiceResponse<SocketState[]>> {
    try {
      this.log("info", `Getting socket states for user: ${userId}`);

      const socketStates = await this.findManyRecords<SocketState>({
        where: {
          userId,
          status: {
            in: ["online", "active"] as SocketStatus[],
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      this.log(
        "info",
        `Found ${socketStates.length} active socket states for user: ${userId}`
      );
      return this.createSuccessResponse(
        socketStates,
        200,
        "Socket states retrieved"
      );
    } catch (error) {
      this.log("error", `Failed to get user socket states`, { userId, error });
      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        500
      );
    }
  }

  /**
   * Get all online users from database
   */
  private async getOnlineUsersFromDB(): Promise<ServiceResponse<string[]>> {
    try {
      this.log("info", `Getting online users from database`);

      const onlineUsers = await this.findManyRecords<SocketState>({
        where: {
          status: {
            in: ["online", "active"] as SocketStatus[],
          },
        },
        select: {
          userId: true,
        },
        distinct: ["userId"],
      });

      const userIds = onlineUsers.map((socket) => socket.userId);
      this.log("info", `Found ${userIds.length} online users in database`);
      return this.createSuccessResponse(userIds, 200, "Online users retrieved");
    } catch (error) {
      this.log("error", `Failed to get online users from database`, { error });
      return this.createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        500
      );
    }
  }

  /**
   * Sync in-memory state with database on initialization
   */
  private async syncStateFromDatabase(): Promise<void> {
    try {
      this.log("info", `Syncing socket state from database`);

      const onlineUsersResponse = await this.getOnlineUsersFromDB();
      if (!onlineUsersResponse.success || !onlineUsersResponse.data) {
        this.log("warn", `Failed to sync state from database`);
        return;
      }

      // Clear current in-memory state
      this.activeUsers.clear();
      this.connectionToUser.clear();

      // Rebuild in-memory state from database
      for (const userId of onlineUsersResponse.data) {
        const userSocketsResponse = await this.getUserSocketStates(userId);
        if (userSocketsResponse.success && userSocketsResponse.data) {
          const socketIds = new Set<string>();

          for (const socketState of userSocketsResponse.data) {
            socketIds.add(socketState.socketId);
            this.connectionToUser.set(socketState.socketId, userId);
          }

          if (socketIds.size > 0) {
            this.activeUsers.set(userId, socketIds);
          }
        }
      }

      this.log(
        "info",
        `State synced from database: ${this.activeUsers.size} users, ${this.connectionToUser.size} connections`
      );
    } catch (error) {
      this.log("error", `Failed to sync state from database`, { error });
    }
  }

  /**
   * Clean up stale socket states in database
   */
  private async cleanupStaleSocketStates(): Promise<void> {
    try {
      this.log("info", `Cleaning up stale socket states`);

      // Remove socket states older than 1 hour that are still marked as online
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

      const result = await this.deleteManyRecords({
        where: {
          updatedAt: {
            lt: oneHourAgo,
          },
          status: {
            in: ["online", "active"] as SocketStatus[],
          },
        },
      });

      this.log("info", `Cleaned up ${result.count} stale socket states`);
    } catch (error) {
      this.log("error", `Failed to cleanup stale socket states`, { error });
    }
  }

  /**
   * Initialize socket state from persisted database state (async)
   */
  private async initializeFromPersistedState(): Promise<void> {
    try {
      this.log(
        "info",
        "Initializing socket state from persisted database state"
      );

      // Clean up stale states first
      await this.cleanupStaleSocketStates();

      // Sync state from database
      await this.syncStateFromDatabase();

      this.log(
        "info",
        `Pusher state initialized from database: ${this.activeUsers.size} users, ${this.connectionToUser.size} connections`
      );
    } catch (error) {
      this.log("error", "Failed to initialize from persisted state", { error });
    }
  }
}

// Singleton instance
const pusherManager = PusherServerManager.getInstance();

/**
 * Initialize Pusher server (Singleton)
 */
export function initializePusherServer(): Pusher {
  return pusherManager.initializeServer();
}

/**
 * Get the Pusher server instance (synchronous)
 * Creates/restores from persistence if needed
 */
export function getPusherServer(): Pusher | null {
  return pusherManager.getServerSync();
}

/**
 * Get the Pusher server instance with state restoration (async)
 * Creates/restores from persistence if needed
 */
export async function getPusherServerAsync(): Promise<Pusher | null> {
  return await pusherManager.getServer();
}

/**
 * Get or create Pusher server instance with guaranteed persistence restoration
 * This function ensures the server exists and state is restored from database
 */
export async function getOrCreatePusherServer(): Promise<Pusher> {
  const server = await pusherManager.getServer();
  if (!server) {
    throw new Error("Failed to create or retrieve Pusher server instance");
  }
  return server;
}

/**
 * Send notification to specific user
 */
export function sendNotificationToUser(
  userId: string,
  notification: NotificationSocketData
): boolean {
  const status = pusherManager.getServerStatus();
  if (!status.available) {
    console.warn("🚨 Pusher server not available for notifications", {
      userId,
      serverStatus: status,
      suggestion: "Initialize pusher server before sending notifications",
    });

    // Try to initialize the server if it's not available
    try {
      pusherManager.initializeServer();
      console.log("✅ Pusher server initialized successfully for notification");
    } catch (error) {
      console.error("❌ Failed to initialize Pusher server:", error);
      return false;
    }
  }
  return pusherManager.sendNotificationToUser(userId, notification);
}

/**
 * Send notification to context (contract, chat, etc.)
 */
export function sendNotificationToContext(
  contextType: string,
  contextId: string,
  notification: NotificationSocketData,
  excludeUserId?: string
): boolean {
  let pusher = pusherManager.getServerSync();

  console.log("\nSender Pusher Object: ", pusher, "\n");

  if (!pusher) {
    const status = pusherManager.getServerStatus();
    console.warn("🚨 Pusher server not available for context notifications", {
      contextType,
      contextId,
      serverStatus: status,
      suggestion: "Initialize pusher server before sending notifications",
    });

    // Try to initialize the server if it's not available
    try {
      pusher = pusherManager.initializeServer();
      console.log(
        "✅ Pusher server initialized successfully for context notification"
      );
    } catch (error) {
      console.error("❌ Failed to initialize Pusher server:", error);
      return false;
    }
  }

  const channelName = `private-${contextType}-${contextId}`;

  try {
    // In Pusher, we can't exclude specific users from a channel trigger
    // This would need to be handled differently, perhaps by sending individual messages
    // or using presence channels to track members
    pusher.trigger(channelName, "notification", notification);

    return true;
  } catch (error) {
    console.error(
      "🔍 [PusherServerManager] Failed to send context notification:",
      error
    );
    return false;
  }
}

/**
 * Get active users count
 */
export function getActiveUsersCount(): number {
  return pusherManager.getActiveUsersCount();
}

/**
 * Get active users for a specific context
 */
export function getActiveUsersInContext(
  contextType: string,
  contextId: string
): string[] {
  return pusherManager.getActiveUsersInContext(contextType, contextId);
}

/**
 * Check if user is online
 */
export function isUserOnline(userId: string): boolean {
  return pusherManager.isUserOnline(userId);
}

/**
 * Get socket states for a specific user from database
 */
export async function getSocketStatesForUser(
  userId: string
): Promise<ServiceResponse<SocketState[]>> {
  return await pusherManager.getSocketStatesForUser(userId);
}

/**
 * Get all online users from database
 */
export async function getOnlineUsersFromDatabase(): Promise<
  ServiceResponse<string[]>
> {
  return await pusherManager.getOnlineUsers();
}

/**
 * Force cleanup of stale socket states
 */
export async function forceCleanupStaleStates(): Promise<void> {
  await pusherManager.forceCleanupStaleStates();
}

/**
 * Manually sync socket state from database
 */
export async function forceSyncFromDatabase(): Promise<void> {
  await pusherManager.forceSyncFromDatabase();
}

/**
 * Get pusher server status for debugging
 */
export function getPusherServerStatus(): {
  initialized: boolean;
  available: boolean;
  activeUsers: number;
  totalConnections: number;
} {
  return pusherManager.getServerStatus();
}

/**
 * Check if pusher server is available for operations
 */
export function isPusherServerAvailable(): boolean {
  return pusherManager.isServerAvailable();
}

/**
 * Check if Pusher environment variables are configured
 */
export function isPusherConfigured(): boolean {
  return !!(
    process.env.PUSHER_APP_ID &&
    process.env.PUSHER_KEY &&
    process.env.PUSHER_SECRET &&
    process.env.PUSHER_CLUSTER
  );
}

/**
 * Set global pusher server reference (for Pages API integration)
 */
export function setGlobalPusherServer(pusherServer: any): void {
  PusherServerManager.setGlobalPusherServer(pusherServer);
}

/**
 * Check if global pusher server is available
 */
export function hasGlobalPusherServer(): boolean {
  return PusherServerManager.hasGlobalPusherServer();
}

/**
 * Broadcast user state change to specific context
 */
export function broadcastUserStateToContext(
  contextType: string,
  contextId: string,
  userStateData: {
    userId: string;
    userName?: string;
    state: string;
    timestamp?: string;
  },
  excludeUserId?: string
): boolean {
  const pusher = pusherManager.getServerSync();
  if (!pusher) return false;

  try {
    const channelName = `private-${contextType}-${contextId}`;
    const stateChangeData = {
      ...userStateData,
      roomId: contextType === "chat" ? contextId : undefined,
      timestamp: userStateData.timestamp || new Date().toISOString(),
    };

    // In Pusher, we can't exclude specific users from a channel trigger
    // This would need to be handled differently, perhaps by sending individual messages
    // or using presence channels to track members
    pusher.trigger(channelName, "user_state_changed", stateChangeData);

    return true;
  } catch (error) {
    console.error(
      "🔍 [PusherServerManager] Failed to broadcast user state to context:",
      error
    );
    return false;
  }
}

/**
 * Broadcast user state change to specific user
 */
export function broadcastUserStateToUser(
  targetUserId: string,
  userStateData: {
    userId: string;
    userName?: string;
    state: string;
    roomId?: string;
    timestamp?: string;
  }
): boolean {
  const pusher = pusherManager.getServerSync();
  if (!pusher) return false;

  try {
    const stateChangeData = {
      ...userStateData,
      timestamp: userStateData.timestamp || new Date().toISOString(),
    };

    // Send to specific user channel
    pusher.trigger(
      `private-user-${targetUserId}`,
      "user_state_changed",
      stateChangeData
    );

    return true;
  } catch (error) {
    console.error(
      "🔍 [PusherServerManager] Failed to broadcast user state to user:",
      error
    );
    return false;
  }
}
