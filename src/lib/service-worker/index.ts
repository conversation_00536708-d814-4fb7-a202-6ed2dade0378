"use client";

/**
 * Service Worker Registration and Push Notification Management
 *
 * This module handles service worker registration, push notification setup,
 * and provides utilities for managing push subscriptions.
 */

import { api } from "@/lib/common/requests";

export interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  tag?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  requireInteraction?: boolean;
  silent?: boolean;
  options?: any;
}

class ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null;
  private isSupported: boolean = false;
  private pushSubscription: PushSubscription | null = null;

  constructor() {
    if (typeof window !== "undefined") {
      this.isSupported =
        "serviceWorker" in navigator && "PushManager" in window;
    }
  }

  /**
   * Register the service worker
   */
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported) {
      console.warn("Service Workers are not supported in this browser");
      return null;
    }

    try {
      this.registration = await navigator.serviceWorker.register("/sw.js", {
        scope: "/",
        updateViaCache: "none",
      });

      console.log("Service Worker registered successfully:", this.registration);

      // Handle service worker updates
      this.registration.addEventListener("updatefound", () => {
        const newWorker = this.registration?.installing;
        if (newWorker) {
          newWorker.addEventListener("statechange", () => {
            if (
              newWorker.state === "installed" &&
              navigator.serviceWorker.controller
            ) {
              // New service worker is available
              this.notifyUpdate();
            }
          });
        }
      });

      // Register background sync for notifications
      try {
        await this.registration.sync.register("background-sync-notifications");
        console.log("✅ Background sync registered for notifications");
      } catch (syncError) {
        console.warn(
          "⚠️ Background sync registration failed (may not be supported):",
          syncError
        );
      }

      // Register periodic background sync if supported
      if ("periodicSync" in this.registration) {
        try {
          // @ts-ignore - periodicSync is experimental
          await this.registration.periodicSync.register(
            "periodic-notification-sync",
            {
              minInterval: 24 * 60 * 60 * 1000, // 24 hours
            }
          );
          console.log("✅ Periodic background sync registered");
        } catch (periodicError) {
          console.warn(
            "⚠️ Periodic background sync registration failed:",
            periodicError
          );
        }
      }

      return this.registration;
    } catch (error) {
      console.error("Service Worker registration failed:", error);
      return null;
    }
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!("Notification" in window)) {
      console.warn("Notifications are not supported in this browser");
      return "denied";
    }

    if (Notification.permission === "granted") {
      return "granted";
    }

    try {
      const permission = await Notification.requestPermission();
      console.log("Notification permission:", permission);
      return permission;
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return "denied";
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(
    vapidPublicKey: string
  ): Promise<PushSubscription | null> {
    if (!this.registration) {
      console.error("Service Worker not registered");
      return null;
    }

    if (!("PushManager" in window)) {
      console.warn("Push messaging is not supported in this browser");
      return null;
    }

    try {
      // Check if already subscribed
      this.pushSubscription =
        await this.registration.pushManager.getSubscription();

      if (this.pushSubscription) {
        console.log("Already subscribed to push notifications");
        return this.pushSubscription;
      }

      // Subscribe to push notifications
      this.pushSubscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey),
      });

      console.log("Subscribed to push notifications:", this.pushSubscription);
      return this.pushSubscription;
    } catch (error) {
      console.error("Failed to subscribe to push notifications:", error);
      return null;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(): Promise<boolean> {
    if (!this.pushSubscription) {
      console.log("Not subscribed to push notifications");
      return true;
    }

    try {
      // First unsubscribe locally
      const result = await this.pushSubscription.unsubscribe();

      if (result) {
        // Then notify the server to remove the subscription
        try {
          await api.delete("push/subscribe");
          console.log("Push subscription removed from server");
        } catch (serverError) {
          console.warn(
            "Failed to remove subscription from server:",
            serverError
          );
          // Continue anyway since local unsubscribe was successful
        }

        this.pushSubscription = null;
        console.log("Unsubscribed from push notifications");
      }

      return result;
    } catch (error) {
      console.error("Failed to unsubscribe from push notifications:", error);
      return false;
    }
  }

  /**
   * Get current push subscription
   */
  async getPushSubscription(): Promise<PushSubscription | null> {
    if (!this.registration) {
      return null;
    }

    try {
      this.pushSubscription =
        await this.registration.pushManager.getSubscription();
      return this.pushSubscription;
    } catch (error) {
      console.error("Failed to get push subscription:", error);
      return null;
    }
  }

  /**
   * Send push subscription to server
   */
  async sendSubscriptionToServer(
    subscription: PushSubscription
  ): Promise<boolean> {
    try {
      const subscriptionData: PushSubscriptionData = {
        endpoint: subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(subscription.getKey("p256dh")!),
          auth: this.arrayBufferToBase64(subscription.getKey("auth")!),
        },
      };

      await api.post("push/subscribe", subscriptionData);

      console.log("Push subscription sent to server successfully");
      return true;
    } catch (error) {
      console.error("Error sending push subscription to server:", error);
      return false;
    }
  }

  /**
   * Show a local notification (for testing)
   */
  async showLocalNotification(payload: NotificationPayload): Promise<void> {
    if (!this.registration) {
      console.error("Service Worker not registered");
      return;
    }

    if (Notification.permission !== "granted") {
      console.warn("Notification permission not granted");
      return;
    }

    try {
      await this.registration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || "/favicons/android-chrome-192x192.png",
        badge: payload.badge || "/favicons/favicon-32x32.png",
        image: payload.image,
        tag: payload.tag || "local-notification",
        data: payload.data || {},
        actions: payload.actions || [],
        requireInteraction: payload.requireInteraction || false,
        silent: payload.silent || false,
        timestamp: Date.now(),
        vibrate: [200, 100, 200],
      });
    } catch (error) {
      console.error("Failed to show notification:", error);
    }
  }

  /**
   * Update service worker
   */
  async updateServiceWorker(): Promise<void> {
    if (!this.registration) {
      return;
    }

    try {
      await this.registration.update();

      // Skip waiting for new service worker
      if (this.registration.waiting) {
        this.registration.waiting.postMessage({ type: "SKIP_WAITING" });
      }
    } catch (error) {
      console.error("Failed to update service worker:", error);
    }
  }

  /**
   * Manually trigger notification sync
   */
  async triggerNotificationSync(): Promise<boolean> {
    if (!this.registration) {
      console.warn("Service Worker not registered, cannot trigger sync");
      return false;
    }

    try {
      await this.registration.sync.register("background-sync-notifications");
      console.log("✅ Notification sync triggered manually");
      return true;
    } catch (error) {
      console.error("❌ Failed to trigger notification sync:", error);
      return false;
    }
  }

  /**
   * Check if service worker is supported
   */
  isServiceWorkerSupported(): boolean {
    return this.isSupported;
  }

  /**
   * Check if push notifications are supported
   */
  isPushSupported(): boolean {
    return this.isSupported && "PushManager" in window;
  }

  /**
   * Get notification permission status
   */
  getNotificationPermission(): NotificationPermission {
    if (!("Notification" in window)) {
      return "denied";
    }
    return Notification.permission;
  }

  /**
   * Utility: Convert VAPID key to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  /**
   * Utility: Convert ArrayBuffer to Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = "";
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  /**
   * Notify about service worker update
   */
  private notifyUpdate(): void {
    // Dispatch custom event for UI to handle
    window.dispatchEvent(
      new CustomEvent("sw-update-available", {
        detail: { registration: this.registration },
      })
    );
  }
}

// Create singleton instance
export const serviceWorkerManager = new ServiceWorkerManager();

// Auto-initialize service worker
export async function initializeServiceWorker(vapidPublicKey?: string) {
  if (typeof window === "undefined") {
    return;
  }

  try {
    // Register service worker
    const registration = await serviceWorkerManager.registerServiceWorker();

    if (registration && vapidPublicKey) {
      // Request notification permission
      const permission =
        await serviceWorkerManager.requestNotificationPermission();

      if (permission === "granted") {
        // Subscribe to push notifications
        const subscription = await serviceWorkerManager.subscribeToPush(
          vapidPublicKey
        );
        if (subscription) {
          await serviceWorkerManager.sendSubscriptionToServer(subscription);
        }
      }
    }
  } catch (error) {
    console.error("Failed to initialize service worker:", error);
  }
}
