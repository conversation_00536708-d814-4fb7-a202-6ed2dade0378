/**
 * Integration utilities for connecting the notification library
 * with existing hooks and components
 */

import { useEffect } from "react";
import { notificationManager, type NotificationPreferences } from "./index";

/**
 * Hook to automatically sync notification preferences with the notification manager
 * This should be used in your root component or layout
 */
export function useNotificationSync(
  preferences: NotificationPreferences | null
) {
  useEffect(() => {
    if (preferences) {
      notificationManager.setPreferences(preferences);
    }
  }, [preferences]);
}

/**
 * Convert useNotifications preferences format to notification library format
 * This handles any format differences between the hook and library
 */
export function convertPreferencesToLibraryFormat(
  hookPreferences: any
): NotificationPreferences {
  // If the formats are already compatible, return as-is
  if (
    hookPreferences?.email &&
    hookPreferences?.push &&
    hookPreferences?.inApp
  ) {
    // Check if it has the correct structure with CRUD operations
    const hasCorrectStructure =
      hookPreferences.email?.room &&
      hookPreferences.push?.room &&
      hookPreferences.inApp?.room;

    if (hasCorrectStructure) {
      return hookPreferences as NotificationPreferences;
    }
  }

  // Handle conversion from useNotifications format to library format
  // The useNotifications hook uses the correct format already, so this is mostly a passthrough
  return {
    email: {
      room: hookPreferences?.email?.room ?? {
        create: true,
        update: true,
        delete: true,
      },
      chat: hookPreferences?.email?.chat ?? {
        create: true,
        update: true,
        delete: true,
      },
      proposal: hookPreferences?.email?.proposal ?? {
        create: true,
        update: true,
        delete: true,
      },

      contract: hookPreferences?.email?.contract ?? {
        create: true,
        update: true,
        delete: true,
      },
      systemAlerts: hookPreferences?.email?.systemAlerts ?? true,
      weeklyDigest: hookPreferences?.email?.weeklyDigest ?? true,
    },
    push: {
      room: hookPreferences?.push?.room ?? {
        create: true,
        update: false,
        delete: false,
      },
      chat: hookPreferences?.push?.chat ?? {
        create: true,
        update: false,
        delete: false,
      },
      proposal: hookPreferences?.push?.proposal ?? {
        create: true,
        update: false,
        delete: false,
      },

      contract: hookPreferences?.push?.contract ?? {
        create: true,
        update: true,
        delete: false,
      },
      systemAlerts: hookPreferences?.push?.systemAlerts ?? true,
    },
    inApp: {
      room: hookPreferences?.inApp?.room ?? {
        create: true,
        update: true,
        delete: true,
      },
      chat: hookPreferences?.inApp?.chat ?? {
        create: true,
        update: true,
        delete: true,
      },
      proposal: hookPreferences?.inApp?.proposal ?? {
        create: true,
        update: true,
        delete: true,
      },

      contract: hookPreferences?.inApp?.contract ?? {
        create: true,
        update: true,
        delete: true,
      },
      systemAlerts: hookPreferences?.inApp?.systemAlerts ?? true,
      roleChanges: hookPreferences?.inApp?.roleChanges ?? true,
    },
  };
}

/**
 * Integration component that automatically syncs preferences
 * Use this in your app layout or root component
 */
export function NotificationPreferencesSync({
  preferences,
}: {
  preferences: any;
}) {
  useEffect(() => {
    if (preferences) {
      const libraryPreferences = convertPreferencesToLibraryFormat(preferences);
      notificationManager.setPreferences(libraryPreferences);
    }
  }, [preferences]);

  return null; // This component doesn't render anything
}
