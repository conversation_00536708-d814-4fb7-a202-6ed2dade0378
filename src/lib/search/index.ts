"use client";

import { useRef, useCallback } from "react";
import { toast } from "sonner";
import type { AppDispatch } from "@/store";

// Generic search configuration interface
export interface SearchConfig<TData, TApiData> {
  // Data transformation functions
  adaptApiToUI: (apiData: TApiData) => TData;

  // Search field configuration
  searchFields: (item: TApiData) => string[];

  // State setters
  setFilteredData: (data: TApiData[]) => void;
  setSearchTerm: (term: string) => void;
  setIsSearching?: (loading: boolean) => void;

  // Redux action
  searchAction: (params: SearchParams) => any;

  // Current data
  currentData?: TData[];
  loadedApiData?: TApiData[];

  // Debounce configuration
  debounceDelay?: number;
}

// Search parameters interface
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  [key: string]: any;
}

// Search result interface
export interface SearchResult<TData> {
  success: boolean;
  data?: TData[];
  pagination?: any;
  searchQuery?: string;
  source?: "local" | "api";
  error?: string;
}

/**
 * Creates a composable search function for any data type
 * @param dispatch Redux dispatch function
 * @param config Search configuration object
 * @returns Search function and cleanup
 */
export function createSearchFunction<TData, TApiData>(
  dispatch: AppDispatch,
  config: SearchConfig<TData, TApiData>
) {
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const searchFunction = useCallback(
    async (searchParams: SearchParams): Promise<SearchResult<TData>> => {
      try {
        //
        const query = searchParams.query?.trim() || "";

        const {
          adaptApiToUI,
          searchFields,
          setFilteredData,
          setSearchTerm,
          setIsSearching,
          searchAction,
          currentData = [],
          loadedApiData,
          debounceDelay = 1100,
        } = config;

        // If no query, reset to show all data
        if (!query) {
          setFilteredData([]);
          setSearchTerm("");
          setIsSearching?.(false);
          return {
            success: true,
            data: currentData,
            pagination: null,
            searchQuery: "",
          };
        }

        // Update search term and set loading state
        setSearchTerm(query);
        setIsSearching?.(true);

        // First, try to filter from loaded data if available
        if (loadedApiData && Array.isArray(loadedApiData)) {
          // Perform local filtering
          const localFilteredData = loadedApiData.filter((item: TApiData) => {
            const searchLower = query.toLowerCase();
            const fieldsToSearch = searchFields(item);

            return fieldsToSearch.some((field) =>
              field?.toLowerCase().includes(searchLower)
            );
          });

          // If we have local results, use them
          if (localFilteredData.length > 0 || loadedApiData.length > 0) {
            setFilteredData(localFilteredData);
            setIsSearching?.(false); // Clear loading state for local search
            return {
              success: true,
              data: localFilteredData.map(adaptApiToUI),
              pagination: null, // Local filtering doesn't have server pagination
              searchQuery: query,
              source: "local", // Indicate this was a local search
            };
          }
        }

        // If no local data available or no results, perform debounced API call
        return new Promise((resolve) => {
          // Clear existing timer
          if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
          }

          // Set new timer for debounce delay
          debounceTimerRef.current = setTimeout(async () => {
            try {
              const result = await dispatch(searchAction(searchParams));

              // Handle response based on Redux action result pattern
              if (result.type.endsWith("/fulfilled")) {
                const payload = result.payload as any;
                const apiData =
                  payload.contracts ||
                  payload.proposals ||
                  payload.documents ||
                  payload.data ||
                  [];
                setFilteredData(apiData);
                setIsSearching?.(false); // Clear loading state on success
                resolve({
                  success: true,
                  data: apiData.map(adaptApiToUI),
                  pagination: payload.pagination,
                  searchQuery: payload.searchQuery || query,
                  source: "api", // Indicate this was an API search
                });
              } else if (result.type.endsWith("/rejected")) {
                const errorMessage =
                  (result.payload as any)?.message || "Failed to search data";
                toast.error(errorMessage);
                setIsSearching?.(false); // Clear loading state on error
                resolve({ success: false, error: errorMessage });
              }
              resolve(result);
            } catch (apiError) {
              const errorMessage =
                apiError instanceof Error
                  ? apiError.message
                  : "Failed to search data";
              toast.error(errorMessage);
              setIsSearching?.(false); // Clear loading state on API error
              resolve({ success: false, error: errorMessage });
            }
          }, debounceDelay);
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to search data";
        toast.error(errorMessage);
        config.setIsSearching?.(false); // Clear loading state on general error
        throw error;
      }
    },
    [dispatch, config]
  );

  // Cleanup function
  const cleanup = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
  }, []);

  return { searchFunction, cleanup };
}

/**
 * Hook for creating a search function with automatic cleanup
 * @param dispatch Redux dispatch function
 * @param config Search configuration object
 * @returns Search function
 */
export function useSearch<TData, TApiData>(
  dispatch: AppDispatch,
  config: SearchConfig<TData, TApiData>
) {
  const { searchFunction, cleanup } = createSearchFunction(dispatch, config);

  // Cleanup on unmount
  useCallback(() => {
    return cleanup;
  }, [cleanup]);

  return searchFunction;
}
