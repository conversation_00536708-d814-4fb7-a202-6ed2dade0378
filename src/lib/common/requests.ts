import axios, { AxiosResponse, AxiosError } from "axios";

// Create axios instance with default configuration
export const requests = axios.create({
  baseURL: "/api/",
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor for adding auth tokens or other headers
requests.interceptors.request.use(
  (config) => {
    // Add any global request modifications here
    // For example, adding auth tokens from localStorage/cookies
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common response patterns
requests.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.warn("Unauthorized access - consider redirecting to login");
    }

    if (error.response?.status === 403) {
      // Handle forbidden access
      console.warn("Forbidden access - insufficient permissions");
    }

    if (error.response?.status && error.response.status >= 500) {
      // Handle server errors
      console.error("Server error occurred:", error.response?.data);
    }

    return Promise.reject(error);
  }
);

// Enhanced fetcher function for SWR with better error handling
export const fetcher = async (url: string) => {
  try {
    const response = await requests.get(url);
    return response.data ?? [];
  } catch (error) {
    throw error; // Re-throw for SWR error handling
  }
};

const MultiPartContentType: Record<string, string> = {
  "Content-Type": "multipart/form-data",
};

// API helper functions for common operations
export const api = {
  // Generic CRUD operations
  get: async <T = any>(
    endpoint: string,
    params?: Record<string, any>
  ): Promise<T> => {
    const response = await requests.get(endpoint, { params });
    return response.data;
  },

  post: async <T = any>(endpoint: string, data?: any): Promise<T> => {
    const response = await requests.post(endpoint, data);
    return response.data;
  },

  // Post with custom headers
  postWithHeaders: async <T = any>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> => {
    const response = await requests.post(endpoint, data, { headers });
    return response.data;
  },

  put: async <T = any>(endpoint: string, data?: any): Promise<T> => {
    const response = await requests.put(endpoint, data);
    return response.data;
  },

  patch: async <T = any>(endpoint: string, data?: any): Promise<T> => {
    const response = await requests.patch(endpoint, data);
    return response.data;
  },

  delete: async <T = any>(endpoint: string, data?: any): Promise<T> => {
    const response = await requests.delete(endpoint, { data });
    return response.data;
  },

  // File upload helper
  upload: async <T = any>(endpoint: string, formData: FormData): Promise<T> => {
    const response = await requests.post(endpoint, formData, {
      headers: MultiPartContentType,
    });
    return response.data;
  },

  upPatch: async <T = any>(
    endpoint: string,
    formData: FormData
  ): Promise<T> => {
    const response = await requests.patch(endpoint, formData, {
      headers: MultiPartContentType,
    });
    return response.data;
  },
};

// Export the fetcher as default for SWR compatibility
export default fetcher;
