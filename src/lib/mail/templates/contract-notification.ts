import { generateBaseTemplate, COLORS, formatCurrency } from "./base";

export interface ContractNotificationTemplateProps {
  contractTitle: string;
  clientName: string;
  totalValue: number;
  status: string;
}

/**
 * Get status color and emoji based on contract status
 */
function getStatusInfo(status: string): {
  color: string;
  emoji: string;
  label: string;
} {
  switch (status.toLowerCase()) {
    case "active":
      return { color: COLORS.neon, emoji: "✅", label: "Active" };
    case "completed":
      return { color: "#10b981", emoji: "🎉", label: "Completed" };
    case "draft":
      return { color: COLORS.gray, emoji: "📝", label: "Draft" };
    case "terminated":
      return { color: "#ef4444", emoji: "❌", label: "Terminated" };
    case "expired":
      return { color: "#f59e0b", emoji: "⏰", label: "Expired" };
    default:
      return { color: COLORS.gray, emoji: "📄", label: status };
  }
}

/**
 * Generate contract notification email template
 */
export function generateContractNotificationTemplate({
  contractTitle,
  clientName,
  totalValue,
  status,
}: ContractNotificationTemplateProps): string {
  const statusInfo = getStatusInfo(status);

  const content = `
    <h1>Contract Update Notification</h1>
    
    <p>We wanted to inform you about an important update to one of your contracts.</p>
    
    <div class="card">
        <h2 style="margin-top: 0; color: ${
          COLORS.navy
        };">📋 Contract Details</h2>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px 0; font-weight: 600; color: ${
                  COLORS.navy
                }; width: 30%;">Contract:</td>
                <td style="padding: 8px 0; color: ${
                  COLORS.darkGray
                };">${contractTitle}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Client:</td>
                <td style="padding: 8px 0; color: ${
                  COLORS.darkGray
                };">${clientName}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Total Value:</td>
                <td style="padding: 8px 0; color: ${
                  COLORS.darkGray
                }; font-weight: 600;">${formatCurrency(totalValue)}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Status:</td>
                <td style="padding: 8px 0;">
                    <span style="
                        display: inline-block;
                        padding: 4px 12px;
                        background-color: ${statusInfo.color}20;
                        color: ${statusInfo.color};
                        border-radius: 20px;
                        font-size: 14px;
                        font-weight: 600;
                        border: 1px solid ${statusInfo.color}40;
                    ">
                        ${statusInfo.emoji} ${statusInfo.label}
                    </span>
                </td>
            </tr>
        </table>
    </div>
    
    ${
      status.toLowerCase() === "active"
        ? `
    <div class="card" style="background-color: ${COLORS.neon}20; border-left: 4px solid ${COLORS.neon};">
        <h2 style="color: ${COLORS.navy}; margin-top: 0;">🎯 Contract Activated</h2>
        <p style="margin-bottom: 0;">
            Great news! Your contract is now active and work can begin. You can track progress and manage payments through your dashboard.
        </p>
    </div>
    `
        : ""
    }
    
    ${
      status.toLowerCase() === "completed"
        ? `
    <div class="card" style="background-color: #10b98120; border-left: 4px solid #10b981;">
        <h2 style="color: ${COLORS.navy}; margin-top: 0;">🎉 Contract Completed</h2>
        <p style="margin-bottom: 0;">
            Congratulations! This contract has been successfully completed. Thank you for your business and we look forward to working with you again.
        </p>
    </div>
    `
        : ""
    }
    
    ${
      status.toLowerCase() === "terminated"
        ? `
    <div class="card" style="background-color: #ef444420; border-left: 4px solid #ef4444;">
        <h2 style="color: #ef4444; margin-top: 0;">⚠️ Contract Terminated</h2>
        <p style="margin-bottom: 0;">
            This contract has been terminated. If you have any questions about this change, please contact our support team.
        </p>
    </div>
    `
        : ""
    }
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/contracts" class="button">
            View Contract Details
        </a>
    </div>
    
    <h2>What's Next?</h2>
    <p>Here are some actions you might want to take:</p>
    
    <ul style="margin: 16px 0; padding-left: 20px;">
        <li>Review the contract details in your dashboard</li>
        <li>Check payment status and upcoming milestones</li>
        <li>Contact your project manager if you have questions</li>
        <li>Update your project timeline if needed</li>
    </ul>
    
    <p>If you have any questions about this contract update, please don't hesitate to reach out to our team at <a href="mailto:<EMAIL>" style="color: ${
      COLORS.neon
    };"><EMAIL></a>.</p>
    
    <p style="margin-top: 30px;">
        Best regards,<br>
        <strong>The tenderbank International Team</strong>
    </p>
  `;

  return generateBaseTemplate({
    title: `Contract Update: ${contractTitle}`,
    preheader: `Your contract "${contractTitle}" status has been updated to ${statusInfo.label}.`,
    content,
  });
}
