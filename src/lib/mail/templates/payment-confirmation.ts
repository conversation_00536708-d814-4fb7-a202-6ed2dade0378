import {
  generateBaseTemplate,
  COLORS,
  formatCurrency,
  formatDate,
} from "./base";

export interface PaymentConfirmationTemplateProps {
  transactionId: string;
  amount: number;
  contractTitle: string;
  paymentDate: string;
}

/**
 * Generate payment confirmation email template
 */
export function generatePaymentConfirmationTemplate({
  transactionId,
  amount,
  contractTitle,
  paymentDate,
}: PaymentConfirmationTemplateProps): string {
  const content = `
    <h1>Payment Confirmation</h1>
    
    <div class="card" style="background-color: ${
      COLORS.neon
    }20; border-left: 4px solid ${COLORS.neon};">
        <h2 style="color: ${
          COLORS.navy
        }; margin-top: 0;">✅ Payment Successful</h2>
        <p style="margin-bottom: 0; font-size: 18px;">
            Your payment of <strong>${formatCurrency(
              amount
            )}</strong> has been processed successfully.
        </p>
    </div>
    
    <h2>Transaction Details</h2>
    
    <div class="card">
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: ${
                  COLORS.navy
                }; width: 35%;">Transaction ID:</td>
                <td style="padding: 12px 0; color: ${
                  COLORS.darkGray
                }; font-family: monospace; font-size: 14px;">${transactionId}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Amount:</td>
                <td style="padding: 12px 0; color: ${
                  COLORS.darkGray
                }; font-weight: 600; font-size: 18px;">${formatCurrency(
    amount
  )}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Contract:</td>
                <td style="padding: 12px 0; color: ${
                  COLORS.darkGray
                };">${contractTitle}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Payment Date:</td>
                <td style="padding: 12px 0; color: ${
                  COLORS.darkGray
                };">${formatDate(paymentDate)}</td>
            </tr>
            <tr>
                <td style="padding: 12px 0; font-weight: 600; color: ${
                  COLORS.navy
                };">Status:</td>
                <td style="padding: 12px 0;">
                    <span style="
                        display: inline-block;
                        padding: 4px 12px;
                        background-color: ${COLORS.neon}20;
                        color: ${COLORS.neon};
                        border-radius: 20px;
                        font-size: 14px;
                        font-weight: 600;
                        border: 1px solid ${COLORS.neon}40;
                    ">
                        ✅ Completed
                    </span>
                </td>
            </tr>
        </table>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="${
          process.env.NEXT_PUBLIC_APP_URL
        }/transactions/${transactionId}" class="button">
            View Transaction Details
        </a>
        <br>
        <a href="${
          process.env.NEXT_PUBLIC_APP_URL
        }/contracts" class="button-secondary" style="margin-top: 10px;">
            View Contract
        </a>
    </div>
    
    <h2>Important Information</h2>
    
    <div class="card" style="background-color: ${COLORS.navy}; color: ${
    COLORS.white
  }; border-left: 4px solid ${COLORS.neon};">
        <h3 style="color: ${
          COLORS.neon
        }; margin-top: 0;">📄 Receipt & Records</h3>
        <p style="color: ${COLORS.white};">
            This email serves as your payment receipt. Please keep it for your records. You can also download a detailed receipt from your dashboard.
        </p>
        <p style="color: ${COLORS.white}; margin-bottom: 0;">
            <strong>Transaction ID:</strong> ${transactionId}
        </p>
    </div>
    
    <h2>What Happens Next?</h2>
    
    <ul style="margin: 16px 0; padding-left: 20px;">
        <li>Your payment has been applied to the contract balance</li>
        <li>You'll receive project updates as work progresses</li>
        <li>Detailed invoices and receipts are available in your dashboard</li>
        <li>Our team will continue working on your project deliverables</li>
    </ul>
    
    <div class="card">
        <h3 style="margin-top: 0; color: ${COLORS.navy};">💳 Payment Method</h3>
        <p style="margin-bottom: 0;">
            This payment was processed securely through our Braintree payment system. Your payment information is encrypted and never stored on our servers.
        </p>
    </div>
    
    <h2>Need Help?</h2>
    <p>If you have any questions about this payment or need assistance, our support team is here to help:</p>
    
    <ul style="margin: 16px 0; padding-left: 20px;">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: ${
          COLORS.neon
        };"><EMAIL></a></li>
        <li><strong>Reference:</strong> Transaction ID ${transactionId}</li>
        <li><strong>Support Hours:</strong> 24/7 through our platform</li>
    </ul>
    
    <p>Thank you for your payment and for choosing tenderbank International for your software development needs.</p>
    
    <p style="margin-top: 30px;">
        Best regards,<br>
        <strong>The tenderbank International Team</strong>
    </p>
  `;

  return generateBaseTemplate({
    title: `Payment Confirmation - ${transactionId}`,
    preheader: `Payment of ${formatCurrency(
      amount
    )} processed successfully for ${contractTitle}.`,
    content,
  });
}
