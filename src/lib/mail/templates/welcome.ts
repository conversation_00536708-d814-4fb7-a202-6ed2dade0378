import { generateBaseTemplate, COLORS } from "./base";

export interface WelcomeTemplateProps {
  userName: string;
}

/**
 * Generate welcome email template
 */
export function generateWelcomeTemplate({
  userName,
}: WelcomeTemplateProps): string {
  const content = `
    <h1>Welcome to tenderbank International, ${userName}!</h1>
    
    <p>We're thrilled to have you join our platform. You now have access to our comprehensive suite of software development and consulting services.</p>
    
    <div class="card">
        <h2 style="margin-top: 0; color: ${COLORS.navy};">🚀 What's Next?</h2>
        <p style="margin-bottom: 0;">Here are some things you can do to get started:</p>
        <ul style="margin: 16px 0; padding-left: 20px;">
            <li>Complete your profile setup</li>
            <li>Explore our service offerings</li>
            <li>Browse available contracts and projects</li>
            <li>Set up your payment methods</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" class="button">
            Get Started
        </a>
    </div>
    
    <h2>Need Help?</h2>
    <p>Our team is here to support you every step of the way. If you have any questions or need assistance, don't hesitate to reach out:</p>
    
    <ul style="margin: 16px 0; padding-left: 20px;">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" style="color: ${COLORS.neon};"><EMAIL></a></li>
        <li><strong>Support:</strong> Available 24/7 through our platform</li>
        <li><strong>Documentation:</strong> <a href="${process.env.NEXT_PUBLIC_APP_URL}/docs" style="color: ${COLORS.neon};">Browse our guides</a></li>
    </ul>
    
    <div class="card" style="background-color: ${COLORS.navy}; color: ${COLORS.white}; border-left: 4px solid ${COLORS.neon};">
        <h2 style="color: ${COLORS.neon}; margin-top: 0;">💡 Pro Tip</h2>
        <p style="color: ${COLORS.white}; margin-bottom: 0;">
            Make sure to verify your email address and complete your profile to unlock all features and ensure smooth project collaboration.
        </p>
    </div>
    
    <p>Thank you for choosing tenderbank International. We look forward to working with you and helping you achieve your software development goals.</p>
    
    <p style="margin-top: 30px;">
        Best regards,<br>
        <strong>The tenderbank International Team</strong>
    </p>
  `;

  return generateBaseTemplate({
    title: "Welcome to tenderbank International",
    preheader: `Welcome ${userName}! Get started with your new account.`,
    content,
  });
}
