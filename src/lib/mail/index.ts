"use server";
/**
 * Mail service and templates
 *
 * This module provides email functionality for the tenderbank International platform
 * including nodemailer service and themed email templates.
 */

// ! I've Muted this section on appending "use server"; flag to escape export errors since server actions must be async exports.
// Export the main email service
// export { EmailService, emailService } from "./service";
// export type { EmailOptions } from "./service";

// // Export base template utilities
// export {
//   generateBaseTemplate,
//   COLORS,
//   formatCurrency,
//   formatDate,
// } from "./templates/base";
// export type { BaseTemplateProps } from "./templates/base";

// // Export individual templates
// export { generateWelcomeTemplate } from "./templates/welcome";
// export type { WelcomeTemplateProps } from "./templates/welcome";

// export { generateContractNotificationTemplate } from "./templates/contract-notification";
// export type { ContractNotificationTemplateProps } from "./templates/contract-notification";

// export { generatePaymentConfirmationTemplate } from "./templates/payment-confirmation";
// export type { PaymentConfirmationTemplateProps } from "./templates/payment-confirmation";

// export { generatePasswordResetTemplate } from "./templates/password-reset";
// export type { PasswordResetTemplateProps } from "./templates/password-reset";
// ! EOC

/**
 * Quick access functions for common email operations
 */

/**
 * Send a welcome email to a new user
 */
export async function sendWelcomeEmail(
  to: string,
  userName: string
): Promise<void> {
  const { emailService } = await import("./service");
  return emailService.sendWelcomeEmail(to, userName);
}

/**
 * Send a contract notification email
 */
export async function sendContractNotification(
  to: string,
  contractData: {
    contractTitle: string;
    clientName: string;
    totalValue: number;
    status: string;
  }
): Promise<void> {
  const { emailService } = await import("./service");
  return emailService.sendContractNotification(to, contractData);
}

/**
 * Send a payment confirmation email
 */
export async function sendPaymentConfirmation(
  to: string,
  paymentData: {
    transactionId: string;
    amount: number;
    contractTitle: string;
    paymentDate: string;
  }
): Promise<void> {
  const { emailService } = await import("./service");
  return emailService.sendPaymentConfirmation(to, paymentData);
}

/**
 * Send a password reset email
 */
export async function sendPasswordResetEmail(
  to: string,
  resetToken: string,
  userName: string
): Promise<void> {
  const { emailService } = await import("./service");
  return emailService.sendPasswordResetEmail(to, resetToken, userName);
}

/**
 * Verify email service connection
 */
export async function verifyEmailConnection(): Promise<boolean> {
  const { emailService } = await import("./service");
  return emailService.verifyConnection();
}
