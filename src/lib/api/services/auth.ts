import { BaseService, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import { CryptoMiddleware } from "@/lib/crypto/middleware";
import { DocumentService } from "./document";
import { getSignedURL } from "@/lib/common/s3";
import { BraintreeService } from "./braintree";

import { RoleService } from "./role";
import { PERMISSIONS } from "@/lib/permissions";

/**
 * Authentication service configuration
 */
export interface AuthServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
}

/**
 * Authentication service for handling login operations
 *
 * This service extends BaseService and provides:
 * 1. Email/password authentication with database validation
 * 2. Password matching against stored user credentials
 * 3. Standardized error handling and logging
 */
export class AuthService extends BaseService {
  constructor(config: AuthServiceConfig = {}) {
    super(config);
    this.setModel(prisma.user as any);
  }

  /**
   * Authenticate user with email and password
   * @param email - User email address
   * @param password - User password
   * @returns Service response with authentication result
   */
  async login(email: string, password: string): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting login for email: ${email}`);

      // Find user by email
      const user: any = await this.findUniqueRecord({
        where: { email },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          email: true,
          customerId: true,
          image: true,
          company: {
            select: {
              id: true,
              name: true,
              tin: true,
              category: true,
              email: true,
              address: true,
              phone: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          role: true,
          createdAt: true,
          updatedAt: true,
          accounts: {
            select: {
              id: true,
              password: true,
              payment_method: {
                select: {
                  id: true,
                  vault_id: true,
                  holder_name: true,
                  ref_number: true,
                  expiry_month: true,
                  expiry_year: true,
                  cvv: true,
                  type: true,
                  isDefault: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        this.log("warn", `User not found for email: ${email}`);
        return this.createErrorResponse(
          `User not found for email: ${email}`,
          401
        );
      }

      const storedPassword = user?.accounts[0]?.password;

      // Check if user has password in accounts
      if (!storedPassword) {
        this.log("warn", `No password found for user: ${email}`);
        return this.createErrorResponse(
          `No password found for user: ${email}`,
          401
        );
      }

      const [hashedPassword, salt] = storedPassword.split("==");

      const valid = await CryptoMiddleware.verifyPassword(
        password,
        hashedPassword,
        salt
      );

      // Match password
      if (!valid) {
        this.log("warn", `Password mismatch for user: ${email}`);
        return this.createErrorResponse(
          `Password mismatch for user: ${email}`,
          401
        );
      }

      // Get Avatar Signed URL
      let signedURL = await getSignedURL(user.image);
      user.image = signedURL;

      // Extract company and account from arrays
      if (user.company?.length > 0) user.company = user.company?.[0];
      if (user.accounts?.length > 0) user.account = user.accounts?.[0];
      if (user.account?.payment_method?.length > 0)
        user.payment_methods = user.account?.payment_method;

      delete user.accounts;
      delete user.account.password;
      delete user.account.payment_method;

      this.log("info", `Login successful for user: ${email}`);

      return this.createSuccessResponse(user, 200, "Login successful");
    } catch (error) {
      this.log("error", `Login error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during login",
        500
      );
    }
  }

  async register(
    firstName: string,
    lastName: string,
    phone: string,
    email: string,
    password: string
  ): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting registration for email: ${email}`);

      if (!firstName) {
        this.log("warn", `First name is required for registration`);
        return this.createErrorResponse("First name is required", 400);
      }

      if (!lastName) {
        this.log("warn", `Last name is required for registration`);
        return this.createErrorResponse("Last name is required", 400);
      }

      if (!phone) {
        this.log("warn", `Phone number is required for registration`);
        return this.createErrorResponse("Phone number is required", 400);
      }

      if (!email) {
        this.log("warn", `Email is required for registration`);
        return this.createErrorResponse("Email is required", 400);
      }

      if (!password) {
        this.log("warn", `Password is required for registration`);
        return this.createErrorResponse("Password is required", 400);
      }

      // Check if user already exists
      const existingUser = await this.findUniqueRecord({
        where: { email },
      });

      if (existingUser) {
        this.log("warn", `User already exists for email: ${email}`);
        return this.createErrorResponse("User already exists", 409);
      }

      // Password Encryption
      const { hashedPassword, salt } = await CryptoMiddleware.hashPassword(
        password
      );

      // Register client
      let gateway = new BraintreeService({
        requireAuth: false,
      });

      // Log Customer on registration
      let braintree = await gateway.createCustomer({
        firstName,
        lastName,
        email,
      });

      let response: ServiceResponse = await this.getRoleAssignment(email);

      const { role, error } = response.data;

      if (!role.id || error) {
        return this.createErrorResponse("Failed to create role", 500);
      }

      // Create user
      const user = await this.createRecord({
        data: {
          firstName,
          lastName,
          phone,
          email,
          customerId: braintree.data.id,
          role: { connect: { id: role.id } },
          accounts: {
            create: {
              password: hashedPassword + "==" + salt,
              type: "credentials",
              provider: "credentials",
              providerAccountId: email,
              refresh_token: null,
              access_token: null,
              expires_at: null,
              token_type: null,
              scope: null,
              id_token: null,
              session_state: null,
            },
          },
        },
      });

      this.log("info", `Registration successful for user: ${email}`);

      return this.createSuccessResponse(user, 201, "Registration successful");
    } catch (error: any) {
      this.log("error", `Registration error: ${error?.message}`);
      return this.createErrorResponse(error.message, 500);
    }
  }

  async updateAvatar(id: string, avatar: File): Promise<ServiceResponse> {
    try {
      let document = new DocumentService({
        requireAuth: false,
      });

      let uploadedFile = await document.uploadDocument(avatar, "avatars", {
        category: "avatar",
        association_entity: "user",
        association_id: id,
      });

      let user: any = await this.updateRecord({
        where: { id: id },
        data: { image: uploadedFile.data.path },
        select: { image: true },
      });

      let signedURL = await getSignedURL(user.image);
      user.image = signedURL;

      return this.createSuccessResponse(
        user,
        200,
        "Avatar updated successfully"
      );
    } catch (error: any) {
      return this.createErrorResponse(error.message, 500);
    }
  }

  async getRoleAssignment(email: string) {
    let permissions: any = {};

    switch (email) {
      case "<EMAIL>":
        permissions = PERMISSIONS.ADMIN;
        break;
      case "<EMAIL>":
        permissions = PERMISSIONS.MANAGER;
        break;
      default:
        permissions = PERMISSIONS.CLIENT;
        break;
    }

    // Create new role or inherit
    let role = new RoleService({
      requireAuth: false,
    });

    return await role.findOrCreateRole(permissions);
  }

  /**
   * Update user details (firstName, lastName, email, phone)
   * @param userId - User ID
   * @param userData - User data to update
   * @returns Service response with update result
   */
  async updateUser(
    userId: string,
    userData: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
    }
  ): Promise<ServiceResponse> {
    try {
      this.log("info", `Updating user details for user: ${userId}`);

      // Validate input data
      if (!userData || Object.keys(userData).length === 0) {
        return this.createErrorResponse("No data provided for update", 400);
      }

      // Check if user exists
      const existingUser: any = await this.findUniqueRecord({
        where: { id: userId },
        select: { id: true, email: true },
      });

      if (!existingUser) {
        return this.createErrorResponse("User not found", 404);
      }

      // If email is being updated, check if it's already taken by another user
      if (userData.email && userData.email !== existingUser.email) {
        const emailExists = await this.findUniqueRecord({
          where: {
            email: userData.email,
            NOT: { id: userId },
          },
          select: { id: true },
        });

        if (emailExists) {
          return this.createErrorResponse(
            "Email address is already in use",
            409
          );
        }
      }

      // Prepare update data - only include fields that are provided
      const updateData: any = {};
      if (userData.firstName !== undefined)
        updateData.firstName = userData.firstName;
      if (userData.lastName !== undefined)
        updateData.lastName = userData.lastName;
      if (userData.email !== undefined) updateData.email = userData.email;
      if (userData.phone !== undefined) updateData.phone = userData.phone;

      // Update user
      const updatedUser = await this.updateRecord({
        where: { id: userId },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          updatedAt: true,
        },
      });

      this.log("info", `User details updated successfully for user: ${userId}`);

      return this.createSuccessResponse(
        updatedUser,
        200,
        "User details updated successfully"
      );
    } catch (error: any) {
      this.log("error", `User update error: ${error?.message}`);
      return this.createErrorResponse(error.message, 500);
    }
  }

  /**
   * Update user password
   * @param userId - User ID
   * @param currentPassword - Current password for verification
   * @param newPassword - New password to set
   * @returns Service response with update result
   */
  async updatePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.log("info", `Updating password for user: ${userId}`);

      // Find user with account information
      const user: any = await this.findUniqueRecord({
        where: { id: userId },
        include: {
          accounts: {
            select: {
              id: true,
              password: true,
            },
          },
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      if (!user.accounts || user.accounts.length === 0) {
        throw new Error("User account not found");
      }

      const account = user.accounts[0];

      // Verify current password
      // Extract hash and salt from stored password (format: hashedPassword==salt)
      const [storedHashedPassword, storedSalt] = account.password.split("==");

      const isCurrentPasswordValid = await CryptoMiddleware.verifyPassword(
        currentPassword,
        storedHashedPassword,
        storedSalt
      );

      if (!isCurrentPasswordValid) {
        throw new Error("Current password is incorrect");
      }

      // Hash new password
      const { hashedPassword: hashedNewPassword, salt: newSalt } =
        await CryptoMiddleware.hashPassword(newPassword);

      // Combine hash and salt in the same format as registration
      const combinedPassword = `${hashedNewPassword}==${newSalt}`;

      // Update password in account table
      const originalModel = this.model;
      this.setModel(prisma.account as any);

      await this.updateRecord({
        where: { id: account.id },
        data: { password: combinedPassword },
      });

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }

      this.log("info", `Password updated successfully for user: ${userId}`);
      return { success: true, message: "Password updated successfully" };
    }, "updatePassword");
  }
}
