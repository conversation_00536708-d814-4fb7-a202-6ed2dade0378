import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import { IdSchema } from "@/lib/api/validators/schemas/common";
import {
  CreateRoleSchema,
  UpdateRoleSchema,
  RoleQuerySchema,
  UpdateRolePermissionsSchema,
  AddEntityPermissionSchema,
  RemoveEntityPermissionSchema,
  SetEntityPermissionsSchema,
  CheckEntityPermissionSchema,
  EntityPermissions,
} from "@/lib/api/validators/schemas/role";

/**
 * Role service configuration
 */
export interface RoleServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Role service for handling role-related operations
 *
 * This service extends BaseService and provides:
 * 1. CRUD operations for roles
 * 2. Permission management for roles
 * 3. Input/output validation using Zod schemas
 * 4. Authentication and authorization checks
 * 5. Standardized error handling and logging
 */
export class RoleService extends BaseService {
  private authRequired: boolean;

  constructor(config: RoleServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the model to the role model delegate
    this.setModel(prisma.role as any);
  }

  /**
   * Create a new role
   * @param data - Role creation data
   * @returns Service response with created role
   */
  async createRole(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(CreateRoleSchema, data);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Check if role name already exists for this account
      const existingRole = await this.findFirstRecord({
        where: {
          name: validatedData.name.trim(),
          accountId: account.id,
        },
      });

      if (existingRole) {
        throw new Error("Role with this name already exists");
      }

      // Prepare role data
      const roleData = {
        name: validatedData.name.trim(),
        description: validatedData.description?.trim() || null,
        status: validatedData.status || "created",
        permissions: validatedData.permissions || {},
        accountId: account.id,
      };

      // Prepare create options
      const options: DatabaseOptions = {
        data: roleData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      // Create the role using base CRUD operation
      const role = await this.createRecord(options);

      return role;
    }, "createRole");
  }

  /**
   * Update an existing role
   * @param data - Role update data
   * @returns Service response with updated role
   */
  async updateRole(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(UpdateRoleSchema, data);

      // Get current user's account for access control
      const account = await this.getCurrentUserAccount();

      // Build update data (only include provided fields)
      const updateData: any = {};
      if (validatedData.name !== undefined)
        updateData.name = validatedData.name.trim();
      if (validatedData.description !== undefined)
        updateData.description = validatedData.description?.trim() || null;
      if (validatedData.status !== undefined)
        updateData.status = validatedData.status;
      if (validatedData.permissions !== undefined)
        updateData.permissions = validatedData.permissions;

      // Check if new name conflicts with existing role (if name is being updated)
      if (updateData.name) {
        const existingRole = await this.findFirstRecord({
          where: {
            name: updateData.name,
            accountId: account.id,
            NOT: { id: validatedData.id },
          },
        });

        if (existingRole) {
          throw new Error("Role with this name already exists");
        }
      }

      // Prepare update options with account filtering
      const options: DatabaseOptions = {
        where: {
          id: validatedData.id,
          accountId: account.id,
        },
        data: updateData,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      // Update the role using base CRUD operation
      const updatedRole = await this.updateRecord(options);

      return updatedRole;
    }, "updateRole");
  }

  /**
   * Get roles with filtering and pagination
   * @param query - Query parameters
   * @returns Service response with roles
   */
  async getRoles(query: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate query parameters
      const validatedQuery: any = this.validateInput(
        RoleQuerySchema,
        query || {}
      );

      // Get current user's account for filtering
      const account = await this.getCurrentUserAccount();

      // Build where clause with account filtering
      const where: any = {
        accountId: account.id, // Only show roles for current user's account
      };
      if (validatedQuery.id) where.id = validatedQuery.id;
      if (validatedQuery.name)
        where.name = { contains: validatedQuery.name, mode: "insensitive" };
      if (validatedQuery.status) where.status = validatedQuery.status;
      if (validatedQuery.search) {
        where.OR = [
          { name: { contains: validatedQuery.search, mode: "insensitive" } },
          {
            description: {
              contains: validatedQuery.search,
              mode: "insensitive",
            },
          },
        ];
      }

      const options: DatabaseOptions = {
        where,
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
              company: {
                select: {
                  id: true,
                  name: true,
                  tin: true,
                  category: true,
                },
              },
            },
          },
        },
      };

      // Handle single role request
      if (validatedQuery.id) {
        const role = await this.findUniqueRecord({
          where: { id: validatedQuery.id },
          select: options.select,
        });

        if (!role) {
          throw new Error("Role not found");
        }

        return role;
      }

      // Handle pagination
      if (validatedQuery.limit) {
        options.take = parseInt(validatedQuery.limit);
      }

      if (validatedQuery.offset) {
        options.skip = parseInt(validatedQuery.offset);
      }

      // Handle sorting
      if (validatedQuery.sortBy) {
        options.orderBy = {
          [validatedQuery.sortBy]: validatedQuery.sortOrder || "desc",
        } as any;
      }

      // Handle multiple roles request
      const roles = await this.findManyRecords(options);
      const total = await this.countRecords({ where });

      return {
        roles,
        pagination: {
          total,
          limit: validatedQuery.limit
            ? parseInt(String(validatedQuery.limit))
            : roles.length,
          offset: validatedQuery.offset
            ? parseInt(String(validatedQuery.offset))
            : 0,
        },
      };
    }, "getRoles");
  }

  /**
   * Delete a role
   * @param roleId - ID of the role to delete
   * @returns Service response
   */
  async deleteRole(roleId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate role ID
      const validatedId = this.validateInput(IdSchema, roleId);

      // Get current user's account for access control
      const account = await this.getCurrentUserAccount();

      // Check if role exists and has users assigned
      const role = await this.findUniqueRecord({
        where: {
          id: validatedId,
          accountId: account.id,
        },
        select: {
          id: true,
          name: true,
          user: {
            select: { id: true },
          },
        },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Prevent deletion if role has users assigned
      if ((role as any).user && (role as any).user.length > 0) {
        throw new Error(
          "Cannot delete role with assigned users. Please reassign users first."
        );
      }

      // Delete the role using base CRUD operation with account filtering
      await this.deleteRecord({
        where: {
          id: validatedId,
          accountId: account.id,
        },
      });

      return { message: "Role deleted successfully" };
    }, "deleteRole");
  }

  /**
   * Update role permissions
   * @param data - Role permissions update data
   * @returns Service response with updated role
   */
  async updateRolePermissions(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(
        UpdateRolePermissionsSchema,
        data
      );

      // Update role permissions
      const updatedRole = await this.updateRecord({
        where: { id: validatedData.id },
        data: { permissions: validatedData.permissions },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedRole;
    }, "updateRolePermissions");
  }

  /**
   * Add permission to a specific entity for a role
   * @param data - Role ID, entity, and action to add
   * @returns Service response with updated role
   */
  async addEntityPermission(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(AddEntityPermissionSchema, data);

      // Get current role
      const role = await this.findUniqueRecord({
        where: { id: validatedData.roleId },
        select: { id: true, permissions: true },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Parse current permissions
      const currentPermissions = (role as any).permissions as EntityPermissions;

      // Add the new permission
      if (!currentPermissions[validatedData.entity]) {
        currentPermissions[validatedData.entity] = [];
      }

      if (
        !currentPermissions[validatedData.entity].includes(validatedData.action)
      ) {
        currentPermissions[validatedData.entity].push(validatedData.action);
      }

      // Update role permissions
      const updatedRole = await this.updateRecord({
        where: { id: validatedData.roleId },
        data: { permissions: currentPermissions },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedRole;
    }, "addEntityPermission");
  }

  /**
   * Remove permission from a specific entity for a role
   * @param data - Role ID, entity, and action to remove
   * @returns Service response with updated role
   */
  async removeEntityPermission(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        RemoveEntityPermissionSchema,
        data
      );

      // Get current role
      const role = await this.findUniqueRecord({
        where: { id: validatedData.roleId },
        select: { id: true, permissions: true },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Parse current permissions
      const currentPermissions = (role as any).permissions as EntityPermissions;

      // Remove the permission
      if (currentPermissions[validatedData.entity]) {
        currentPermissions[validatedData.entity] = currentPermissions[
          validatedData.entity
        ].filter((action) => action !== validatedData.action);

        // Remove entity if no permissions left
        if (currentPermissions[validatedData.entity].length === 0) {
          delete currentPermissions[validatedData.entity];
        }
      }

      // Update role permissions
      const updatedRole = await this.updateRecord({
        where: { id: validatedData.roleId },
        data: { permissions: currentPermissions },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedRole;
    }, "removeEntityPermission");
  }

  /**
   * Set all permissions for a specific entity for a role
   * @param data - Role ID, entity, and actions to set
   * @returns Service response with updated role
   */
  async setEntityPermissions(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        SetEntityPermissionsSchema,
        data
      );

      // Get current role
      const role = await this.findUniqueRecord({
        where: { id: validatedData.roleId },
        select: { id: true, permissions: true },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Parse current permissions
      const currentPermissions = (role as any).permissions as EntityPermissions;

      // Set the permissions for the entity
      if (validatedData.actions.length > 0) {
        currentPermissions[validatedData.entity] = [
          ...new Set(validatedData.actions),
        ]; // Remove duplicates
      } else {
        delete currentPermissions[validatedData.entity]; // Remove entity if no actions
      }

      // Update role permissions
      const updatedRole = await this.updateRecord({
        where: { id: validatedData.roleId },
        data: { permissions: currentPermissions },
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          permissions: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return updatedRole;
    }, "setEntityPermissions");
  }

  /**
   * Check if a role has a specific permission
   * @param data - Role ID, entity, and action to check
   * @returns Service response with boolean result
   */
  async hasEntityPermission(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(
        CheckEntityPermissionSchema,
        data
      );

      // Get role permissions
      const role = await this.findUniqueRecord({
        where: { id: validatedData.roleId },
        select: { id: true, permissions: true },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Check permission
      const permissions = (role as any).permissions as EntityPermissions;
      const hasPermission =
        permissions[validatedData.entity]?.includes(validatedData.action) ||
        false;

      return { hasPermission };
    }, "hasEntityPermission");
  }

  /**
   * Get all entities and their permissions for a role
   * @param roleId - Role ID to get permissions for
   * @returns Service response with permissions object
   */
  async getRolePermissions(roleId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Validate role ID
      const validatedId = this.validateInput(IdSchema, roleId);

      // Get role with permissions
      const role = await this.findUniqueRecord({
        where: { id: validatedId },
        select: {
          id: true,
          name: true,
          permissions: true,
        },
      });

      if (!role) {
        throw new Error("Role not found");
      }

      return {
        roleId: (role as any).id,
        roleName: (role as any).name,
        permissions: (role as any).permissions as EntityPermissions,
      };
    }, "getRolePermissions");
  }

  /**
   * Find or create a role by name
   * @param data - Role data for finding/creating
   * @returns Service response with role and creation status
   */
  async findOrCreateRole(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData: any = this.validateInput(CreateRoleSchema, data);

      // Prepare where clause for finding existing role
      const whereClause = {
        name: validatedData.name.trim(),
      };

      // Prepare data for creating new role if not found
      const createData = {
        name: validatedData.name.trim(),
        description: validatedData.description?.trim() || null,
        status: validatedData.status || "created",
        permissions: validatedData.permissions || {},
      };

      // Use the base service's findOrCreateRecord method
      const result = await this.findOrCreateRecord({
        where: whereClause,
        data: createData,
        select: { id: true },
      });

      return {
        role: result.record,
        created: result.created,
        message: result.created
          ? "Role created successfully"
          : "Existing role found",
      };
    }, "findOrCreateRole");
  }
}
