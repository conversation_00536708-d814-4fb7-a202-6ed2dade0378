import { BaseService } from "./base";
import {
  CreateContractSchema,
  ContractStatisticsSchema,
  type Contract,
  type CreateContract,
  type UpdateContract,
  type ContractStatistics,
} from "../validators/schemas/contract";
import { prisma } from "@/lib/common/prisma";
import { DatabaseOptions } from "./base";
import { TransactionService } from "./transaction";
import { TransactionType } from "@prisma/client";
import { DocumentService } from "./document";
import { CreateTransactionRequest } from "@/lib/api/validators/schemas/braintree";
import { PaginationOptions } from "@/lib/pagination";
import { notificationService } from "./notification";

export class ContractService extends BaseService {
  private authRequired: boolean;

  constructor(config: any = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the Prisma model for database operations
    this.setModel(prisma.contract as any);
  }

  /**
   * Search contracts with single query parameter
   * @param searchParams - Object containing search query and pagination
   */
  async searchContracts(searchParams: {
    query?: string;
    page?: number;
    limit?: number;
  }): Promise<any> {
    return this.executeOperation(
      async () => {
        // Require authentication if enabled
        if (this.authRequired) {
          this.requireAuth();
        }

        // Get current user's id and account
        const userId = this.getCurrentUserId();
        const account = await this.getCurrentUserAccount();

        // Build dynamic where conditions
        const whereConditions: any = {
          OR: [
            { accountId: account.id },
            { proposal: { account: { user: { id: userId } } } },
          ],
        };

        // Add search filters using OR logic for partial string matching
        if (searchParams.query && searchParams.query.trim()) {
          const searchQuery = searchParams.query.trim();

          // Create OR conditions for searching across multiple fields
          const searchConditions = [
            // Search in proposal name (contract title)
            {
              proposal: {
                name: {
                  contains: searchQuery,
                  mode: "insensitive",
                },
              },
            },
            // Search in client name
            {
              proposal: {
                account: {
                  user: {
                    name: {
                      contains: searchQuery,
                      mode: "insensitive",
                    },
                  },
                },
              },
            },
            // Search in client email
            {
              proposal: {
                account: {
                  user: {
                    email: {
                      contains: searchQuery,
                      mode: "insensitive",
                    },
                  },
                },
              },
            },
          ];

          // Add the search conditions to the existing whereConditions
          if (whereConditions.OR) {
            // If there are already OR conditions (from access control), combine them
            whereConditions.AND = [
              { OR: whereConditions.OR },
              { OR: searchConditions },
            ];
            delete whereConditions.OR;
          } else {
            // Add search conditions as additional OR clause
            whereConditions.AND = [whereConditions, { OR: searchConditions }];
          }
        }

        // Set pagination parameters
        const page = searchParams.page || 1;
        const limit = searchParams.limit || 10;
        const skip = (page - 1) * limit;

        // Build query options with same select structure as getContracts
        const options: DatabaseOptions = {
          where: whereConditions,
          select: {
            id: true,
            status: true,
            total_value: true,
            paid_value: true,
            room: { select: { id: true } },
            remaining_value: true,
            start_date: true,
            end_date: true,
            proposal: {
              select: {
                id: true,
                name: true,
                account: {
                  select: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
            createdAt: true,
            updatedAt: true,
          },
          skip,
          take: limit,
          orderBy: {
            createdAt: "desc",
          },
        };

        // Get contracts and total count
        const [contracts, totalCount] = await Promise.all([
          this.findManyRecords<Contract>(options),
          this.countRecords({ where: whereConditions }),
        ]);

        // Transform to match API schema (same as getContracts)
        const transformedContracts = contracts.map((contract: any) => ({
          id: contract.id,
          title: contract.proposal.name,
          description: contract.proposal.description,
          status: contract.status,
          total_value: contract.total_value || 0,
          paid_value: contract.paid_value || 0,
          remaining_value: contract.remaining_value || 0,
          hasRoom: contract.room.length > 0,
          room: contract.room[0] ?? { id: "" },
          start_date: contract.start_date,
          end_date: contract.end_date,
          client: contract.proposal.account.user,
          proposal: {
            id: contract.proposal.id,
            name: contract.proposal.name,
          },
          createdAt: contract.createdAt,
          updatedAt: contract.updatedAt,
        }));

        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limit);
        const hasNextPage = page < totalPages;
        const hasPreviousPage = page > 1;

        return {
          contracts: transformedContracts,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNextPage,
            hasPreviousPage,
          },
          searchQuery: searchParams.query, // Return search query for reference
        };
      },
      "searchContracts",
      true // Enable pagination in response
    );
  }

  /**
   * Get all contracts with pagination
   * @param paginationOptions - Pagination parameters (page, limit, cursor, orderBy)
   */
  async getContracts(paginationOptions: PaginationOptions = {}): Promise<any> {
    return this.executeOperation(
      async () => {
        // Require authentication if enabled
        if (this.authRequired) {
          this.requireAuth();
        }

        // Set pagination parameters
        this.handlePagination(paginationOptions);

        // Get current user's id
        const userId = this.getCurrentUserId();

        // Get current user's account
        const account = await this.getCurrentUserAccount();

        // Build base query options
        const baseOptions: DatabaseOptions = {
          where: {
            OR: [
              { accountId: account.id },
              { proposal: { account: { user: { id: userId } } } },
            ],
          },
          select: {
            id: true,
            status: true,
            total_value: true,
            paid_value: true,
            room: { select: { id: true } },
            remaining_value: true,
            start_date: true,
            end_date: true,
            proposal: {
              select: {
                id: true,
                name: true,
                account: {
                  select: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
            createdAt: true,
            updatedAt: true,
          },
        };

        // Apply pagination query options
        const paginationQuery = this.getPrismaOffsetQuery();
        const options: DatabaseOptions = {
          ...baseOptions,
          ...paginationQuery,
        };

        const contracts = await this.findManyRecords<Contract>(options);

        // Transform to match API schema
        const transformedContracts = contracts.map((contract: any) => ({
          id: contract.id,
          title: contract.proposal.name,
          description: contract.proposal.description,
          status: contract.status,
          total_value: contract.total_value || 0,
          paid_value: contract.paid_value || 0,
          remaining_value: contract.remaining_value || 0,
          hasRoom: contract.room.length > 0,
          room: contract.room[0] ?? { id: "" },
          start_date: contract.start_date,
          end_date: contract.end_date,
          client: contract.proposal.account.user, // This will need to be resolved to actual client name
          proposal: {
            id: contract.proposal.id,
            name: contract.proposal.name,
          },
          createdAt: contract.createdAt,
          updatedAt: contract.updatedAt,
        }));

        return transformedContracts;
      },
      "getContracts",
      true
    ); // Enable pagination in response
  }

  /**
   * Get contract by ID
   */
  async getContract(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Store original model
      const originalModel: any = this.model;

      // Get current user's id
      const userId = this.getCurrentUserId();
      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Fetch contract from database with access control
      const options: DatabaseOptions = {
        where: {
          id: id,
          OR: [
            { accountId: account.id },
            { proposal: { account: { user: { id: userId } } } },
          ],
        },
        select: {
          id: true,
          status: true,
          total_value: true,
          paid_value: true,
          remaining_value: true,
          room: { select: { id: true } },
          start_date: true,
          end_date: true,
          proposal: {
            select: {
              id: true,
              name: true,
              description: true,
              account: {
                select: {
                  user: { select: { id: true, name: true, email: true } },
                },
              },
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      };

      const contract: any = await this.findUniqueRecord(options);

      if (!contract) {
        throw new Error("Contract not found");
      }

      this.setModel(prisma.document as any);
      let attachments: any[] = await this.findManyRecords({
        where: {
          association_id: contract.id,
          association_entity: "contract",
        },
        select: {
          id: true,
          name: true,
          path: true,
          file_type: true,
          size: true,
        },
      });

      // Transform to match API schema
      const transformedContract = {
        id: contract.id,
        title: contract.proposal.name,
        description: contract.proposal.description,
        status: contract.status,
        total_value: contract.total_value || 0,
        paid_value: contract.paid_value || 0,
        remaining_value: contract.remaining_value || 0,
        hasRoom: contract.room.length > 0,
        room: contract.room[0] ?? { id: "" },
        start_date: contract.start_date,
        end_date: contract.end_date,
        client: contract.proposal.account.user, // This will need to be resolved to actual client name
        proposal: {
          id: contract.proposal.id,
          name: contract.proposal.name,
        },
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt,
        attachments: attachments,
      };

      // Reset Model Back to Original
      this.setModel(originalModel);

      return transformedContract;
    }, "getContract");
  }

  /**
   * Create a new contract
   */
  async createContract(contractData: CreateContract): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      let attachments: Record<string, any>[] = [];
      let attachment: any = undefined;
      let contract: Record<string, any> = {};

      Object.entries(contractData).forEach(
        ([key, value]: [string, any], index: number) => {
          if (key.startsWith("attachment-")) {
            let count: number = index + 1;
            attachments.push({ ["file-" + count]: value });
          } else if (key === "attachment") attachment = value;
          else contract[key] = value;
        }
      );

      this.log("info", "Creating new contract", { title: contract.title });

      // Validate input
      const validatedData = CreateContractSchema.parse(contract);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      const originalModel: any = this.model;

      // Get client from proposal
      this.setModel(prisma.proposal as any);
      let proposal: any = await this.findUniqueRecord({
        where: { id: validatedData.proposal_id },
        select: {
          account: {
            select: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      let { user: proposalClient } = proposal.account;

      if (proposalClient) {
        this.createErrorResponse("Proposal Client not found", 400);
      }

      // Reset back to original model
      this.setModel(originalModel);

      // Prepare contract data for database
      const contractDbData = {
        total_value: validatedData.total_value || 0,
        paid_value: validatedData.paid_value || 0,
        remaining_value: validatedData.remaining_value || 0,
        start_date: new Date(validatedData.start_date),
        end_date: new Date(validatedData.end_date),
        proposalId: validatedData.proposal_id || null,
        accountId: account.id,
        agreed_to_terms_and_conditions: false, // Default value
      };

      // Create contract in database
      const options: DatabaseOptions = {
        data: contractDbData,
        select: {
          id: true,
          status: true,
          total_value: true,
          paid_value: true,
          remaining_value: true,
          start_date: true,
          end_date: true,
          room: {
            select: {
              id: true,
            },
          },
          proposal: {
            select: {
              id: true,
              name: true,
              description: true,
              account: {
                select: {
                  user: {
                    select: { id: true, name: true, email: true },
                  },
                },
              },
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      };

      const newContract: any = await this.createRecord(options);

      const associations: any = {
        category: "contracts",
        association_entity: "contract",
        association_id: newContract.id,
      };

      if (attachment) {
        this.setModel(prisma.document as any);
        await this.updateRecord({
          where: { id: attachment.id },
          data: associations,
        }).finally(() => {
          this.setModel(originalModel);
        });
      }

      if (attachments && attachments?.length > 0) {
        // Set model to attachment for creating attachments
        let documentsWithAssociations = attachments.map((file: any) => ({
          ...file,
          ...associations,
        }));

        let document = new DocumentService({
          requireAuth: false,
        });

        document.bulkCreateDocuments(documentsWithAssociations);
      }

      // Transform to match API schema
      const transformedContract = {
        id: newContract.id,
        title: newContract.proposal.name,
        description: newContract.proposal.description,
        client: newContract.proposal.account.user,
        status: newContract.status,
        total_value: newContract.total_value || 0,
        paid_value: newContract.paid_value || 0,
        remaining_value: newContract.remaining_value || 0,
        hasRoom: newContract.room.length > 0,
        room: newContract.room[0] ?? { id: "" },
        start_date: newContract.start_date,
        end_date: newContract.end_date,
        proposal_id: newContract.proposal.id,
        createdAt: newContract.createdAt,
        updatedAt: newContract.updatedAt,
      };

      this.log("info", `Contract created successfully: ${newContract.id}`);

      // Send notifications about contract creation
      await this.createContractNotifications(
        newContract.id,
        newContract.proposal.name,
        { id: account.userId, name: account.user?.name },
        { id: proposalClient.id, name: proposalClient.name }
      );

      return transformedContract;
    }, "createContract");
  }

  /**
   * Update an existing contract
   */
  async updateContract(
    id: string,
    contractData: Partial<UpdateContract>
  ): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", `Updating contract: ${id}`, contractData);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Prepare update data
      const updateData: any = {};
      if (contractData.status) updateData.status = contractData.status;
      if (contractData.total_value !== undefined)
        updateData.total_value = contractData.total_value;
      if (contractData.paid_value !== undefined)
        updateData.paid_value = contractData.paid_value;
      if (contractData.remaining_value !== undefined)
        updateData.remaining_value = contractData.remaining_value;
      if (contractData.start_date)
        updateData.start_date = new Date(contractData.start_date);
      if (contractData.end_date)
        updateData.end_date = new Date(contractData.end_date);
      if (contractData.client) updateData.client = contractData.client;
      if (contractData.proposal_id !== undefined)
        updateData.proposalId = contractData.proposal_id;

      // Update contract in database with access control
      const options: DatabaseOptions = {
        where: {
          id: id,
          accountId: account.id,
        },
        data: updateData,
        select: {
          id: true,
          status: true,
          total_value: true,
          paid_value: true,
          remaining_value: true,
          start_date: true,
          end_date: true,
          proposal: {
            select: {
              id: true,
              name: true,
              description: true,
              account: {
                select: {
                  user: {
                    select: { id: true, name: true, email: true },
                  },
                },
              },
            },
          },
          createdAt: true,
          updatedAt: true,
        },
      };

      const updatedContract: any = await this.updateRecord(options);

      // Transform to match API schema
      const transformedContract = {
        id: updatedContract.id,
        title: updatedContract.proposal.name,
        description: updatedContract.proposal.description,
        client: updatedContract.proposal.account.user,
        status: updatedContract.status,
        total_value: updatedContract.total_value || 0,
        paid_value: updatedContract.paid_value || 0,
        remaining_value: updatedContract.remaining_value || 0,
        start_date: updatedContract.start_date,
        end_date: updatedContract.end_date,
        proposal_id: updatedContract.proposal.id,
        createdAt: updatedContract.createdAt,
        updatedAt: updatedContract.updatedAt,
      };

      this.log("info", `Contract updated successfully: ${id}`);

      // Send notifications about contract update
      const changes = Object.keys(contractData).filter(
        (key) => contractData[key as keyof UpdateContract] !== undefined
      );
      await this.createContractUpdateNotifications(
        updatedContract.id,
        updatedContract.proposal.name,
        { id: account.userId, name: account.user?.name },
        {
          id: updatedContract.proposal.account.user.id,
          name: updatedContract.proposal.account.user.name,
        },
        changes
      );

      return transformedContract;
    }, "updateContract");
  }

  /**
   * Delete a contract
   */
  async deleteContract(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", `Deleting contract: ${id}`);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Get contract data before deletion for notifications
      const contractToDelete: any = await this.findUniqueRecord({
        where: {
          id: id,
          accountId: account.id,
        },
        select: {
          id: true,
          proposal: {
            select: {
              name: true,
              account: {
                select: {
                  user: {
                    select: { id: true, name: true, email: true },
                  },
                },
              },
            },
          },
        },
      });

      if (!contractToDelete) {
        throw new Error("Contract not found or access denied");
      }

      // Delete contract from database with access control
      const options: DatabaseOptions = {
        where: {
          id: id,
          accountId: account.id,
        },
      };

      await this.deleteRecord(options);

      this.log("info", `Contract deleted successfully: ${id}`);

      // Send notifications about contract deletion
      await this.createContractDeleteNotifications(
        contractToDelete.id,
        contractToDelete.proposal.name,
        { id: account.userId, name: account.user?.name },
        {
          id: contractToDelete.proposal.account.user.id,
          name: contractToDelete.proposal.account.user.name,
        }
      );

      return { message: "Contract deleted successfully" };
    }, "deleteContract");
  }

  /**
   * Get contract statistics
   */
  async getContractStatistics(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Fetching contract statistics");

      // Get current user's account
      const userId = this.getCurrentUserId();
      const account = await this.getCurrentUserAccount();

      // Get all contracts for the user's account
      const contracts: any[] = await this.findManyRecords({
        where: {
          OR: [
            { accountId: account.id },
            { proposal: { account: { user: { id: userId } } } },
          ],
        },
        select: {
          status: true,
          total_value: true,
        },
      });

      // Calculate statistics from database contracts
      const statistics: ContractStatistics = {
        total: contracts.length,
        active: contracts.filter((c) => c.status === "active").length,
        completed: contracts.filter((c) => c.status === "completed").length,
        draft: contracts.filter((c) => c.status === "draft").length,
        terminated: contracts.filter((c) => c.status === "terminated").length,
        expired: contracts.filter((c) => c.status === "expired").length,
        totalValue: contracts.reduce((sum, c) => sum + (c.total_value || 0), 0),
        averageValue:
          contracts.length > 0
            ? contracts.reduce((sum, c) => sum + (c.total_value || 0), 0) /
              contracts.length
            : 0,
      };

      // Validate statistics
      const validatedStatistics = ContractStatisticsSchema.parse(statistics);

      this.log("info", "Contract statistics calculated", validatedStatistics);

      return validatedStatistics;
    }, "getContractStatistics");
  }

  /**
   * Process contract payment - deducts amount from remaining_value and adds to paid_value
   * @param contractId - The contract ID (used as orderId reference)
   * @param paymentAmount - The amount being paid
   * @returns Updated contract with payment processed
   */
  async processContractPayment(data: CreateTransactionRequest): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      let {
        orderId: contractId,
        amount: paymentAmount,
        payment_method_id,
        description,
      } = data ?? {};

      // Validate payment amount
      if (paymentAmount <= 0) {
        throw new Error("Payment amount must be greater than 0");
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Find the contract
      const contract: any = await this.findUniqueRecord({
        where: {
          id: contractId,
          OR: [{ accountId: account.id }, { client: this.getCurrentUserId() }],
        },
        select: {
          id: true,
          name: true,
          total_value: true,
          paid_value: true,
          remaining_value: true,
          status: true,
        },
      });

      if (!contract) {
        throw new Error("Contract not found or access denied");
      }

      // Check if payment amount exceeds remaining value
      const currentRemaining = contract.remaining_value || 0;
      if (paymentAmount > currentRemaining) {
        throw new Error(
          `Payment amount ($${paymentAmount}) exceeds remaining contract value ($${currentRemaining})`
        );
      }

      // Calculate new values
      const newPaidValue = (contract.paid_value || 0) + paymentAmount;
      const newRemainingValue = currentRemaining - paymentAmount;
      const totalValue = contract.total_value || 0;

      // Determine if contract is now fully paid
      const isFullyPaid = newRemainingValue === 0;
      const newStatus =
        isFullyPaid && contract.status === "active"
          ? "completed"
          : contract.status;

      // Update the contract
      const updatedContract: any = await this.updateRecord({
        where: { id: contractId },
        data: {
          paid_value: newPaidValue,
          remaining_value: newRemainingValue,
          status: newStatus,
        },
        select: {
          id: true,
          name: true,
          total_value: true,
          paid_value: true,
          remaining_value: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // Log the payment completion status
      const completionPercentage =
        totalValue > 0 ? (newPaidValue / totalValue) * 100 : 0;

      this.log("info", "Contract payment processed", {
        contractId,
        contractName: contract.name,
        paymentAmount,
        previousPaidValue: contract.paid_value || 0,
        newPaidValue,
        previousRemainingValue: currentRemaining,
        newRemainingValue,
        totalValue,
        completionPercentage: Math.round(completionPercentage * 100) / 100,
        isFullyPaid,
        statusChanged: contract.status !== newStatus,
        previousStatus: contract.status,
        newStatus,
      });

      if (isFullyPaid) {
        this.log("info", "Contract fully paid and completed", {
          contractId,
          contractName: contract.name,
          totalValue,
          finalPaidValue: newPaidValue,
        });
      }

      // Log transaction to database for audit trail
      let transactionLog = new TransactionService();

      let paymentType: TransactionType = isFullyPaid
        ? "full_payment"
        : "partial_payment";

      await transactionLog.createTransaction({
        type: paymentType,
        orderId: contractId,
        amount: paymentAmount,
        payment_method_id: payment_method_id,
        condition: "none",
        status: "completed",
        description: description,
        accountId: account.id,
      });

      return {
        id: updatedContract.id,
        name: updatedContract.name,
        total_value: updatedContract.total_value,
        paid_value: updatedContract.paid_value,
        remaining_value: updatedContract.remaining_value,
        status: updatedContract.status,
        payment_processed: {
          amount: paymentAmount,
          completion_percentage: completionPercentage,
          is_fully_paid: isFullyPaid,
          status_changed: contract.status !== newStatus,
        },
        createdAt: updatedContract.createdAt,
        updatedAt: updatedContract.updatedAt,
      };
    }, "processContractPayment");
  }

  /**
   * Create notifications for contract creation
   */
  private async createContractNotifications(
    contractId: string,
    contractTitle: string,
    creator: any,
    client: any
  ): Promise<void> {
    try {
      const notificationData = {
        title: `New contract created: ${contractTitle}`,
        message: `${creator.name || "Someone"} created a new contract`,
        type: "contract" as const,
        category: "inApp" as const,
        userId: client.id,
        data: {
          contractId: contractId,
          contractTitle: contractTitle,
          creatorId: creator.id,
          creatorName: creator.name,
          actionUrl: `/contracts/${contractId}`,
        },
        priority: "normal" as const,
        sourceId: contractId,
        sourceType: "contract",
      };

      // Use centralized notification service to create and send notification
      await notificationService.createAndSendNotification(notificationData);

      this.log(
        "info",
        `Created contract creation notification for: ${contractTitle}`
      );
    } catch (error) {
      this.log("error", `Failed to create contract notifications: ${error}`);
      // Don't throw error to avoid breaking the main contract creation flow
    }
  }

  /**
   * Create notifications for contract updates
   */
  private async createContractUpdateNotifications(
    contractId: string,
    contractTitle: string,
    updater: any,
    client: any,
    changes: string[]
  ): Promise<void> {
    try {
      const changesText = changes.length > 0 ? ` (${changes.join(", ")})` : "";

      const notificationData = {
        title: `Contract updated: ${contractTitle}`,
        message: `${
          updater.name || "Someone"
        } updated the contract${changesText}`,
        type: "contract" as const,
        category: "inApp" as const,
        userId: client.id,
        data: {
          contractId: contractId,
          contractTitle: contractTitle,
          updaterId: updater.id,
          updaterName: updater.name,
          changes: changes,
          actionUrl: `/contracts/${contractId}`,
        },
        priority: "normal" as const,
        sourceId: contractId,
        sourceType: "contract",
      };

      // Use centralized notification service to create and send notification
      await notificationService.createAndSendNotification(notificationData);

      this.log(
        "info",
        `Created contract update notification for: ${contractTitle}`
      );
    } catch (error) {
      this.log(
        "error",
        `Failed to create contract update notifications: ${error}`
      );
      // Don't throw error to avoid breaking the main contract update flow
    }
  }

  /**
   * Create notifications for contract deletion
   */
  private async createContractDeleteNotifications(
    contractId: string,
    contractTitle: string,
    deleter: any,
    client: any
  ): Promise<void> {
    try {
      const notificationData = {
        title: `Contract deleted: ${contractTitle}`,
        message: `${deleter.name || "Someone"} deleted the contract`,
        type: "contract" as const,
        category: "inApp" as const,
        userId: client.id,
        data: {
          contractId: contractId,
          contractTitle: contractTitle,
          deleterId: deleter.id,
          deleterName: deleter.name,
          actionUrl: `/contracts`,
        },
        priority: "high" as const,
        sourceId: contractId,
        sourceType: "contract",
      };

      // Use centralized notification service to create and send notification
      await notificationService.createAndSendNotification(notificationData);

      this.log(
        "info",
        `Created contract deletion notification for: ${contractTitle}`
      );
    } catch (error) {
      this.log(
        "error",
        `Failed to create contract deletion notifications: ${error}`
      );
      // Don't throw error to avoid breaking the main contract deletion flow
    }
  }
}
