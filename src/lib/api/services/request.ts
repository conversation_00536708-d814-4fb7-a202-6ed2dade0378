import { BaseService, ServiceResponse, ServiceContext } from "./base";
import { prisma } from "@/lib/common/prisma";
import { NotificationService } from "./notification";
import {
  CreateRequestSchema,
  UpdateRequestSchema,
  RequestQuerySchema,
  BulkRequestSchema,
  type CreateRequest,
  type UpdateRequest,
  type RequestQuery,
  type BulkRequest,
  type Request,
} from "@/lib/api/validators/schemas/request";

/**
 * Request Service
 *
 * Handles all request-related operations including CRUD operations,
 * search, statistics, and bulk actions with notification integration.
 */
export class RequestService extends BaseService {
  private notificationService: NotificationService;

  constructor() {
    super({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });

    // Set the Prisma model for request operations
    this.setModel(prisma.request as any);

    // Initialize notification service
    this.notificationService = new NotificationService();
  }

  /**
   * Initialize the service with context
   */
  async initialize(context: ServiceContext): Promise<void> {
    this.setContext(context);
    await this.notificationService.initialize(context);
  }

  /**
   * Create a new request with notification
   */
  async createRequest(data: CreateRequest): Promise<ServiceResponse<Request>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(CreateRequestSchema, data);

      // Check for existing request with same tender_id and userId
      const existingRequest = await this.findUniqueRecord({
        where: {
          tender_id_userId: {
            tender_id: validatedData.tender_id,
            userId: validatedData.userId,
          },
        },
      });

      if (existingRequest) {
        throw new Error("Request already exists for this tender and user");
      }

      // Create the request
      const request = await this.createRecord<Request>({
        data: validatedData,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Create notification for request creation
      await this.createNotificationForRequest(request, "created");

      return request;
    }, "createRequest");
  }

  /**
   * Get request by ID
   */
  async getRequest(id: string): Promise<ServiceResponse<Request | null>> {
    return this.executeOperation(async () => {
      const request = await this.findUniqueRecord<Request>({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return request;
    }, "getRequest");
  }

  /**
   * Get requests with filtering and pagination
   */
  async getRequests(
    query: RequestQuery = {}
  ): Promise<ServiceResponse<Request[]>> {
    return this.executeOperation(
      async () => {
        const validatedQuery = this.validateInput(RequestQuerySchema, query);

        // Set pagination
        this.goToPage(validatedQuery.page);
        super.limit = validatedQuery.limit;

        // Build where clause
        const where: any = {};

        if (validatedQuery.status) {
          where.status = validatedQuery.status;
        }

        if (validatedQuery.userId) {
          where.userId = validatedQuery.userId;
        }

        if (validatedQuery.tender_id) {
          where.tender_id = validatedQuery.tender_id;
        }

        if (validatedQuery.search) {
          where.OR = [
            {
              message: { contains: validatedQuery.search, mode: "insensitive" },
            },
            {
              tender_id: {
                contains: validatedQuery.search,
                mode: "insensitive",
              },
            },
          ];
        }

        // Build orderBy clause
        const orderBy: any = {};
        orderBy[validatedQuery.sortBy] = validatedQuery.sortOrder;

        const requests = await this.findManyRecords<Request>({
          where,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy,
          take: this.limit,
          skip: this.offset,
        });

        return requests;
      },
      "getRequests",
      true
    );
  }

  /**
   * Update request
   */
  async updateRequest(data: UpdateRequest): Promise<ServiceResponse<Request>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(UpdateRequestSchema, data);
      const { id, ...updateData } = validatedData;

      const request = await this.updateRecord<Request>({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Create notification for request update
      await this.createNotificationForRequest(request, "updated");

      return request;
    }, "updateRequest");
  }

  /**
   * Delete request
   */
  async deleteRequest(id: string): Promise<ServiceResponse<Request>> {
    return this.executeOperation(async () => {
      const request = await this.deleteRecord<Request>({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Create notification for request deletion
      await this.createNotificationForRequest(request, "deleted");

      return request;
    }, "deleteRequest");
  }

  /**
   * Perform bulk operations on requests
   */
  async bulkAction(data: BulkRequest): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const validatedData = this.validateInput(BulkRequestSchema, data);
      const { action, requestIds, data: actionData } = validatedData;

      switch (action) {
        case "delete":
          const deleteResult = await this.deleteManyRecords({
            where: { id: { in: requestIds } },
          });
          return { deleted: deleteResult.count };

        case "updateStatus":
          if (!actionData?.status) {
            throw new Error("Status is required for bulk status update");
          }

          const updateResult = await this.executeQuery("updateMany", {
            where: { id: { in: requestIds } },
            data: { status: actionData.status },
          });
          return { updated: updateResult.count };

        case "export":
          const requests = await this.findManyRecords({
            where: { id: { in: requestIds } },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          });
          return { requests, count: requests.length };

        default:
          throw new Error(`Unsupported bulk action: ${action}`);
      }
    }, "bulkAction");
  }

  /**
   * Get request statistics
   */
  async getRequestStats(): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      const [
        total,
        active,
        inactive,
        submitted,
        completed,
        statusCounts,
        userCounts,
      ] = await Promise.all([
        this.countRecords(),
        this.countRecords({ where: { status: "active" } }),
        this.countRecords({ where: { status: "inactive" } }),
        this.countRecords({ where: { status: "submitted" } }),
        this.countRecords({ where: { status: "completed" } }),
        this.executeRawQuery`
          SELECT status, COUNT(*)::int as count 
          FROM "request" 
          GROUP BY status
        `,
        this.executeRawQuery`
          SELECT "userId", COUNT(*)::int as count 
          FROM "request" 
          GROUP BY "userId"
        `,
      ]);

      const byStatus = statusCounts.reduce((acc: any, item: any) => {
        acc[item.status] = item.count;
        return acc;
      }, {});

      const byUser = userCounts.reduce((acc: any, item: any) => {
        acc[item.userId] = item.count;
        return acc;
      }, {});

      return {
        total,
        active,
        inactive,
        submitted,
        completed,
        byStatus,
        byUser,
        recentActivity: [], // Could be implemented with date-based queries
      };
    }, "getRequestStats");
  }

  /**
   * Create notification for request operations
   */
  private async createNotificationForRequest(
    request: Request,
    action: "created" | "updated" | "deleted"
  ): Promise<void> {
    try {
      const actionMessages = {
        created: "New request created",
        updated: "Request updated",
        deleted: "Request deleted",
      };

      await this.notificationService.createAndSendNotification({
        title: actionMessages[action],
        message: `Request for tender ${request.tender_id} has been ${action}`,
        type: "systemAlerts",
        category: "inApp",
        userId: request.userId,
        priority: "normal",
        data: {
          requestId: request.id,
          tenderId: request.tender_id,
          action,
        },
      });
    } catch (error) {
      this.log("error", "Failed to create notification for request", { error });
      // Don't throw error to avoid breaking the main operation
    }
  }
}
