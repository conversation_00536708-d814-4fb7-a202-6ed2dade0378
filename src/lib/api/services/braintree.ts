import { prisma } from "@/lib/common/prisma";
import { EmailService } from "@/lib/mail/service";
import { BaseService, ServiceResponse } from "./base";

// Braintree SDK imports
import braintree from "braintree";

// Import validation schemas
import {
  CreateTransactionSchema,
  CreateCustomerSchema,
  CreatePaymentMethodSchema,
  VoidTransactionSchema,
  RefundTransactionSchema,
  type CreateTransactionRequest,
  type CreateCustomerRequest,
  type CreatePaymentMethodRequest,
  type VoidTransactionRequest,
  type RefundTransactionRequest,
} from "@/lib/api/validators/schemas/braintree";
import { TransactionService } from "./transaction";
import { ContractService } from "./contract";

/**
 * Braintree service configuration
 */
export interface BraintreeServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
  environment?: "sandbox" | "production";
}

/**
 * Braintree environment configuration
 */
const BRAINTREE_CONFIG = {
  // Environment (sandbox or production)
  ENVIRONMENT:
    (process.env.BRAINTREE_ENVIRONMENT as "sandbox" | "production") ||
    "sandbox",

  // Merchant credentials
  MERCHANT_ID: process.env.BRAINTREE_MERCHANT_ID || "",
  PUBLIC_KEY: process.env.BRAINTREE_PUBLIC_KEY || "",
  PRIVATE_KEY: process.env.BRAINTREE_PRIVATE_KEY || "",

  // Webhook credentials
  WEBHOOK_DESTINATION_URL: process.env.BRAINTREE_WEBHOOK_URL || "",
};

// Schemas and types are now imported from validators

/**
 * Braintree service for handling payment processing and authentication
 *
 * This service extends BaseService and provides:
 * 1. Braintree SDK initialization and configuration
 * 2. Transaction processing (sale, void, refund)
 * 3. Customer management
 * 4. Payment method management
 * 5. Webhook handling
 * 6. Authentication and authorization
 */
export class BraintreeService extends BaseService {
  private gateway: braintree.BraintreeGateway;
  private authRequired: boolean;

  constructor(config: BraintreeServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;

    // Initialize Braintree gateway
    this.gateway = this.initializeBraintreeGateway(config.environment);

    // Set the Prisma model for transaction logging
    this.setModel(prisma.user as any);
  }

  /**
   * Initialize Braintree gateway with environment configuration
   * @param environment - Braintree environment (sandbox or production)
   * @returns Configured Braintree gateway
   */
  private initializeBraintreeGateway(
    environment?: "sandbox" | "production"
  ): braintree.BraintreeGateway {
    const env = environment || BRAINTREE_CONFIG.ENVIRONMENT;

    // Validate required configuration
    if (
      !BRAINTREE_CONFIG.MERCHANT_ID ||
      !BRAINTREE_CONFIG.PUBLIC_KEY ||
      !BRAINTREE_CONFIG.PRIVATE_KEY
    ) {
      throw new Error(
        "Braintree credentials are not properly configured. Please check environment variables."
      );
    }

    const gateway = new braintree.BraintreeGateway({
      environment:
        env === "production"
          ? braintree.Environment.Production
          : braintree.Environment.Sandbox,
      merchantId: BRAINTREE_CONFIG.MERCHANT_ID,
      publicKey: BRAINTREE_CONFIG.PUBLIC_KEY,
      privateKey: BRAINTREE_CONFIG.PRIVATE_KEY,
    });

    this.log("info", `Braintree gateway initialized for ${env} environment`);
    return gateway;
  }

  /**
   * Get Braintree client token for frontend initialization
   * @param customerId - Optional customer ID for existing customers
   * @returns Client token for frontend SDK
   */
  async getClientToken(
    customerId?: string
  ): Promise<ServiceResponse<{ clientToken: string }>> {
    return this.executeOperation(async () => {
      this.log("info", "Generating Braintree client token", { customerId });

      const request: any = {};
      if (customerId) {
        request.customerId = customerId;
      }

      let user: any = await this.getCurrentUser();

      if (user) {
        request.customerId = user.customerId;
      }

      const response = await this.gateway.clientToken.generate(request);

      if (!response.success) {
        throw new Error(`Failed to generate client token: ${response.message}`);
      }

      return {
        clientToken: response.clientToken,
      };
    }, "getClientToken");
  }

  /**
   * Create a new customer in Braintree
   * @param customerData - Customer information
   * @returns Created customer data
   */
  async createCustomer(
    customerData: CreateCustomerRequest
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        CreateCustomerSchema,
        customerData
      );

      this.log("info", "Creating Braintree customer", {
        email: validatedData.email,
      });

      const response = await this.gateway.customer.create({
        id: validatedData.id,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        email: validatedData.email,
        phone: validatedData.phone,
        company: validatedData.company,
      });

      if (!response.success) {
        throw new Error(`Failed to create customer: ${response.message}`);
      }

      this.log("info", "Braintree customer created successfully", {
        customerId: response.customer.id,
      });

      return response.customer;
    }, "createCustomer");
  }

  /**
   * Create a payment method for a customer
   * @param paymentMethodData - Payment method information
   * @returns Created payment method data
   */
  async createPaymentMethod(
    paymentMethodData: CreatePaymentMethodRequest
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        CreatePaymentMethodSchema,
        paymentMethodData
      );

      this.log("info", "Creating Braintree payment method", {
        customerId: validatedData.customerId,
      });

      const response = await this.gateway.paymentMethod.create({
        customerId: validatedData.customerId,
        paymentMethodNonce: validatedData.paymentMethodNonce,
        options: {
          makeDefault: validatedData.makeDefault,
          verifyCard: true,
        },
      });

      if (!response.success) {
        throw new Error(`Failed to create payment method: ${response.message}`);
      }

      this.log("info", "Braintree payment method created successfully", {
        token: response.paymentMethod.token,
      });

      return response.paymentMethod;
    }, "createPaymentMethod");
  }

  /**
   * Create a payment method nonce from a payment method token
   * @param paymentMethodToken - The payment method token string
   * @returns Payment method nonce data
   */
  async createPaymentMethodNonce(
    paymentMethodToken: string
  ): Promise<ServiceResponse<{ nonce: string; details?: any }>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input
      if (!paymentMethodToken || typeof paymentMethodToken !== "string") {
        throw new Error(
          "Payment method token is required and must be a string"
        );
      }

      this.log("info", "Creating payment method nonce from token", {
        tokenLength: paymentMethodToken.length,
      });

      try {
        // Create nonce from payment method token using the Braintree API
        // Note: In Braintree, payment method nonces are typically created client-side
        // For server-side creation from a token, we use the paymentMethodNonce.create method
        const response = await (this.gateway as any).paymentMethodNonce.create(
          paymentMethodToken
        );

        if (!response.success) {
          const errorMessage =
            response.message || "Failed to create payment method nonce";
          this.log("error", "Payment method nonce creation failed", {
            error: errorMessage,
            processorResponseCode: response.processorResponseCode,
            processorResponseText: response.processorResponseText,
          });
          throw new Error(errorMessage);
        }

        this.log("info", "Payment method nonce created successfully", {
          nonceLength: response.paymentMethodNonce.nonce?.length || 0,
          type: response.paymentMethodNonce.type,
        });

        return {
          nonce: response.paymentMethodNonce.nonce,
          details: {
            type: response.paymentMethodNonce.type,
            description: response.paymentMethodNonce.description,
            consumed: response.paymentMethodNonce.consumed,
            threeDSecureInfo: response.paymentMethodNonce.threeDSecureInfo,
            binData: response.paymentMethodNonce.binData,
          },
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        this.log("error", "Failed to create payment method nonce", {
          error: errorMessage,
          paymentMethodToken: paymentMethodToken.substring(0, 10) + "...", // Log partial token for debugging
        });
        throw new Error(
          `Failed to create payment method nonce: ${errorMessage}`
        );
      }
    }, "createPaymentMethodNonce");
  }

  /**
   * Process a transaction (sale) to the company account
   * @param transactionData - Transaction information
   * @returns Transaction result
   */
  async createTransaction(
    transactionData: CreateTransactionRequest
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        CreateTransactionSchema,
        transactionData
      );

      this.log("info", "Processing Braintree transaction", {
        amount: validatedData.amount,
        customerId: validatedData.customerId,
      });

      if (!validatedData.orderId) {
        this.createErrorResponse("Order ID is required", 400);
      }

      const transactionRequest: any = {
        amount: validatedData.amount.toString(),
        options: {
          submitForSettlement: true,
        },
      };

      // Add customer ID
      let user = await this.getCurrentUser();
      if (user) {
        transactionRequest.customerId = user.customerId;
        transactionRequest.options = {
          ...transactionRequest.options,
          storeInVaultOnSuccess: true,
        };
      }

      // Add order ID if provided
      if (validatedData.orderId) {
        transactionRequest.orderId = validatedData.orderId;
      }

      // Add description if provided
      if (validatedData.description) {
        transactionRequest.descriptor = {
          name: validatedData.description.substring(0, 22), // Braintree limit
        };
      }

      // Add payment method nonce if provided
      if (validatedData.paymentMethodNonce) {
        transactionRequest.paymentMethodNonce =
          validatedData.paymentMethodNonce;
      } else if (validatedData.payment_method_id) {
        // Backward compatibility: if payment_method_id is provided, use it as payment method token
        transactionRequest.paymentMethodToken = validatedData.payment_method_id;
      } else {
        throw new Error(
          "Either paymentMethodNonce or payment_method_id is required"
        );
      }

      const response = await this.gateway.transaction.sale(transactionRequest);

      if (!response.success) {
        const errorMessage = response.message || "Transaction failed";
        this.log("error", "Braintree transaction failed", {
          error: errorMessage,
          processorResponseCode: response.transaction?.processorResponseCode,
          processorResponseText: response.transaction?.processorResponseText,
        });
        throw new Error(errorMessage);
      }

      // Transaction successful
      this.log("info", "Braintree transaction completed successfully", {
        transactionId: response.transaction.id,
        status: response.transaction.status,
      });

      // Process contract payment if orderId is provided (contract reference)
      const contractService = new ContractService();

      const contractPaymentResult =
        await contractService.processContractPayment(
          validatedData // Using orderId as contractId
        );

      this.log("info", "Contract payment processed successfully", {
        contractId: validatedData.orderId,
        paymentResult: contractPaymentResult.payment_processed,
      });

      // Log transaction to database for audit trail
      await this.logTransactionToDatabase(response.transaction, validatedData);

      // Get contract details
      this.setModel(prisma.contract as any);
      let contract: { id: string; name: string } | any =
        await this.findUniqueRecord({
          where: { id: validatedData.orderId },
          select: { name: true },
        });

      if (!contract) {
        this.createErrorResponse("Contract not found", 404);
      }

      // Send an Notification Email
      const emailService = new EmailService();
      await emailService.sendPaymentConfirmation(user.email, {
        transactionId: response.transaction.id,
        amount: validatedData.amount,
        contractTitle: contract.name,
        paymentDate: new Date().toISOString(),
      });

      // Original Contract
      this.setModel(prisma.user as any);

      return response.transaction;
    }, "createTransaction");
  }

  /**
   * Void a transaction
   * @param voidData - Transaction void information
   * @returns Void result
   */
  async voidTransaction(
    voidData: VoidTransactionRequest
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(VoidTransactionSchema, voidData);

      this.log("info", "Voiding Braintree transaction", {
        transactionId: validatedData.transactionId,
      });

      const response = await this.gateway.transaction.void(
        validatedData.transactionId
      );

      if (!response.success) {
        throw new Error(`Failed to void transaction: ${response.message}`);
      }

      this.log("info", "Braintree transaction voided successfully", {
        transactionId: response.transaction.id,
        status: response.transaction.status,
      });

      return response.transaction;
    }, "voidTransaction");
  }

  /**
   * Refund a transaction
   * @param refundData - Transaction refund information
   * @returns Refund result
   */
  async refundTransaction(
    refundData: RefundTransactionRequest
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        RefundTransactionSchema,
        refundData
      );

      this.log("info", "Refunding Braintree transaction", {
        transactionId: validatedData.transactionId,
        amount: validatedData.amount,
      });

      const refundRequest: any = {};
      if (validatedData.amount) {
        refundRequest.amount = validatedData.amount.toString();
      }

      const response = await this.gateway.transaction.refund(
        validatedData.transactionId,
        refundRequest.amount
      );

      if (!response.success) {
        throw new Error(`Failed to refund transaction: ${response.message}`);
      }

      this.log("info", "Braintree transaction refunded successfully", {
        transactionId: response.transaction.id,
        status: response.transaction.status,
      });

      return response.transaction;
    }, "refundTransaction");
  }

  /**
   * Find a transaction by ID
   * @param transactionId - Transaction ID
   * @returns Transaction data
   */
  async findTransaction(transactionId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Finding Braintree transaction", { transactionId });

      const transaction = await this.gateway.transaction.find(transactionId);

      if (!transaction) {
        throw new Error(`Transaction not found: ${transactionId}`);
      }

      return transaction;
    }, "findTransaction");
  }

  /**
   * Search transactions using company merchant account
   * @param searchParams - Search parameters (excluding merchantAccountId)
   * @returns Array of transactions
   */
  async searchTransactions(
    searchParams: {
      customerId?: string;
      status?: string;
      amount?: { min?: number; max?: number };
      createdAt?: { min?: Date; max?: Date };
      orderId?: string;
      paymentMethodToken?: string;
    } = {}
  ): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Searching Braintree transactions", {
        searchParams: {
          ...searchParams,
          merchantAccountId: BRAINTREE_CONFIG.COMPANY_MERCHANT_ACCOUNT_ID,
        },
      });

      // Build search criteria with company merchant account
      const searchCriteria: any = {
        // Always use company merchant account to avoid authorization errors
        merchantAccountId: {
          is: BRAINTREE_CONFIG.COMPANY_MERCHANT_ACCOUNT_ID,
        },
      };

      // Add optional search parameters
      if (searchParams.customerId) {
        searchCriteria.customerId = { is: searchParams.customerId };
      }

      if (searchParams.status) {
        searchCriteria.status = { is: searchParams.status };
      }

      if (searchParams.amount) {
        searchCriteria.amount = {};
        if (searchParams.amount.min) {
          searchCriteria.amount.min = searchParams.amount.min;
        }
        if (searchParams.amount.max) {
          searchCriteria.amount.max = searchParams.amount.max;
        }
      }

      if (searchParams.createdAt) {
        searchCriteria.createdAt = {};
        if (searchParams.createdAt.min) {
          searchCriteria.createdAt.min = searchParams.createdAt.min;
        }
        if (searchParams.createdAt.max) {
          searchCriteria.createdAt.max = searchParams.createdAt.max;
        }
      }

      if (searchParams.orderId) {
        searchCriteria.orderId = { is: searchParams.orderId };
      }

      if (searchParams.paymentMethodToken) {
        searchCriteria.paymentMethodToken = {
          is: searchParams.paymentMethodToken,
        };
      }

      // For now, we'll use findTransaction for individual lookups
      // In a full implementation, you would use the Braintree search API
      // This is a simplified version that demonstrates the concept
      const transactions: any[] = [];

      // If searching by specific criteria, we can implement targeted searches
      // For this example, we'll return an empty array and log the search intent
      this.log(
        "info",
        "Transaction search requested with company merchant account",
        {
          merchantAccountId: BRAINTREE_CONFIG.COMPANY_MERCHANT_ACCOUNT_ID,
          searchCriteria: searchParams,
        }
      );

      this.log("info", "Transaction search completed", {
        count: transactions.length,
      });

      return transactions;
    }, "searchTransactions");
  }

  /**
   * Find a customer by ID
   * @param customerId - Customer ID
   * @returns Customer data
   */
  async findCustomer(customerId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Finding Braintree customer", { customerId });

      const response = await this.gateway.customer.find(customerId);

      if (!response) {
        throw new Error(`Customer not found: ${customerId}`);
      }

      return response;
    }, "findCustomer");
  }

  /**
   * Get or create a customer for the current authenticated user
   * @returns Customer data
   */
  async getOrCreateCustomerForCurrentUser(): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication
      this.requireAuth();

      const currentUser = await this.getCurrentUserAccount();
      const braintreeCustomerId = `user_${currentUser.id}`;

      try {
        // Try to find existing customer
        const existingCustomer = await this.gateway.customer.find(
          braintreeCustomerId
        );

        if (existingCustomer) {
          this.log("info", "Found existing Braintree customer", {
            customerId: braintreeCustomerId,
          });
          return existingCustomer;
        }
      } catch (error) {
        // Customer doesn't exist, create new one
        this.log("info", "Creating new Braintree customer for user", {
          userId: currentUser.id,
        });
      }

      // Create new customer
      const customerData: CreateCustomerRequest = {
        id: braintreeCustomerId,
        firstName: currentUser.user?.name?.split(" ")[0] || "User",
        lastName: currentUser.user?.name?.split(" ").slice(1).join(" ") || "",
        email: currentUser.user?.email || "",
      };

      const createResponse = await this.createCustomer(customerData);

      if (!createResponse.success) {
        throw new Error("Failed to create customer for current user");
      }

      return createResponse.data;
    }, "getOrCreateCustomerForCurrentUser");
  }

  /**
   * Process webhook notifications from Braintree
   * @param webhookNotification - Webhook notification data
   * @returns Processed webhook result
   */
  async processWebhook(
    webhookNotification: any
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.log("info", "Processing Braintree webhook", {
        kind: webhookNotification.kind,
      });

      // Parse webhook notification
      const notification = await this.gateway.webhookNotification.parse(
        webhookNotification.bt_signature,
        webhookNotification.bt_payload
      );

      // Handle different webhook types
      switch (notification.kind) {
        case "transaction_settled":
          await this.handleTransactionSettled(notification.transaction);
          break;
        case "transaction_settlement_declined":
          await this.handleTransactionSettlementDeclined(
            notification.transaction
          );
          break;
        case "transaction_disbursed":
          await this.handleTransactionDisbursed(notification.transaction);
          break;
        default:
          this.log("info", "Unhandled webhook type", {
            kind: notification.kind,
          });
      }

      return notification;
    }, "processWebhook");
  }

  /**
   * Verify webhook signature
   * @param signature - Webhook signature
   * @param payload - Webhook payload
   * @returns Verification result
   */
  async verifyWebhook(
    signature: string,
    payload: string
  ): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      this.log("info", "Verifying Braintree webhook signature");

      const notification = await this.gateway.webhookNotification.parse(
        signature,
        payload
      );

      return notification;
    }, "verifyWebhook");
  }

  /**
   * Log transaction to database for audit trail
   * @param transaction - Braintree transaction object
   * @param originalRequest - Original transaction request data
   */
  private async logTransactionToDatabase(
    transaction: any,
    originalRequest: CreateTransactionRequest
  ): Promise<void> {
    try {
      // This would typically log to a transactions table
      // For now, we'll log to the user's account or a separate audit log
      this.log("info", "Logging transaction to database", {
        transactionId: transaction.id,
        amount: transaction.amount,
        status: transaction.status,
        customerId: originalRequest.customerId,
      });

      // You can implement actual database logging here
      // Example: await prisma.transaction.create({ data: { ... } });
    } catch (error) {
      this.log("error", "Failed to log transaction to database", { error });
      // Don't throw error here as it shouldn't fail the main transaction
    }
  }

  /**
   * Handle transaction settled webhook
   * @param transaction - Transaction object from webhook
   */
  private async handleTransactionSettled(transaction: any): Promise<void> {
    this.log("info", "Handling transaction settled webhook", {
      transactionId: transaction.id,
    });

    // Update transaction status in database
    // Trigger any post-settlement actions
  }

  /**
   * Handle transaction settlement declined webhook
   * @param transaction - Transaction object from webhook
   */
  private async handleTransactionSettlementDeclined(
    transaction: any
  ): Promise<void> {
    this.log("error", "Handling transaction settlement declined webhook", {
      transactionId: transaction.id,
    });

    // Update transaction status in database
    // Notify relevant parties
    // Trigger refund or retry logic
  }

  /**
   * Handle transaction disbursed webhook
   * @param transaction - Transaction object from webhook
   */
  private async handleTransactionDisbursed(transaction: any): Promise<void> {
    this.log("info", "Handling transaction disbursed webhook", {
      transactionId: transaction.id,
    });

    // Update transaction status in database
    // Trigger any post-disbursement actions
  }

  /**
   * Get gateway instance for advanced operations
   * @returns Braintree gateway instance
   */
  getGateway(): braintree.BraintreeGateway {
    return this.gateway;
  }

  /**
   * Test connection to Braintree
   * @returns Connection test result
   */
  async testConnection(): Promise<
    ServiceResponse<{ connected: boolean; environment: string }>
  > {
    return this.executeOperation(async () => {
      this.log("info", "Testing Braintree connection");

      // Generate a client token to test connection
      const response = await this.gateway.clientToken.generate({});

      if (!response.success) {
        throw new Error("Failed to connect to Braintree");
      }

      return {
        connected: true,
        environment: BRAINTREE_CONFIG.ENVIRONMENT,
      };
    }, "testConnection");
  }
}
