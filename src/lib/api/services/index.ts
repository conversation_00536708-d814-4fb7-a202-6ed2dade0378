// Base service exports
export * from "./base";

// Service implementations
export * from "./proposal";
export * from "./document";
export * from "./contract";
export * from "./client";
export * from "./company";
export * from "./role";
export * from "./rbac";
export * from "./braintree";
export * from "./transaction";
export * from "./request";
export * from "./subscription";
export * from "./tenders/nest";

// Re-export existing services for backward compatibility
// Note: Data services are located in ../validators/data/
// export * from "../validators/data";

// Service types and interfaces
export type {
  BaseServiceConfig,
  ServiceResponse,
  ValidationError,
  ServiceContext,
} from "./base";

// Convenience imports for common service patterns
import { BaseService } from "./base";

/**
 * Create a service instance with context
 * @param ServiceClass - Service class to instantiate
 * @param context - Service context
 * @param config - Service configuration
 * @returns Service instance with context set
 */
export function createServiceWithContext<T extends BaseService>(
  ServiceClass: new (config?: any) => T,
  context: any,
  config?: any
): T {
  const service = new ServiceClass(config);
  service.setContext(context);
  return service;
}

/**
 * Service factory for creating configured service instances
 */
export class ServiceFactory {
  /**
   * Create a service with default configuration
   * @param ServiceClass - Service class to instantiate
   * @param context - Optional service context
   * @returns Configured service instance
   */
  static create<T extends BaseService>(
    ServiceClass: new (config?: any) => T,
    context?: any
  ): T {
    const service = new ServiceClass({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });

    if (context) {
      service.setContext(context);
    }

    return service;
  }

  /**
   * Create a service with strict configuration
   * @param ServiceClass - Service class to instantiate
   * @param context - Optional service context
   * @returns Strictly configured service instance
   */
  static createStrict<T extends BaseService>(
    ServiceClass: new (config?: any) => T,
    context?: any
  ): T {
    const service = new ServiceClass({
      enableLogging: true,
      throwOnError: true,
      validateInput: true,
      validateOutput: true,
    });

    if (context) {
      service.setContext(context);
    }

    return service;
  }

  /**
   * Create a service with minimal configuration for performance
   * @param ServiceClass - Service class to instantiate
   * @param context - Optional service context
   * @returns Performance-optimized service instance
   */
  static createMinimal<T extends BaseService>(
    ServiceClass: new (config?: any) => T,
    context?: any
  ): T {
    const service = new ServiceClass({
      enableLogging: false,
      throwOnError: false,
      validateInput: false,
      validateOutput: false,
    });

    if (context) {
      service.setContext(context);
    }

    return service;
  }
}
