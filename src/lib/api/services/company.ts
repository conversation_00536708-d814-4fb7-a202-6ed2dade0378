import { z } from "zod";
import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  IdSchema,
  RequiredStringSchema,
} from "@/lib/api/validators/schemas/common";

/**
 * Company service configuration
 */
export interface CompanyServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Company creation schema
 */
export const CreateCompanySchema = z.object({
  name: RequiredStringSchema,
  tin: RequiredStringSchema,
  category: z.string().optional(),
  email: z.string().email().optional().or(z.literal("")),
  address: z.string().optional(),
  phone: z.string().optional(),
});

/**
 * Company update schema
 */
export const UpdateCompanySchema = z.object({
  name: z.string().min(1).optional(),
  tin: z.string().min(1).optional(),
  category: z.string().optional(),
  email: z.string().email().optional().or(z.literal("")),
  address: z.string().optional(),
  phone: z.string().optional(),
});

/**
 * Company upsert schema (for API requests)
 */
export const UpsertCompanySchema = z.object({
  name: RequiredStringSchema,
  tin: RequiredStringSchema,
  category: z.string().optional(),
  email: z.string().email().optional().or(z.literal("")),
  address: z.string().optional(),
  phone: z.string().optional(),
});

/**
 * Company query schema
 */
export const CompanyQuerySchema = z.object({
  id: IdSchema.optional(),
  name: z.string().optional(),
  tin: z.string().optional(),
  category: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
});

/**
 * Company service for handling company operations
 *
 * This service extends BaseService and provides:
 * 1. Company CRUD operations (create, read, update, delete)
 * 2. Company upsert operations (create or update)
 * 3. Company search and filtering
 * 4. Input/output validation using Zod schemas
 * 5. Authentication and authorization checks
 * 6. Standardized error handling and logging
 */
export class CompanyService extends BaseService {
  private authRequired: boolean;

  constructor(config: CompanyServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    this.setModel(prisma.company as any);
  }

  /**
   * Create a new company
   * @param data - Company creation data
   * @returns Service response with created company
   */
  async create(data: z.infer<typeof CreateCompanySchema>): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(CreateCompanySchema, data);

      // Check if company with same name or TIN already exists
      const existingCompany = await this.findFirstRecord({
        where: {
          OR: [
            { name: validatedData.name },
            { tin: validatedData.tin },
          ],
        },
      });

      if (existingCompany) {
        throw new Error("Company with this name or TIN already exists");
      }

      // Create the company
      const company = await this.createRecord({
        data: {
          ...validatedData,
          email: validatedData.email || null,
          category: validatedData.category || null,
          address: validatedData.address || null,
          phone: validatedData.phone || null,
        },
      });

      return company;
    }, "createCompany");
  }

  /**
   * Get company by ID
   * @param id - Company ID
   * @returns Service response with company data
   */
  async getById(id: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth();
      }

      const company = await this.findUniqueRecord({
        where: { id },
      });

      if (!company) {
        throw new Error("Company not found");
      }

      return company;
    }, "getCompanyById");
  }

  /**
   * Get current user's company
   * @returns Service response with company data
   */
  async getCurrentUserCompany(): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      this.requireAuth();

      const currentUser = await this.getCurrentUser();
      
      if (!currentUser) {
        throw new Error("User not found");
      }

      // Get user's company through the user relation
      const originalModel = this.model;
      this.setModel(prisma.user as any);

      const userWithCompany = await this.findUniqueRecord({
        where: { id: currentUser.id },
        include: {
          company: true,
        },
      });

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }

      if (!userWithCompany?.company) {
        return null; // User doesn't have a company yet
      }

      return userWithCompany.company;
    }, "getCurrentUserCompany");
  }

  /**
   * Update company by ID
   * @param id - Company ID
   * @param data - Company update data
   * @returns Service response with updated company
   */
  async update(id: string, data: z.infer<typeof UpdateCompanySchema>): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(UpdateCompanySchema, data);

      // Check if company exists
      const existingCompany = await this.findUniqueRecord({
        where: { id },
      });

      if (!existingCompany) {
        throw new Error("Company not found");
      }

      // Check for duplicate name or TIN if they're being updated
      if (validatedData.name || validatedData.tin) {
        const duplicateCheck = await this.findFirstRecord({
          where: {
            AND: [
              { id: { not: id } }, // Exclude current company
              {
                OR: [
                  ...(validatedData.name ? [{ name: validatedData.name }] : []),
                  ...(validatedData.tin ? [{ tin: validatedData.tin }] : []),
                ],
              },
            ],
          },
        });

        if (duplicateCheck) {
          throw new Error("Company with this name or TIN already exists");
        }
      }

      // Update the company
      const updatedCompany = await this.updateRecord({
        where: { id },
        data: {
          ...validatedData,
          email: validatedData.email || null,
          category: validatedData.category || null,
          address: validatedData.address || null,
          phone: validatedData.phone || null,
        },
      });

      return updatedCompany;
    }, "updateCompany");
  }

  /**
   * Upsert company (create or update)
   * @param data - Company upsert data
   * @returns Service response with upserted company
   */
  async upsert(data: z.infer<typeof UpsertCompanySchema>): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(UpsertCompanySchema, data);

      const currentUser = await this.getCurrentUser();
      
      if (!currentUser) {
        throw new Error("User not found");
      }

      // Check if user already has a company
      const originalModel = this.model;
      this.setModel(prisma.user as any);

      const userWithCompany = await this.findUniqueRecord({
        where: { id: currentUser.id },
        include: {
          company: true,
        },
      });

      // Restore original model
      if (originalModel) {
        this.setModel(originalModel);
      }

      if (userWithCompany?.company) {
        // Update existing company
        return this.update(userWithCompany.company.id, validatedData);
      } else {
        // Create new company and associate with user
        const company = await this.createRecord({
          data: {
            ...validatedData,
            email: validatedData.email || null,
            category: validatedData.category || null,
            address: validatedData.address || null,
            phone: validatedData.phone || null,
            user: {
              connect: { id: currentUser.id },
            },
          },
        });

        return company;
      }
    }, "upsertCompany");
  }

  /**
   * Delete company by ID
   * @param id - Company ID
   * @returns Service response with deletion result
   */
  async delete(id: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth();
      }

      // Check if company exists
      const existingCompany = await this.findUniqueRecord({
        where: { id },
      });

      if (!existingCompany) {
        throw new Error("Company not found");
      }

      // Delete the company
      const deletedCompany = await this.deleteRecord({
        where: { id },
      });

      return { id: deletedCompany.id };
    }, "deleteCompany");
  }

  /**
   * Get all companies with optional filtering and pagination
   * @param params - Query parameters
   * @returns Service response with companies list
   */
  async getAll(params: z.infer<typeof CompanyQuerySchema> = {}): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      if (this.authRequired) {
        this.requireAuth(["admin", "super_admin"]); // Only admins can see all companies
      }

      // Validate query parameters
      const validatedParams = this.validateInput(CompanyQuerySchema, params);

      // Set pagination
      if (validatedParams.page) this.setPage(validatedParams.page);
      if (validatedParams.limit) this.setLimit(validatedParams.limit);

      // Build where clause
      const where: any = {};

      if (validatedParams.name) {
        where.name = { contains: validatedParams.name, mode: "insensitive" };
      }

      if (validatedParams.tin) {
        where.tin = { contains: validatedParams.tin, mode: "insensitive" };
      }

      if (validatedParams.category) {
        where.category = validatedParams.category;
      }

      if (validatedParams.search) {
        where.OR = [
          { name: { contains: validatedParams.search, mode: "insensitive" } },
          { tin: { contains: validatedParams.search, mode: "insensitive" } },
          { email: { contains: validatedParams.search, mode: "insensitive" } },
          { category: { contains: validatedParams.search, mode: "insensitive" } },
        ];
      }

      // Get companies with pagination
      const companies = await this.findManyRecords({
        where,
        orderBy: { createdAt: "desc" },
        take: this.limit,
        skip: this.offset,
      });

      return companies;
    }, "getAllCompanies", true);
  }
}
