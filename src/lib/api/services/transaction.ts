import { BaseService, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  CreateTransactionSchema,
  UpdateTransactionSchema,
  TransactionQuerySchema,
  BulkUpdateTransactionSchema,
  type CreateTransaction,
  type UpdateTransaction,
  type TransactionQuery,
  type BulkUpdateTransaction,
  type TransactionWithRelations,
  type TransactionStats,
  type TransactionType,
  type TransactionStatus,
} from "@/lib/api/validators/schemas/transactions";

// Enums are now imported from validators

/**
 * Transaction service configuration
 */
export interface TransactionServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

// Schemas and types are now imported from validators

/**
 * Transaction service for handling transaction CRUD operations
 *
 * This service extends BaseService and provides:
 * 1. Complete CRUD operations for transactions
 * 2. Advanced querying with filtering, sorting, and pagination
 * 3. Bulk operations for efficiency
 * 4. Transaction statistics and analytics
 * 5. Payment method integration
 * 6. Account-based access control
 * 7. Audit logging and validation
 */
export class TransactionService extends BaseService {
  private authRequired: boolean;

  constructor(config: TransactionServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;

    // Set the Prisma model for transaction operations
    this.setModel(prisma.transaction as any);
  }

  /**
   * Create a new transaction
   * @param transactionData - Transaction creation data
   * @returns Created transaction with relations
   */
  async createTransaction(
    transactionData: CreateTransaction
  ): Promise<ServiceResponse<TransactionWithRelations>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        CreateTransactionSchema,
        transactionData
      );

      this.log("info", "Creating transaction", {
        type: validatedData.type,
        amount: validatedData.amount,
        accountId: validatedData.accountId,
      });

      // Verify account exists and user has access
      await this.verifyAccountAccess(validatedData.accountId);

      // Verify payment method exists and belongs to account (if provided)
      if (validatedData.payment_method_id) {
        await this.verifyPaymentMethodAccess(
          validatedData.payment_method_id,
          validatedData.accountId
        );
      }

      // Create transaction with relations
      const transaction = await this.createRecord<TransactionWithRelations>({
        data: {
          type: validatedData.type,
          amount: validatedData.amount,
          payment_method_id: validatedData.payment_method_id,
          condition: validatedData.condition,
          status: validatedData.status,
          description: validatedData.description,
          accountId: validatedData.accountId,
        },
        include: {
          payment_method: {
            select: {
              id: true,
              holder_name: true,
              ref_number: true,
              type: true,
              isDefault: true,
            },
          },
          account: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
        },
      });

      this.log("info", "Transaction created successfully", {
        transactionId: transaction.id,
        amount: transaction.amount,
      });

      return transaction;
    }, "createTransaction");
  }

  /**
   * Get transaction by ID
   * @param transactionId - Transaction ID
   * @param includeRelations - Whether to include payment method and account relations
   * @returns Transaction with optional relations
   */
  async getTransactionById(
    transactionId: string,
    includeRelations: boolean = false
  ): Promise<ServiceResponse<TransactionWithRelations | null>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Getting transaction by ID", { transactionId });

      const include = includeRelations
        ? {
            payment_method: {
              select: {
                id: true,
                holder_name: true,
                ref_number: true,
                type: true,
                isDefault: true,
              },
            },
            account: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          }
        : undefined;

      const transaction = await this.findUniqueRecord<TransactionWithRelations>(
        {
          where: { id: transactionId },
          include,
        }
      );

      if (transaction && this.authRequired) {
        // Verify user has access to this transaction's account
        await this.verifyAccountAccess(transaction.accountId);
      }

      return transaction;
    }, "getTransactionById");
  }

  /**
   * Query transactions with advanced filtering, sorting, and pagination
   * @param queryParams - Query parameters for filtering and pagination
   * @returns Paginated transactions with metadata
   */
  async queryTransactions(queryParams: TransactionQuery): Promise<
    ServiceResponse<{
      transactions: TransactionWithRelations[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
      };
    }>
  > {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedQuery = this.validateInput(
        TransactionQuerySchema,
        queryParams
      );

      this.log("info", "Querying transactions", {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        filters: {
          accountId: validatedQuery.accountId,
          type: validatedQuery.type,
          status: validatedQuery.status,
        },
      });

      // Build where clause
      const where: any = {};

      if (validatedQuery.id) where.id = validatedQuery.id;
      if (validatedQuery.accountId) where.accountId = validatedQuery.accountId;
      if (validatedQuery.payment_method_id)
        where.payment_method_id = validatedQuery.payment_method_id;
      if (validatedQuery.type) where.type = validatedQuery.type;
      if (validatedQuery.status) where.status = validatedQuery.status;

      // Amount range filtering
      if (validatedQuery.minAmount || validatedQuery.maxAmount) {
        where.amount = {};
        if (validatedQuery.minAmount)
          where.amount.gte = validatedQuery.minAmount;
        if (validatedQuery.maxAmount)
          where.amount.lte = validatedQuery.maxAmount;
      }

      // Date range filtering
      if (validatedQuery.dateFrom || validatedQuery.dateTo) {
        where.createdAt = {};
        if (validatedQuery.dateFrom)
          where.createdAt.gte = new Date(validatedQuery.dateFrom);
        if (validatedQuery.dateTo)
          where.createdAt.lte = new Date(validatedQuery.dateTo);
      }

      // If authentication is required and no specific accountId is provided,
      // filter by current user's accessible accounts
      if (this.authRequired && !validatedQuery.accountId) {
        const currentUserId = this.getCurrentUserId();
        if (currentUserId) {
          where.account = {
            userId: currentUserId,
          };
        }
      }

      // Build include clause
      const include =
        validatedQuery.includePaymentMethod || validatedQuery.includeAccount
          ? {
              ...(validatedQuery.includePaymentMethod && {
                payment_method: {
                  select: {
                    id: true,
                    holder_name: true,
                    ref_number: true,
                    type: true,
                    isDefault: true,
                  },
                },
              }),
              ...(validatedQuery.includeAccount && {
                account: {
                  select: {
                    id: true,
                    email: true,
                    name: true,
                  },
                },
              }),
            }
          : undefined;

      // Calculate pagination
      const skip = (validatedQuery.page - 1) * validatedQuery.limit;

      // Get total count for pagination
      const total = await this.countRecords({ where });

      // Get transactions
      const transactions = await this.findManyRecords<TransactionWithRelations>(
        {
          where,
          include,
          orderBy: { [validatedQuery.sortBy]: validatedQuery.sortOrder },
          skip,
          take: validatedQuery.limit,
        }
      );

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / validatedQuery.limit);
      const hasNext = validatedQuery.page < totalPages;
      const hasPrev = validatedQuery.page > 1;

      this.log("info", "Transactions queried successfully", {
        count: transactions.length,
        total,
        page: validatedQuery.page,
      });

      return {
        transactions,
        pagination: {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
          total,
          totalPages,
          hasNext,
          hasPrev,
        },
      };
    }, "queryTransactions");
  }

  /**
   * Update transaction by ID
   * @param transactionId - Transaction ID
   * @param updateData - Transaction update data
   * @returns Updated transaction with relations
   */
  async updateTransaction(
    transactionId: string,
    updateData: UpdateTransaction
  ): Promise<ServiceResponse<TransactionWithRelations>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        UpdateTransactionSchema,
        updateData
      );

      this.log("info", "Updating transaction", { transactionId });

      // First, get the existing transaction to verify access
      const existingTransaction = await this.findUniqueRecord<any>({
        where: { id: transactionId },
        select: { id: true, accountId: true },
      });

      if (!existingTransaction) {
        throw new Error("Transaction not found");
      }

      // Verify user has access to this transaction's account
      if (this.authRequired) {
        await this.verifyAccountAccess(existingTransaction.accountId);
      }

      // Verify payment method exists and belongs to account (if being updated)
      if (validatedData.payment_method_id) {
        await this.verifyPaymentMethodAccess(
          validatedData.payment_method_id,
          existingTransaction.accountId
        );
      }

      // Update transaction with relations
      const transaction = await this.updateRecord<TransactionWithRelations>({
        where: { id: transactionId },
        data: {
          ...(validatedData.type && { type: validatedData.type }),
          ...(validatedData.amount && { amount: validatedData.amount }),
          ...(validatedData.payment_method_id !== undefined && {
            payment_method_id: validatedData.payment_method_id,
          }),
          ...(validatedData.condition !== undefined && {
            condition: validatedData.condition,
          }),
          ...(validatedData.status && { status: validatedData.status }),
          ...(validatedData.description !== undefined && {
            description: validatedData.description,
          }),
          updatedAt: new Date(),
        },
        include: {
          payment_method: {
            select: {
              id: true,
              holder_name: true,
              ref_number: true,
              type: true,
              isDefault: true,
            },
          },
          account: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
        },
      });

      this.log("info", "Transaction updated successfully", {
        transactionId: transaction.id,
      });

      return transaction;
    }, "updateTransaction");
  }

  /**
   * Delete transaction by ID
   * @param transactionId - Transaction ID
   * @returns Deleted transaction
   */
  async deleteTransaction(
    transactionId: string
  ): Promise<ServiceResponse<TransactionWithRelations>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Deleting transaction", { transactionId });

      // First, get the existing transaction to verify access
      const existingTransaction = await this.findUniqueRecord<any>({
        where: { id: transactionId },
        select: { id: true, accountId: true },
      });

      if (!existingTransaction) {
        throw new Error("Transaction not found");
      }

      // Verify user has access to this transaction's account
      if (this.authRequired) {
        await this.verifyAccountAccess(existingTransaction.accountId);
      }

      // Delete transaction
      const transaction = await this.deleteRecord<TransactionWithRelations>({
        where: { id: transactionId },
        include: {
          payment_method: {
            select: {
              id: true,
              holder_name: true,
              ref_number: true,
              type: true,
              isDefault: true,
            },
          },
          account: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
        },
      });

      this.log("info", "Transaction deleted successfully", {
        transactionId: transaction.id,
      });

      return transaction;
    }, "deleteTransaction");
  }

  /**
   * Bulk update multiple transactions
   * @param bulkUpdateData - Bulk update data with transaction IDs and updates
   * @returns Updated transactions count
   */
  async bulkUpdateTransactions(
    bulkUpdateData: BulkUpdateTransaction
  ): Promise<ServiceResponse<{ count: number; updatedIds: string[] }>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(
        BulkUpdateTransactionSchema,
        bulkUpdateData
      );

      this.log("info", "Bulk updating transactions", {
        count: validatedData.transactionIds.length,
      });

      // Verify user has access to all transactions
      if (this.authRequired) {
        const transactions = await this.findManyRecords<any>({
          where: { id: { in: validatedData.transactionIds } },
          select: { id: true, accountId: true },
        });

        for (const transaction of transactions) {
          await this.verifyAccountAccess(transaction.accountId);
        }

        if (transactions.length !== validatedData.transactionIds.length) {
          throw new Error("Some transactions not found or access denied");
        }
      }

      // Perform bulk update using transaction
      const result = await this.executeTransaction(async (tx) => {
        const updateData: any = {
          ...(validatedData.updates.type && {
            type: validatedData.updates.type,
          }),
          ...(validatedData.updates.amount && {
            amount: validatedData.updates.amount,
          }),
          ...(validatedData.updates.payment_method_id !== undefined && {
            payment_method_id: validatedData.updates.payment_method_id,
          }),
          ...(validatedData.updates.condition !== undefined && {
            condition: validatedData.updates.condition,
          }),
          ...(validatedData.updates.status && {
            status: validatedData.updates.status,
          }),
          ...(validatedData.updates.description !== undefined && {
            description: validatedData.updates.description,
          }),
          updatedAt: new Date(),
        };

        const updateResult = await tx.transaction.updateMany({
          where: { id: { in: validatedData.transactionIds } },
          data: updateData,
        });

        return {
          count: updateResult.count,
          updatedIds: validatedData.transactionIds,
        };
      });

      this.log("info", "Bulk update completed successfully", {
        updatedCount: result.count,
      });

      return result;
    }, "bulkUpdateTransactions");
  }

  /**
   * Get transaction statistics for an account
   * @param accountId - Account ID (optional, defaults to current user's accounts)
   * @param dateFrom - Start date for statistics (optional)
   * @param dateTo - End date for statistics (optional)
   * @returns Transaction statistics
   */
  async getTransactionStats(
    accountId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ServiceResponse<TransactionStats>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Getting transaction statistics", {
        accountId,
        dateFrom,
        dateTo,
      });

      // Build where clause
      const where: any = {};

      if (accountId) {
        where.accountId = accountId;
        // Verify user has access to this account
        if (this.authRequired) {
          await this.verifyAccountAccess(accountId);
        }
      } else if (this.authRequired) {
        // Filter by current user's accessible accounts
        const currentUserId = this.getCurrentUserId();
        if (currentUserId) {
          where.account = {
            userId: currentUserId,
          };
        }
      }

      // Date range filtering
      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = new Date(dateFrom);
        if (dateTo) where.createdAt.lte = new Date(dateTo);
      }

      // Get all transactions for statistics
      const transactions = await this.findManyRecords<any>({
        where,
        select: {
          id: true,
          type: true,
          status: true,
          amount: true,
          createdAt: true,
        },
      });

      // Calculate statistics
      const totalTransactions = transactions.length;
      const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
      const averageAmount =
        totalTransactions > 0 ? totalAmount / totalTransactions : 0;

      // Group by type
      const transactionsByType = transactions.reduce((acc, t) => {
        acc[t.type as TransactionType] =
          (acc[t.type as TransactionType] || 0) + 1;
        return acc;
      }, {} as Record<TransactionType, number>);

      // Group by status
      const transactionsByStatus = transactions.reduce((acc, t) => {
        acc[t.status as TransactionStatus] =
          (acc[t.status as TransactionStatus] || 0) + 1;
        return acc;
      }, {} as Record<TransactionStatus, number>);

      // Monthly statistics
      const monthlyStats = transactions.reduce((acc, t) => {
        const month = t.createdAt.toISOString().substring(0, 7); // YYYY-MM
        const existing = acc.find(
          (m: { month: string; count: number; totalAmount: number }) =>
            m.month === month
        );
        if (existing) {
          existing.count += 1;
          existing.totalAmount += t.amount;
        } else {
          acc.push({
            month,
            count: 1,
            totalAmount: t.amount,
          });
        }
        return acc;
      }, [] as Array<{ month: string; count: number; totalAmount: number }>);

      const stats: TransactionStats = {
        totalTransactions,
        totalAmount,
        averageAmount,
        transactionsByType,
        transactionsByStatus,
        monthlyStats: monthlyStats.sort(
          (a: { month: string }, b: { month: string }) =>
            a.month.localeCompare(b.month)
        ),
      };

      this.log("info", "Transaction statistics calculated successfully", {
        totalTransactions,
        totalAmount,
      });

      return stats;
    }, "getTransactionStats");
  }

  // ==================== HELPER METHODS ====================

  /**
   * Verify that the current user has access to the specified account
   * @param accountId - Account ID to verify access to
   * @throws Error if account not found or access denied
   */
  private async verifyAccountAccess(accountId: string): Promise<void> {
    const currentUserId = this.getCurrentUserId();
    if (!currentUserId) {
      throw new Error("Authentication required");
    }

    const account = await prisma.account.findFirst({
      where: {
        id: accountId,
        userId: currentUserId,
      },
      select: { id: true },
    });

    if (!account) {
      throw new Error("Account not found or access denied");
    }
  }

  /**
   * Verify that the payment method exists and belongs to the specified account
   * @param paymentMethodId - Payment method ID to verify
   * @param accountId - Account ID that should own the payment method
   * @throws Error if payment method not found or doesn't belong to account
   */
  private async verifyPaymentMethodAccess(
    paymentMethodId: string,
    accountId: string
  ): Promise<void> {
    const paymentMethod = await prisma.payment_method.findFirst({
      where: {
        id: paymentMethodId,
        accountId: accountId,
      },
      select: { id: true },
    });

    if (!paymentMethod) {
      throw new Error(
        "Payment method not found or doesn't belong to the specified account"
      );
    }
  }

  /**
   * Get transactions by account ID with optional filtering
   * @param accountId - Account ID
   * @param filters - Optional filters (status, type, date range)
   * @returns Transactions for the account
   */
  async getTransactionsByAccount(
    accountId: string,
    filters: {
      status?: string;
      type?: string;
      dateFrom?: string;
      dateTo?: string;
      limit?: number;
    } = {}
  ): Promise<ServiceResponse<TransactionWithRelations[]>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
        await this.verifyAccountAccess(accountId);
      }

      this.log("info", "Getting transactions by account", {
        accountId,
        filters,
      });

      // Build where clause
      const where: any = { accountId };

      if (filters.status) where.status = filters.status;
      if (filters.type) where.type = filters.type;

      // Date range filtering
      if (filters.dateFrom || filters.dateTo) {
        where.createdAt = {};
        if (filters.dateFrom) where.createdAt.gte = new Date(filters.dateFrom);
        if (filters.dateTo) where.createdAt.lte = new Date(filters.dateTo);
      }

      const transactions = await this.findManyRecords<TransactionWithRelations>(
        {
          where,
          include: {
            payment_method: {
              select: {
                id: true,
                holder_name: true,
                ref_number: true,
                type: true,
                isDefault: true,
              },
            },
            account: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          ...(filters.limit && { take: filters.limit }),
        }
      );

      this.log("info", "Transactions retrieved successfully", {
        accountId,
        count: transactions.length,
      });

      return transactions;
    }, "getTransactionsByAccount");
  }

  /**
   * Get transactions by payment method ID
   * @param paymentMethodId - Payment method ID
   * @returns Transactions for the payment method
   */
  async getTransactionsByPaymentMethod(
    paymentMethodId: string
  ): Promise<ServiceResponse<TransactionWithRelations[]>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      this.log("info", "Getting transactions by payment method", {
        paymentMethodId,
      });

      // First verify the payment method exists and user has access
      const paymentMethod = await prisma.payment_method.findUnique({
        where: { id: paymentMethodId },
        select: { id: true, accountId: true },
      });

      if (!paymentMethod) {
        throw new Error("Payment method not found");
      }

      if (this.authRequired) {
        await this.verifyAccountAccess(paymentMethod.accountId);
      }

      const transactions = await this.findManyRecords<TransactionWithRelations>(
        {
          where: { payment_method_id: paymentMethodId },
          include: {
            payment_method: {
              select: {
                id: true,
                holder_name: true,
                ref_number: true,
                type: true,
                isDefault: true,
              },
            },
            account: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        }
      );

      this.log("info", "Transactions retrieved successfully", {
        paymentMethodId,
        count: transactions.length,
      });

      return transactions;
    }, "getTransactionsByPaymentMethod");
  }
}
