import { z } from "zod";
import { BaseService, DatabaseOptions, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import { IdSchema } from "@/lib/api/validators/schemas/common";

/**
 * RBAC service configuration
 */
export interface RBACServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
  validateInput?: boolean;
  validateOutput?: boolean;
  requireAuth?: boolean;
}

/**
 * Schema for assigning a role to a user
 */
export const AssignRoleSchema = z.object({
  userId: IdSchema,
  roleId: IdSchema,
});

/**
 * Schema for removing a role from a user
 */
export const RemoveRoleSchema = z.object({
  userId: IdSchema,
});

/**
 * Schema for user query parameters
 */
export const UserQuerySchema = z.object({
  id: IdSchema.optional(),
  email: z.string().email().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  roleId: IdSchema.optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
  sortBy: z
    .enum(["firstName", "lastName", "email", "createdAt", "updatedAt"])
    .optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
  search: z.string().optional(),
});

/**
 * RBAC service for handling role-based access control operations
 *
 * This service extends BaseService and provides:
 * 1. Role assignment and removal operations
 * 2. User management with role information
 * 3. Current user retrieval with role details
 * 4. Input/output validation using Zod schemas
 * 5. Authentication and authorization checks
 * 6. Standardized error handling and logging
 */
export class RBACService extends BaseService {
  private authRequired: boolean;

  constructor(config: RBACServiceConfig = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the model to the user model delegate for user operations
    this.setModel(prisma.user as any);
  }

  /**
   * Assign a role to a user
   * @param data - Role assignment data containing userId and roleId
   * @returns Service response with updated user
   */
  async assignRole(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(AssignRoleSchema, data);

      // Check if user exists
      const user = await this.findUniqueRecord({
        where: { id: validatedData.userId },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Set the model to the role model delegate for role operations
      this.setModel(prisma.role as any);

      // Check if role exists
      const role = await this.findUniqueRecord({
        where: { id: validatedData.roleId },
      }).finally(() => {
        // Set the model back to the user model delegate
        this.setModel(prisma.user as any);
      });

      if (!role) {
        throw new Error("Role not found");
      }

      // Assign role to user
      const updatedUser = await this.updateRecord({
        where: { id: validatedData.userId },
        data: { roleId: validatedData.roleId },
        include: { role: true },
      });

      // Making sure the model is set back to the user model delegate
      this.setModel(prisma.user as any);

      return updatedUser;
    }, "assignRole");
  }

  /**
   * Remove role from a user
   * @param data - Role removal data containing userId
   * @returns Service response with updated user
   */
  async removeRole(data: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(RemoveRoleSchema, data);

      // Check if user exists
      const user = await this.findUniqueRecord({
        where: { id: validatedData.userId },
        include: { role: true },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Remove role from user
      const updatedUser = await this.updateRecord({
        where: { id: validatedData.userId },
        data: { roleId: null },
        include: { role: true },
      });

      return updatedUser;
    }, "removeRole");
  }

  /**
   * Get users with role information and filtering
   * @param query - Query parameters for filtering and pagination
   * @returns Service response with users list
   */
  async getUsers(query: unknown): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate query parameters
      const validatedQuery = this.validateInput(UserQuerySchema, query || {});

      // Build where clause
      const where: any = {};
      if (validatedQuery.id) where.id = validatedQuery.id;
      if (validatedQuery.email)
        where.email = { contains: validatedQuery.email, mode: "insensitive" };
      if (validatedQuery.firstName)
        where.firstName = {
          contains: validatedQuery.firstName,
          mode: "insensitive",
        };
      if (validatedQuery.lastName)
        where.lastName = {
          contains: validatedQuery.lastName,
          mode: "insensitive",
        };
      if (validatedQuery.roleId) where.roleId = validatedQuery.roleId;
      if (validatedQuery.search) {
        where.OR = [
          {
            firstName: { contains: validatedQuery.search, mode: "insensitive" },
          },
          {
            lastName: { contains: validatedQuery.search, mode: "insensitive" },
          },
          { email: { contains: validatedQuery.search, mode: "insensitive" } },
        ];
      }

      const options: DatabaseOptions = {
        where,
        include: { role: true },
      };

      // Handle single user request
      if (validatedQuery.id) {
        const user = await this.findUniqueRecord({
          where: { id: validatedQuery.id },
          include: options.include,
        });

        if (!user) {
          throw new Error("User not found");
        }

        return user;
      }

      // Handle pagination
      if (validatedQuery.limit) {
        options.take = parseInt(String(validatedQuery.limit));
      }

      if (validatedQuery.offset) {
        options.skip = parseInt(String(validatedQuery.offset));
      }

      // Handle sorting
      if (validatedQuery.sortBy) {
        options.orderBy = {
          [validatedQuery.sortBy]: validatedQuery.sortOrder || "desc",
        } as any;
      }

      // Handle multiple users request
      const users = await this.findManyRecords(options);
      const total = await this.countRecords({ where });

      return {
        users,
        pagination: {
          total,
          limit: validatedQuery.limit
            ? parseInt(String(validatedQuery.limit))
            : users.length,
          offset: validatedQuery.offset
            ? parseInt(String(validatedQuery.offset))
            : 0,
        },
      };
    }, "getUsers");
  }

  /**
   * Get user by ID with role information
   * @param userId - ID of the user to retrieve
   * @returns Service response with user data
   */
  async getUserById(userId: string): Promise<ServiceResponse> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        super.requireAuth();
      }

      // Validate user ID
      const validatedId = this.validateInput(IdSchema, userId);

      // Get user with role information
      const user = await this.findUniqueRecord({
        where: { id: validatedId },
        include: { role: true, profile: true },
      });

      if (!user) {
        throw new Error("User not found");
      }

      return user;
    }, "getUserById");
  }
}

/**
 * Type exports
 */
export type AssignRole = z.infer<typeof AssignRoleSchema>;
export type RemoveRole = z.infer<typeof RemoveRoleSchema>;
export type UserQuery = z.infer<typeof UserQuerySchema>;
