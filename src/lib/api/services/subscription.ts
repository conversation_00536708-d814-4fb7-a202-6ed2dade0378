import { BaseService, ServiceResponse, ServiceContext } from "./base";
import { prisma } from "@/lib/common/prisma";
import { NotificationService } from "./notification";
import {
  CreateSubscriptionSchema,
  UpdateSubscriptionSchema,
  SubscriptionQuerySchema,
  BulkSubscriptionSchema,
  SubscriptionStatisticsSchema,
  type CreateSubscription,
  type UpdateSubscription,
  type SubscriptionQuery,
  type BulkSubscription,
  type Subscription,
  type SubscriptionStatistics,
} from "@/lib/api/validators/schemas/subscription";

/**
 * Subscription Service
 *
 * Handles all subscription-related operations including CRUD operations,
 * search, statistics, and bulk actions with notification integration.
 */
export class SubscriptionService extends BaseService {
  private notificationService: NotificationService;

  constructor() {
    super({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });

    // Set the Prisma model for subscription operations
    this.setModel(prisma.subscription as any);

    // Initialize notification service
    this.notificationService = new NotificationService();
  }

  /**
   * Initialize the service with context
   */
  async initialize(context: ServiceContext): Promise<void> {
    this.setContext(context);
    await this.notificationService.initialize(context);
  }

  /**
   * Create a new subscription
   */
  async createSubscription(
    subscriptionData: CreateSubscription
  ): Promise<ServiceResponse<Subscription>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(
        CreateSubscriptionSchema,
        subscriptionData
      );

      // Check if subscription with same name already exists
      const existingSubscription = await this.findFirstRecord({
        where: { name: validatedData.name },
      });

      if (existingSubscription) {
        throw new Error("Subscription with this name already exists");
      }

      // Create the subscription
      const subscription = await this.createRecord<Subscription>({
        data: {
          ...validatedData,
          subscribers: validatedData.subscribers || 0,
        },
      });

      // Send notification about subscription creation
      if (this.context?.user?.id) {
        await this.notificationService.createNotification({
          title: "Subscription Created",
          message: `New subscription "${subscription.name}" has been created`,
          type: "systemAlerts",
          category: "inApp",
          userId: this.context.user.id,
          priority: "normal",
          data: {
            subscriptionId: subscription.id,
            subscriptionName: subscription.name,
            action: "create",
          },
        });
      }

      return subscription;
    }, "createSubscription");
  }

  /**
   * Get subscription by ID
   */
  async getSubscriptionById(
    id: string
  ): Promise<ServiceResponse<Subscription>> {
    return this.executeOperation(async () => {
      const subscription = await this.findUniqueRecord<Subscription>({
        where: { id },
      });

      if (!subscription) {
        throw new Error("Subscription not found");
      }

      return subscription;
    }, "getSubscriptionById");
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(
    id: string,
    subscriptionData: Partial<UpdateSubscription>
  ): Promise<ServiceResponse<Subscription>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(
        UpdateSubscriptionSchema.omit({ id: true }),
        subscriptionData
      );

      // Check if subscription exists
      const existingSubscription = await this.findUniqueRecord({
        where: { id },
      });

      if (!existingSubscription) {
        throw new Error("Subscription not found");
      }

      // Check if name is being updated and if it conflicts with another subscription
      if (
        validatedData.name &&
        validatedData.name !== existingSubscription.name
      ) {
        const nameConflict = await this.findFirstRecord({
          where: {
            name: validatedData.name,
            id: { not: id },
          },
        });

        if (nameConflict) {
          throw new Error("Subscription with this name already exists");
        }
      }

      // Update the subscription
      const updatedSubscription = await this.updateRecord<Subscription>({
        where: { id },
        data: validatedData,
      });

      // Send notification about subscription update
      if (this.context?.user?.id) {
        await this.notificationService.createNotification({
          title: "Subscription Updated",
          message: `Subscription "${updatedSubscription.name}" has been updated`,
          type: "systemAlerts",
          category: "inApp",
          userId: this.context.user.id,
          priority: "normal",
          data: {
            subscriptionId: updatedSubscription.id,
            subscriptionName: updatedSubscription.name,
            action: "update",
          },
        });
      }

      return updatedSubscription;
    }, "updateSubscription");
  }

  /**
   * Delete a subscription
   */
  async deleteSubscription(id: string): Promise<ServiceResponse<Subscription>> {
    return this.executeOperation(async () => {
      // Check if subscription exists
      const existingSubscription = await this.findUniqueRecord({
        where: { id },
      });

      if (!existingSubscription) {
        throw new Error("Subscription not found");
      }

      // Delete the subscription
      const deletedSubscription = await this.deleteRecord<Subscription>({
        where: { id },
      });

      // Send notification about subscription deletion
      if (this.context?.user?.id) {
        await this.notificationService.createNotification({
          title: "Subscription Deleted",
          message: `Subscription "${existingSubscription.name}" has been deleted`,
          type: "systemAlerts",
          category: "inApp",
          userId: this.context.user.id,
          priority: "normal",
          data: {
            subscriptionId: id,
            subscriptionName: existingSubscription.name,
            action: "delete",
          },
        });
      }

      return deletedSubscription;
    }, "deleteSubscription");
  }

  /**
   * Search subscriptions with filtering and pagination
   */
  async searchSubscriptions(
    queryParams: SubscriptionQuery
  ): Promise<ServiceResponse<Subscription[]>> {
    return this.executeOperation(
      async () => {
        // Validate query parameters
        const validatedParams = this.validateInput(
          SubscriptionQuerySchema,
          queryParams
        );

        // Build where clause
        const where: any = {};

        if (validatedParams.id) {
          where.id = validatedParams.id;
        }

        if (validatedParams.name) {
          where.name = { contains: validatedParams.name, mode: "insensitive" };
        }

        if (validatedParams.status) {
          where.status = validatedParams.status;
        }

        if (
          validatedParams.minPrice !== undefined ||
          validatedParams.maxPrice !== undefined
        ) {
          where.price = {};
          if (validatedParams.minPrice !== undefined) {
            where.price.gte = validatedParams.minPrice;
          }
          if (validatedParams.maxPrice !== undefined) {
            where.price.lte = validatedParams.maxPrice;
          }
        }

        if (
          validatedParams.minSubscribers !== undefined ||
          validatedParams.maxSubscribers !== undefined
        ) {
          where.subscribers = {};
          if (validatedParams.minSubscribers !== undefined) {
            where.subscribers.gte = validatedParams.minSubscribers;
          }
          if (validatedParams.maxSubscribers !== undefined) {
            where.subscribers.lte = validatedParams.maxSubscribers;
          }
        }

        if (validatedParams.search) {
          where.OR = [
            { name: { contains: validatedParams.search, mode: "insensitive" } },
            {
              description: {
                contains: validatedParams.search,
                mode: "insensitive",
              },
            },
          ];
        }

        // Set pagination
        const paginationSettings = {
          page: validatedParams.page,
          limit: validatedParams.limit,
        };
        this.handlePagination(paginationSettings);

        // Get subscriptions with pagination
        const subscriptions = await this.findManyRecords<Subscription>({
          where,
          orderBy: { [validatedParams.sortBy]: validatedParams.sortOrder },
          take: this.limit,
          skip: this.offset,
        });

        return subscriptions;
      },
      "searchSubscriptions",
      true
    );
  }

  /**
   * Get all subscriptions with pagination
   */
  async getAllSubscriptions(
    queryParams: Partial<SubscriptionQuery> = {}
  ): Promise<ServiceResponse<Subscription[]>> {
    return this.executeOperation(
      async () => {
        // Validate query parameters
        const validatedParams = this.validateInput(
          SubscriptionQuerySchema.partial(),
          queryParams
        );

        // Set pagination
        this.setPagination(
          validatedParams.page || 1,
          validatedParams.limit || 10
        );

        // Build where clause
        const where: any = {};
        if (validatedParams.status) {
          where.status = validatedParams.status;
        }

        // Get subscriptions with pagination
        const subscriptions = await this.findManyRecords<Subscription>({
          where,
          orderBy: {
            [validatedParams.sortBy || "createdAt"]:
              validatedParams.sortOrder || "desc",
          },
          take: this.limit,
          skip: this.offset,
        });

        return subscriptions;
      },
      "getAllSubscriptions",
      true
    );
  }

  /**
   * Perform bulk actions on subscriptions
   */
  async bulkAction(bulkData: BulkSubscription): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Validate bulk data
      const validatedData = this.validateInput(
        BulkSubscriptionSchema,
        bulkData
      );

      let result: any;

      switch (validatedData.action) {
        case "create":
          if (
            !validatedData.subscriptions ||
            validatedData.subscriptions.length === 0
          ) {
            throw new Error("Subscriptions data is required for bulk create");
          }
          result = await this.bulkCreateSubscriptions(
            validatedData.subscriptions
          );
          break;

        case "update":
          if (!validatedData.subscriptionIds || !validatedData.updates) {
            throw new Error(
              "Subscription IDs and updates are required for bulk update"
            );
          }
          result = await this.bulkUpdateSubscriptions(
            validatedData.subscriptionIds,
            validatedData.updates
          );
          break;

        case "delete":
          if (
            !validatedData.subscriptionIds ||
            validatedData.subscriptionIds.length === 0
          ) {
            throw new Error("Subscription IDs are required for bulk delete");
          }
          result = await this.bulkDeleteSubscriptions(
            validatedData.subscriptionIds
          );
          break;

        case "activate":
          if (
            !validatedData.subscriptionIds ||
            validatedData.subscriptionIds.length === 0
          ) {
            throw new Error("Subscription IDs are required for bulk activate");
          }
          result = await this.bulkUpdateSubscriptions(
            validatedData.subscriptionIds,
            { status: "active" }
          );
          break;

        case "deactivate":
          if (
            !validatedData.subscriptionIds ||
            validatedData.subscriptionIds.length === 0
          ) {
            throw new Error(
              "Subscription IDs are required for bulk deactivate"
            );
          }
          result = await this.bulkUpdateSubscriptions(
            validatedData.subscriptionIds,
            { status: "inactive" }
          );
          break;

        default:
          throw new Error(`Unsupported bulk action: ${validatedData.action}`);
      }

      return result;
    }, "bulkAction");
  }

  /**
   * Bulk create subscriptions
   */
  private async bulkCreateSubscriptions(
    subscriptions: CreateSubscription[]
  ): Promise<any> {
    const results = [];
    const errors = [];

    for (const subscriptionData of subscriptions) {
      try {
        const result = await this.createSubscription(subscriptionData);
        if (result.success) {
          results.push(result.data);
        } else {
          errors.push({ data: subscriptionData, error: result.message });
        }
      } catch (error) {
        errors.push({
          data: subscriptionData,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      created: results,
      errors,
      summary: {
        total: subscriptions.length,
        successful: results.length,
        failed: errors.length,
      },
    };
  }

  /**
   * Bulk update subscriptions
   */
  private async bulkUpdateSubscriptions(
    subscriptionIds: string[],
    updates: Partial<UpdateSubscription>
  ): Promise<any> {
    const results = [];
    const errors = [];

    for (const id of subscriptionIds) {
      try {
        const result = await this.updateSubscription(id, updates);
        if (result.success) {
          results.push(result.data);
        } else {
          errors.push({ id, error: result.message });
        }
      } catch (error) {
        errors.push({
          id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      updated: results,
      errors,
      summary: {
        total: subscriptionIds.length,
        successful: results.length,
        failed: errors.length,
      },
    };
  }

  /**
   * Bulk delete subscriptions
   */
  private async bulkDeleteSubscriptions(
    subscriptionIds: string[]
  ): Promise<any> {
    const results = [];
    const errors = [];

    for (const id of subscriptionIds) {
      try {
        const result = await this.deleteSubscription(id);
        if (result.success) {
          results.push({ id, deleted: true });
        } else {
          errors.push({ id, error: result.message });
        }
      } catch (error) {
        errors.push({
          id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return {
      deleted: results,
      errors,
      summary: {
        total: subscriptionIds.length,
        successful: results.length,
        failed: errors.length,
      },
    };
  }

  /**
   * Get subscription statistics
   */
  async getSubscriptionStatistics(): Promise<
    ServiceResponse<SubscriptionStatistics>
  > {
    return this.executeOperation(async () => {
      // Get total counts
      const totalSubscriptions = await this.countRecords({});
      const activeSubscriptions = await this.countRecords({
        where: { status: "active" },
      });
      const inactiveSubscriptions = await this.countRecords({
        where: { status: "inactive" },
      });

      // Get aggregated data
      const aggregateResult = await (prisma.subscription as any).aggregate({
        _sum: {
          subscribers: true,
          price: true,
        },
        _avg: {
          price: true,
        },
      });

      // Get subscriptions by status
      const statusCounts = await (prisma.subscription as any).groupBy({
        by: ["status"],
        _count: {
          id: true,
        },
      });

      const subscriptionsByStatus = statusCounts.reduce(
        (acc: any, item: any) => {
          acc[item.status] = item._count.id;
          return acc;
        },
        {}
      );

      // Get recent subscriptions
      const recentSubscriptions = await this.findManyRecords<Subscription>({
        orderBy: { createdAt: "desc" },
        take: 10,
      });

      const statistics: SubscriptionStatistics = {
        totalSubscriptions,
        activeSubscriptions,
        inactiveSubscriptions,
        totalSubscribers: aggregateResult._sum.subscribers || 0,
        averagePrice: aggregateResult._avg.price || 0,
        totalRevenue: aggregateResult._sum.price || 0,
        subscriptionsByStatus,
        recentSubscriptions,
      };

      return statistics;
    }, "getSubscriptionStatistics");
  }
}
