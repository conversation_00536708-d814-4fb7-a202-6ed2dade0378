import { prisma } from "@/lib/common/prisma";
import { BaseService, ServiceResponse } from "./base";
import { BraintreeService } from "./braintree";

/**
 * Payment Method Service
 * 
 * This service provides payment method management functionality
 * by wrapping the BraintreeService and database operations.
 */
export class PaymentMethodService extends BaseService {
  private braintreeService: BraintreeService;
  private authRequired: boolean;

  constructor(config: any = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    this.braintreeService = new BraintreeService(config);
    
    // Set the Prisma model for payment method operations
    this.setModel(prisma.payment_method as any);
  }

  /**
   * Set payment method as default
   * @param paymentMethodId - The payment method ID to set as default
   * @returns Service response with updated payment method
   */
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      if (!paymentMethodId) {
        throw new Error("Payment method ID is required");
      }

      // Get current user account ID
      const accountId = this.getContext()?.user?.id;
      if (!accountId) {
        throw new Error("User account ID is required");
      }

      // First, unset all existing default payment methods for this account
      await this.updateManyRecords({
        where: { accountId },
        data: { isDefault: false },
      });

      // Set the specified payment method as default
      const updatedPaymentMethod = await this.updateRecord(paymentMethodId, {
        isDefault: true,
      });

      this.log("info", "Payment method set as default successfully", {
        paymentMethodId,
        accountId,
      });

      return updatedPaymentMethod;
    }, "setDefaultPaymentMethod");
  }

  /**
   * Get payment methods for current user
   * @returns Service response with payment methods
   */
  async getPaymentMethods(): Promise<ServiceResponse<any[]>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user account ID
      const accountId = this.getContext()?.user?.id;
      if (!accountId) {
        throw new Error("User account ID is required");
      }

      const paymentMethods = await this.findManyRecords({
        where: { accountId },
        orderBy: [
          { isDefault: "desc" }, // Default payment methods first
          { createdAt: "desc" },
        ],
      });

      this.log("info", "Payment methods retrieved successfully", {
        accountId,
        count: paymentMethods.length,
      });

      return paymentMethods;
    }, "getPaymentMethods");
  }

  /**
   * Create a new payment method
   * @param paymentMethodData - Payment method data
   * @returns Service response with created payment method
   */
  async createPaymentMethod(paymentMethodData: any): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user account ID
      const accountId = this.getContext()?.user?.id;
      if (!accountId) {
        throw new Error("User account ID is required");
      }

      // Add account ID to payment method data
      const dataWithAccount = {
        ...paymentMethodData,
        accountId,
      };

      // Create payment method in database
      const paymentMethod = await this.createRecord(dataWithAccount);

      this.log("info", "Payment method created successfully", {
        paymentMethodId: paymentMethod.id,
        accountId,
      });

      return paymentMethod;
    }, "createPaymentMethod");
  }

  /**
   * Delete a payment method
   * @param paymentMethodId - The payment method ID to delete
   * @returns Service response
   */
  async deletePaymentMethod(paymentMethodId: string): Promise<ServiceResponse<any>> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      if (!paymentMethodId) {
        throw new Error("Payment method ID is required");
      }

      // Get current user account ID
      const accountId = this.getContext()?.user?.id;
      if (!accountId) {
        throw new Error("User account ID is required");
      }

      // Verify the payment method belongs to the current user
      const paymentMethod = await this.findUniqueRecord({
        where: { id: paymentMethodId, accountId },
      });

      if (!paymentMethod) {
        throw new Error("Payment method not found or access denied");
      }

      // Delete the payment method
      await this.deleteRecord(paymentMethodId);

      this.log("info", "Payment method deleted successfully", {
        paymentMethodId,
        accountId,
      });

      return { deleted: true };
    }, "deletePaymentMethod");
  }
}
