import { z } from "zod";

// Contract status enum
export const ContractStatus = z.enum([
  "draft",
  "active",
  "completed",
  "terminated",
  "expired",
]);

// Contract search parameters schema
export const ContractSearchParamsSchema = z.object({
  query: z.string().optional(),
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
});

// Base contract schema
export const ContractSchema = z.object({
  id: z.string(),
  status: ContractStatus,
  client: z
    .object({
      id: z.string(),
      name: z.string(),
      email: z.string().email(),
    })
    .optional(),
  total_value: z
    .number()
    .min(0, "Total value must be positive")
    .optional()
    .default(0),
  paid_value: z
    .number()
    .min(0, "Paid value must be positive")
    .optional()
    .default(0),
  remaining_value: z
    .number()
    .min(0, "Remaining value must be positive")
    .optional()
    .default(0),
  start_date: z.string().datetime(),
  hasRoom: z.boolean().optional(),
  attachments: z.any().optional(),
  room: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .optional(),
  end_date: z.string().datetime(),
  proposal_id: z.string().optional(),
  proposal: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Create contract schema (without id, createdAt, updatedAt)
export const CreateContractSchema = ContractSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Update contract schema (all fields optional except id)
export const UpdateContractSchema = ContractSchema.partial().extend({
  id: z.string(),
});

// Contract response schema
export const ContractResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: ContractSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Contracts list response schema
export const ContractsListResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z.array(ContractSchema).optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Contract statistics schema
export const ContractStatisticsSchema = z.object({
  total: z.number(),
  active: z.number(),
  completed: z.number(),
  draft: z.number(),
  terminated: z.number(),
  expired: z.number(),
  totalValue: z.number(),
  averageValue: z.number(),
});

// Contract statistics response schema
export const ContractStatisticsResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: ContractStatisticsSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Type exports
export type Contract = z.infer<typeof ContractSchema>;
export type CreateContract = z.infer<typeof CreateContractSchema>;
export type UpdateContract = z.infer<typeof UpdateContractSchema>;
export type ContractSearchParams = z.infer<typeof ContractSearchParamsSchema>;
export type ContractResponse = z.infer<typeof ContractResponseSchema>;
export type ContractsListResponse = z.infer<typeof ContractsListResponseSchema>;
export type ContractStatistics = z.infer<typeof ContractStatisticsSchema>;
export type ContractStatisticsResponse = z.infer<
  typeof ContractStatisticsResponseSchema
>;
