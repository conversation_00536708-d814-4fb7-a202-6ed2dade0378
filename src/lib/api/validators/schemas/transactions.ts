import { z } from "zod";

// Transaction type enum
export const TransactionTypeEnum = z.enum([
  "full_payment",
  "partial_payment",
  "advanced_payment",
]);

// Transaction status enum
export const TransactionStatusEnum = z.enum([
  "active",
  "inactive",
  "submitted",
  "received",
  "negotiating",
  "agreed",
  "created",
  "inprogress",
  "reviewing",
  "completed",
  "closed",
  "terminated",
  "pending",
]);

// Sort fields enum for transaction queries
export const TransactionSortFieldEnum = z.enum([
  "createdAt",
  "updatedAt",
  "amount",
  "type",
  "status",
]);

// Sort order enum
export const SortOrderEnum = z.enum(["asc", "desc"]);

// Base transaction schema
export const TransactionSchema = z.object({
  id: z.string(),
  type: TransactionTypeEnum,
  amount: z.number().positive("Amount must be positive"),
  payment_method_id: z.string().optional(),
  condition: z.string().optional(),
  status: TransactionStatusEnum,
  description: z.string().optional(),
  accountId: z.string().min(1, "Account ID is required"),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

// Create transaction schema (without id, createdAt, updatedAt)
export const CreateTransactionSchema = z.object({
  type: TransactionTypeEnum.default("full_payment"),
  orderId: z.string().min(1, "Order ID is required"),
  amount: z.number().positive("Amount must be positive"),
  payment_method_id: z.string().optional(),
  condition: z.string().optional(),
  status: TransactionStatusEnum.default("created"),
  description: z.string().optional(),
  accountId: z.string().min(1, "Account ID is required"),
});

// Update transaction schema (all fields optional)
export const UpdateTransactionSchema = z.object({
  type: TransactionTypeEnum.optional(),
  amount: z.number().positive("Amount must be positive").optional(),
  payment_method_id: z.string().optional(),
  condition: z.string().optional(),
  status: TransactionStatusEnum.optional(),
  description: z.string().optional(),
});

// Transaction query schema for filtering and pagination
export const TransactionQuerySchema = z.object({
  id: z.string().optional(),
  accountId: z.string().optional(),
  payment_method_id: z.string().optional(),
  type: TransactionTypeEnum.optional(),
  status: TransactionStatusEnum.optional(),
  minAmount: z.number().optional(),
  maxAmount: z.number().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  sortBy: TransactionSortFieldEnum.default("createdAt"),
  sortOrder: SortOrderEnum.default("desc"),
  includePaymentMethod: z.boolean().default(false),
  includeAccount: z.boolean().default(false),
});

// Bulk update transaction schema
export const BulkUpdateTransactionSchema = z.object({
  transactionIds: z
    .array(z.string())
    .min(1, "At least one transaction ID is required"),
  updates: UpdateTransactionSchema,
});

// Transaction with relations schema
export const TransactionWithRelationsSchema = TransactionSchema.extend({
  payment_method: z
    .object({
      id: z.string(),
      holder_name: z.string(),
      ref_number: z.string(),
      type: z.string(),
      isDefault: z.boolean(),
    })
    .optional(),
  account: z
    .object({
      id: z.string(),
      email: z.string(),
      name: z.string().optional(),
    })
    .optional(),
});

// Transaction statistics schema
export const TransactionStatsSchema = z.object({
  totalTransactions: z.number(),
  totalAmount: z.number(),
  averageAmount: z.number(),
  transactionsByType: z.record(TransactionTypeEnum, z.number()),
  transactionsByStatus: z.record(TransactionStatusEnum, z.number()),
  monthlyStats: z.array(
    z.object({
      month: z.string(),
      count: z.number(),
      totalAmount: z.number(),
    })
  ),
});

// Transaction response schema
export const TransactionResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: TransactionWithRelationsSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Transactions list response schema
export const TransactionsListResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      transactions: z.array(TransactionWithRelationsSchema),
      pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        totalPages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
      }),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Transaction statistics response schema
export const TransactionStatsResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: TransactionStatsSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Bulk update response schema
export const BulkUpdateResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      count: z.number(),
      updatedIds: z.array(z.string()),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Type exports for TypeScript
export type Transaction = z.infer<typeof TransactionSchema>;
export type CreateTransaction = z.infer<typeof CreateTransactionSchema>;
export type UpdateTransaction = z.infer<typeof UpdateTransactionSchema>;
export type TransactionQuery = z.infer<typeof TransactionQuerySchema>;
export type BulkUpdateTransaction = z.infer<typeof BulkUpdateTransactionSchema>;
export type TransactionWithRelations = z.infer<
  typeof TransactionWithRelationsSchema
>;
export type TransactionStats = z.infer<typeof TransactionStatsSchema>;
export type TransactionType = z.infer<typeof TransactionTypeEnum>;
export type TransactionStatus = z.infer<typeof TransactionStatusEnum>;
export type TransactionSortField = z.infer<typeof TransactionSortFieldEnum>;
export type SortOrder = z.infer<typeof SortOrderEnum>;

// Response type exports
export type TransactionResponse = z.infer<typeof TransactionResponseSchema>;
export type TransactionsListResponse = z.infer<
  typeof TransactionsListResponseSchema
>;
export type TransactionStatsResponse = z.infer<
  typeof TransactionStatsResponseSchema
>;
export type BulkUpdateResponse = z.infer<typeof BulkUpdateResponseSchema>;
