import { z } from "zod";
import { StatusSchema, IdSchema, RequiredStringSchema } from "./common";

/**
 * Base Document schema based on Prisma Document model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseDocumentSchema = z.object({
  name: RequiredStringSchema,
  path: RequiredStringSchema,
  file_type: RequiredStringSchema,
  size: RequiredStringSchema,
  status: StatusSchema.default("created"),
  category: RequiredStringSchema,
  association_entity: RequiredStringSchema,
  association_id: RequiredStringSchema,
  proposalId: z.string().cuid().optional(),
});

/**
 * Complete Document schema including metadata fields
 */
export const DocumentSchema = BaseDocumentSchema.extend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Document creation schema (for API requests)
 * Excludes id, createdAt, updatedAt, and makes status optional with default
 */
export const CreateDocumentSchema = z.object({
  name: RequiredStringSchema,
  path: RequiredStringSchema,
  file_type: RequiredStringSchema,
  size: z.int().optional(),
  status: StatusSchema.optional().default("created"),
  category: z.string().optional(),
  association_entity: RequiredStringSchema,
  association_id: RequiredStringSchema,
});

/**
 * Document update schema (for API requests)
 * All fields are optional except id
 */
export const UpdateDocumentSchema = z.object({
  id: IdSchema,
  name: RequiredStringSchema.optional(),
  path: RequiredStringSchema.optional(),
  file_type: RequiredStringSchema.optional(),
  size: RequiredStringSchema.optional(),
  status: StatusSchema.optional(),
  category: RequiredStringSchema.optional(),
  association_entity: RequiredStringSchema.optional(),
  association_id: RequiredStringSchema.optional(),
  proposalId: z.string().cuid().optional(),
});

/**
 * Document query/filter schema (for API requests)
 */
export const DocumentQuerySchema = z.object({
  id: IdSchema.optional(),
  category: RequiredStringSchema.optional(),
  status: StatusSchema.optional(),
  association_entity: RequiredStringSchema.optional(),
  association_id: RequiredStringSchema.optional(),
  proposalId: z.string().cuid().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
});

/**
 * Type exports
 */
export type Document = z.infer<typeof DocumentSchema>;
export type BaseDocument = z.infer<typeof BaseDocumentSchema>;
export type CreateDocument = z.infer<typeof CreateDocumentSchema>;
export type UpdateDocument = z.infer<typeof UpdateDocumentSchema>;
export type DocumentQuery = z.infer<typeof DocumentQuerySchema>;
