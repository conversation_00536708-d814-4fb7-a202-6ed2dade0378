import { z } from "zod";
import { IdSchema, RequiredStringSchema, StatusSchema } from "./common";

/**
 * Permission actions enum
 */
export const PermissionActionSchema = z.enum([
  "create",
  "read",
  "update",
  "delete",
]);

/**
 * Entity permissions schema - maps entity to array of actions
 * Structure: { [entity]: ['create', 'update', 'delete', 'read'], ... }
 */
export const EntityPermissionsSchema = z.record(
  z.string(), // entity name (e.g., 'user', 'role', 'document')
  z.array(PermissionActionSchema) // array of actions for that entity
);

/**
 * Base Role schema based on Prisma Role model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseRoleSchema = z.object({
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: StatusSchema.default("created"),
  permissions: EntityPermissionsSchema.default({}),
});

/**
 * Complete Role schema including metadata fields
 */
export const RoleSchema = BaseRoleSchema.extend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date().optional(),
});

/**
 * Role creation schema (for API requests)
 */
export const CreateRoleSchema = z.object({
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: StatusSchema.default("created"),
  permissions: EntityPermissionsSchema.default({}),
});

/**
 * Role update schema (for API requests)
 */
export const UpdateRoleSchema = z.object({
  id: IdSchema,
  name: RequiredStringSchema.optional(),
  description: z.string().optional(),
  status: StatusSchema.optional(),
  permissions: EntityPermissionsSchema.optional(),
});

/**
 * Role query schema (for API requests)
 */
export const RoleQuerySchema = z.object({
  id: IdSchema.optional(),
  name: z.string().optional(),
  status: StatusSchema.optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
  sortBy: z.enum(["name", "status", "createdAt", "updatedAt"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
  search: z.string().optional(),
});

/**
 * Role permission update schema
 */
export const UpdateRolePermissionsSchema = z.object({
  id: IdSchema,
  permissions: EntityPermissionsSchema,
});

/**
 * Add entity permission schema
 */
export const AddEntityPermissionSchema = z.object({
  roleId: IdSchema,
  entity: z.string().min(1, "Entity name is required"),
  action: PermissionActionSchema,
});

/**
 * Remove entity permission schema
 */
export const RemoveEntityPermissionSchema = z.object({
  roleId: IdSchema,
  entity: z.string().min(1, "Entity name is required"),
  action: PermissionActionSchema,
});

/**
 * Set entity permissions schema
 */
export const SetEntityPermissionsSchema = z.object({
  roleId: IdSchema,
  entity: z.string().min(1, "Entity name is required"),
  actions: z.array(PermissionActionSchema),
});

/**
 * Check entity permission schema
 */
export const CheckEntityPermissionSchema = z.object({
  roleId: IdSchema,
  entity: z.string().min(1, "Entity name is required"),
  action: PermissionActionSchema,
});

/**
 * Role response schema
 */
export const RoleResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: RoleSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

/**
 * Roles list response schema
 */
export const RolesListResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      roles: z.array(RoleSchema),
      pagination: z.object({
        total: z.number(),
        limit: z.number(),
        offset: z.number(),
      }),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

/**
 * Type exports
 */
export type PermissionAction = z.infer<typeof PermissionActionSchema>;
export type EntityPermissions = z.infer<typeof EntityPermissionsSchema>;
export type Role = z.infer<typeof RoleSchema>;
export type BaseRole = z.infer<typeof BaseRoleSchema>;
export type CreateRole = z.infer<typeof CreateRoleSchema>;
export type UpdateRole = z.infer<typeof UpdateRoleSchema>;
export type RoleQuery = z.infer<typeof RoleQuerySchema>;
export type UpdateRolePermissions = z.infer<typeof UpdateRolePermissionsSchema>;
export type AddEntityPermission = z.infer<typeof AddEntityPermissionSchema>;
export type RemoveEntityPermission = z.infer<
  typeof RemoveEntityPermissionSchema
>;
export type SetEntityPermissions = z.infer<typeof SetEntityPermissionsSchema>;
export type CheckEntityPermission = z.infer<typeof CheckEntityPermissionSchema>;
export type RoleResponse = z.infer<typeof RoleResponseSchema>;
export type RolesListResponse = z.infer<typeof RolesListResponseSchema>;
