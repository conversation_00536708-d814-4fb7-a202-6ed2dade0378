import { z } from "zod";

/**
 * Braintree transaction validation schemas
 */

// Create transaction schema
// Note: merchantAccountId is not included as it's automatically set to the company account
export const CreateTransactionSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  customerId: z.string().optional(),
  orderId: z.string(),
  paymentMethodNonce: z.string().optional(),
  payment_method_id: z.string().optional(), // Keep for backward compatibility
  description: z.string().optional(),
});

// Create customer schema
export const CreateCustomerSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email(),
  phone: z.string().optional(),
  company: z.string().optional(),
});

// Create payment method schema
export const CreatePaymentMethodSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
  paymentMethodNonce: z.string().min(1, "Payment method nonce is required"),
  makeDefault: z.boolean().optional().default(false),
});

// Create payment method nonce schema
export const CreatePaymentMethodNonceSchema = z.object({
  paymentMethodToken: z.string().min(1, "Payment method token is required"),
});

// Void transaction schema
export const VoidTransactionSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
});

// Refund transaction schema
export const RefundTransactionSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
  amount: z.number().positive().optional(),
});

// Initialize Braintree schema
export const InitializeBraintreeSchema = z.object({
  customerId: z.string().optional(),
  environment: z.enum(["sandbox", "production"]).optional(),
});

// Test connection schema
export const TestConnectionSchema = z.object({
  includeDetails: z.boolean().optional().default(false),
});

// Braintree webhook schema
export const BraintreeWebhookSchema = z.object({
  bt_signature: z.string().min(1, "Braintree signature is required"),
  bt_payload: z.string().min(1, "Braintree payload is required"),
});

// Braintree configuration schema
export const BraintreeConfigSchema = z.object({
  merchantId: z.string().min(1, "Merchant ID is required"),
  publicKey: z.string().min(1, "Public key is required"),
  privateKey: z.string().min(1, "Private key is required"),
  environment: z.enum(["sandbox", "production"]),
  merchantAccountId: z.string().optional(),
});

// Client token request schema
export const ClientTokenRequestSchema = z.object({
  customerId: z.string().optional(),
  merchantAccountId: z.string().optional(),
});

// Transaction search schema
export const TransactionSearchSchema = z.object({
  customerId: z.string().optional(),
  status: z
    .enum([
      "authorized",
      "authorizing",
      "authorization_expired",
      "failed",
      "gateway_rejected",
      "processor_declined",
      "settled",
      "settling",
      "submitted_for_settlement",
      "voided",
    ])
    .optional(),
  amount: z
    .object({
      min: z.number().positive().optional(),
      max: z.number().positive().optional(),
    })
    .optional(),
  createdAt: z
    .object({
      min: z.date().optional(),
      max: z.date().optional(),
    })
    .optional(),
  orderId: z.string().optional(),
  paymentMethodToken: z.string().optional(),
  // merchantAccountId removed - searches are automatically scoped to company account
});

// Payment method search schema
export const PaymentMethodSearchSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
  type: z
    .enum([
      "credit_card",
      "paypal_account",
      "apple_pay_card",
      "google_pay_card",
    ])
    .optional(),
  isDefault: z.boolean().optional(),
});

// Customer search schema
export const CustomerSearchSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  company: z.string().optional(),
  createdAt: z
    .object({
      min: z.date().optional(),
      max: z.date().optional(),
    })
    .optional(),
});

// Update customer schema
export const UpdateCustomerSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  company: z.string().optional(),
});

// Update payment method schema
export const UpdatePaymentMethodSchema = z.object({
  paymentMethodToken: z.string().min(1, "Payment method token is required"),
  makeDefault: z.boolean().optional(),
  billingAddress: z
    .object({
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      company: z.string().optional(),
      streetAddress: z.string().optional(),
      extendedAddress: z.string().optional(),
      locality: z.string().optional(),
      region: z.string().optional(),
      postalCode: z.string().optional(),
      countryCodeAlpha2: z.string().optional(),
    })
    .optional(),
});

// Delete payment method schema
export const DeletePaymentMethodSchema = z.object({
  paymentMethodToken: z.string().min(1, "Payment method token is required"),
});

// Delete customer schema
export const DeleteCustomerSchema = z.object({
  customerId: z.string().min(1, "Customer ID is required"),
});

/**
 * Type exports for TypeScript
 */
export type CreateTransactionRequest = z.infer<typeof CreateTransactionSchema>;
export type CreateCustomerRequest = z.infer<typeof CreateCustomerSchema>;
export type CreatePaymentMethodRequest = z.infer<
  typeof CreatePaymentMethodSchema
>;
export type CreatePaymentMethodNonceRequest = z.infer<
  typeof CreatePaymentMethodNonceSchema
>;
export type VoidTransactionRequest = z.infer<typeof VoidTransactionSchema>;
export type RefundTransactionRequest = z.infer<typeof RefundTransactionSchema>;
export type InitializeBraintreeRequest = z.infer<
  typeof InitializeBraintreeSchema
>;
export type TestConnectionRequest = z.infer<typeof TestConnectionSchema>;
export type BraintreeWebhookRequest = z.infer<typeof BraintreeWebhookSchema>;
export type BraintreeConfigRequest = z.infer<typeof BraintreeConfigSchema>;
export type ClientTokenRequest = z.infer<typeof ClientTokenRequestSchema>;
export type TransactionSearchRequest = z.infer<typeof TransactionSearchSchema>;
export type PaymentMethodSearchRequest = z.infer<
  typeof PaymentMethodSearchSchema
>;
export type CustomerSearchRequest = z.infer<typeof CustomerSearchSchema>;
export type UpdateCustomerRequest = z.infer<typeof UpdateCustomerSchema>;
export type UpdatePaymentMethodRequest = z.infer<
  typeof UpdatePaymentMethodSchema
>;
export type DeletePaymentMethodRequest = z.infer<
  typeof DeletePaymentMethodSchema
>;
export type DeleteCustomerRequest = z.infer<typeof DeleteCustomerSchema>;

/**
 * Response schemas for API endpoints
 */
export const BraintreeTransactionResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      id: z.string(),
      status: z.string(),
      amount: z.number(),
      currencyIsoCode: z.string(),
      merchantId: z.string(),
      orderId: z.string().optional(),
      customerId: z.string().optional(),
      paymentMethodToken: z.string().optional(),
      createdAt: z.date(),
      updatedAt: z.date(),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

export const BraintreeCustomerResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      id: z.string(),
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      email: z.string().optional(),
      phone: z.string().optional(),
      company: z.string().optional(),
      createdAt: z.date(),
      updatedAt: z.date(),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

export const BraintreePaymentMethodResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      token: z.string(),
      customerId: z.string(),
      isDefault: z.boolean(),
      type: z.string(),
      createdAt: z.date(),
      updatedAt: z.date(),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

export const BraintreeStatusResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z
    .object({
      isConnected: z.boolean(),
      environment: z.string(),
      merchantId: z.string(),
      lastChecked: z.string(),
    })
    .optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Response type exports
export type BraintreeTransactionResponse = z.infer<
  typeof BraintreeTransactionResponseSchema
>;
export type BraintreeCustomerResponse = z.infer<
  typeof BraintreeCustomerResponseSchema
>;
export type BraintreePaymentMethodResponse = z.infer<
  typeof BraintreePaymentMethodResponseSchema
>;
export type BraintreeStatusResponse = z.infer<
  typeof BraintreeStatusResponseSchema
>;
