import { z } from "zod";

// Base room schema
export const RoomSchema = z.object({
  id: z.string(),
  contractId: z.string().optional(),
  members: z.array(z.string()).optional(),
  accountId: z.string().optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Create room schema (without id, createdAt, updatedAt)
export const CreateRoomSchema = RoomSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Update room schema (all fields optional except id)
export const UpdateRoomSchema = RoomSchema.partial()
  .extend({
    id: z.string(),
  })
  .omit({
    createdAt: true,
    updatedAt: true,
  });

// Room with relations schema
export const RoomWithRelationsSchema = RoomSchema.extend({
  contract: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .optional(),
  account: z
    .object({
      id: z.string(),
      user: z
        .object({
          id: z.string(),
          name: z.string().optional(),
          email: z.string().optional(),
          image: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
  members: z
    .array(
      z.object({
        id: z.string(),
        accountId: z.string(),
        roomId: z.string(),
        joinedAt: z.string().datetime(),
        account: z
          .object({
            id: z.string(),
            user: z
              .object({
                id: z.string(),
                name: z.string().optional(),
                email: z.string().optional(),
                image: z.string().optional(),
              })
              .optional(),
          })
          .optional(),
      })
    )
    .optional(),
  messages: z
    .array(
      z.object({
        id: z.string(),
        content: z.string(),
        createdAt: z.string().datetime(),
      })
    )
    .optional(),
  _count: z
    .object({
      members: z.number(),
      messages: z.number(),
    })
    .optional(),
});

// Room response schema
export const RoomResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: RoomWithRelationsSchema.optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
});

// Rooms list response schema
export const RoomsListResponseSchema = z.object({
  success: z.boolean(),
  error: z.boolean(),
  data: z.array(RoomWithRelationsSchema).optional(),
  message: z.string().optional(),
  statusCode: z.number().optional(),
  timestamp: z.string().optional(),
  pagination: z
    .object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    })
    .optional(),
});

// Room query parameters schema
export const RoomQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1)),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10)),
  search: z.string().optional(),
  contractId: z.string().optional(),
  accountId: z.string().optional(),
  includeMembers: z
    .string()
    .optional()
    .transform((val) => val === "true"),
  includeMessages: z
    .string()
    .optional()
    .transform((val) => val === "true"),
  includeContract: z
    .string()
    .optional()
    .transform((val) => val === "true"),
});

// Export types
export type Room = z.infer<typeof RoomSchema>;
export type CreateRoom = z.infer<typeof CreateRoomSchema>;
export type UpdateRoom = z.infer<typeof UpdateRoomSchema>;
export type RoomWithRelations = z.infer<typeof RoomWithRelationsSchema>;
export type RoomQuery = z.infer<typeof RoomQuerySchema>;
export type RoomResponse = z.infer<typeof RoomResponseSchema>;
export type RoomsListResponse = z.infer<typeof RoomsListResponseSchema>;
