import { z } from "zod";

// Status enum schema matching Prisma schema
export const StatusSchema = z.enum([
  "online",
  "offline",
  "active",
  "inactive",
  "submitted",
  "received",
  "negotiating",
  "agreed",
  "created",
  "inprogress",
  "reviewing",
  "completed",
  "closed",
  "terminated",
  "pending",
]);

// Required string schema for validation
export const RequiredStringSchema = z.string().min(1, "This field is required");

/**
 * Base Request schema based on Prisma Request model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseRequestSchema = z.object({
  id: z.string().optional(),
  message: z.string().optional(),
  status: StatusSchema.default("active"),
  tender_id: RequiredStringSchema,
  userId: z.string().min(1),
});

/**
 * Create Request schema for new request creation
 * Excludes id field as it's auto-generated
 */
export const CreateRequestSchema = BaseRequestSchema.omit({ id: true });

/**
 * Update Request schema for request updates
 * Makes all fields optional except id
 */
export const UpdateRequestSchema = z.object({
  id: RequiredStringSchema,
  message: z.string().optional(),
  status: StatusSchema.optional(),
  tender_id: z.string().optional(),
  userId: z.string().optional(),
});

/**
 * Request query schema for filtering and pagination
 */
export const RequestQuerySchema = z.object({
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
  sortBy: z.enum(["createdAt", "updatedAt", "status"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  status: StatusSchema.optional(),
  userId: z.string().optional(),
  tender_id: z.string().optional(),
  search: z.string().optional(),
});

/**
 * Bulk request operations schema
 */
export const BulkRequestSchema = z.object({
  action: z.enum(["delete", "updateStatus", "export"]),
  requestIds: z.array(z.string().min(1)).min(1, "At least one request ID is required"),
  data: z.record(z.any()).optional(), // For bulk updates
});

/**
 * Request statistics schema
 */
export const RequestStatsSchema = z.object({
  total: z.number().int().nonnegative(),
  active: z.number().int().nonnegative(),
  inactive: z.number().int().nonnegative(),
  submitted: z.number().int().nonnegative(),
  completed: z.number().int().nonnegative(),
  byStatus: z.record(z.string(), z.number().int().nonnegative()),
  byUser: z.record(z.string(), z.number().int().nonnegative()),
  recentActivity: z.array(z.object({
    date: z.string(),
    count: z.number().int().nonnegative(),
  })),
});

// Type exports for use in other files
export type CreateRequest = z.infer<typeof CreateRequestSchema>;
export type UpdateRequest = z.infer<typeof UpdateRequestSchema>;
export type RequestQuery = z.infer<typeof RequestQuerySchema>;
export type BulkRequest = z.infer<typeof BulkRequestSchema>;
export type RequestStats = z.infer<typeof RequestStatsSchema>;
export type Request = z.infer<typeof BaseRequestSchema> & {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
};
