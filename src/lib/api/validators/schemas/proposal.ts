import { z } from "zod";
import { StatusSchema, IdSchema, RequiredStringSchema } from "./common";

/**
 * Base Proposal schema based on Prisma Proposal model
 * Excludes metadata fields: id, createdAt, updatedAt
 */
export const BaseProposalSchema = z.object({
  id: z.string().optional(),
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: StatusSchema.default("created"),
  links: z.array(z.string()).default([]),
  milestones: z.array(z.any()).default([]), // Json[] in Prisma
  fixed_budget: z.number().min(0, "Fixed budget must be non-negative"),
  total_budget: z.number().min(0, "Total budget must be non-negative"),
  duration: z.number().int().positive("Duration must be a positive integer"),
  agreed_to_terms_and_conditions: z.boolean(),
  accountId: z.string().min(1),
  client: z
    .object({
      name: z.string(),
      email: z.string(),
    })
    .optional(),
});

/**
 * Complete Proposal schema including metadata fields
 */
export const ProposalSchema = BaseProposalSchema.extend({
  id: IdSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

/**
 * Proposal creation schema (for API requests)
 * Excludes id, createdAt, updatedAt, and makes some fields optional with defaults
 */
export const CreateProposalSchema = z.object({
  name: RequiredStringSchema,
  description: z.string().optional(),
  status: StatusSchema.optional().default("created"),
  links: z.array(z.string()).default([]).optional(),
  milestones: z.array(z.any()).default([]).optional(),
  fixed_budget: z
    .number()
    .min(0, "Fixed budget must be non-negative")
    .optional(),
  total_budget: z.number().min(0, "Total budget must be non-negative"),
  duration: z.number().int().positive("Duration must be a positive integer"),
  agreed_to_terms_and_conditions: z.boolean(),
});

/**
 * Proposal update schema (for API requests)
 * All fields are optional except id
 */
export const UpdateProposalSchema = z.object({
  id: IdSchema,
  name: RequiredStringSchema.optional(),
  description: z.string().optional(),
  status: StatusSchema.optional(),
  links: z.array(z.string()).optional(),
  milestones: z.array(z.any()).optional(),
  fixed_budget: z
    .number()
    .min(0, "Fixed budget must be non-negative")
    .optional(),
  total_budget: z
    .number()
    .min(0, "Total budget must be non-negative")
    .optional(),
  duration: z
    .number()
    .int()
    .positive("Duration must be a positive integer")
    .optional(),
  agreed_to_terms_and_conditions: z.boolean().optional(),
});

/**
 * Proposal query/filter schema (for API requests)
 */
export const ProposalQuerySchema = z.object({
  id: IdSchema.optional(),
  status: StatusSchema.optional(),
  accountId: z.string().min(1).optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
  sortBy: z
    .enum([
      "createdAt",
      "updatedAt",
      "name",
      "status",
      "total_budget",
      "duration",
    ])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

/**
 * Enhanced validation schemas with business logic
 */
export const ProposalBusinessValidationSchema = CreateProposalSchema.refine(
  (data) => data.total_budget >= (data.fixed_budget || 0),
  {
    message: "Total budget must be greater than or equal to fixed budget",
    path: ["total_budget"],
  }
)
  .refine((data) => data.duration > 0, {
    message: "Duration must be greater than 0",
    path: ["duration"],
  })
  .refine((data) => data.agreed_to_terms_and_conditions === true, {
    message: "Must agree to terms and conditions",
    path: ["agreed_to_terms_and_conditions"],
  });

/**
 * Type exports
 */
export type Proposal = z.infer<typeof ProposalSchema>;
export type BaseProposal = z.infer<typeof BaseProposalSchema>;
export type CreateProposal = z.infer<typeof CreateProposalSchema>;
export type UpdateProposal = z.infer<typeof UpdateProposalSchema>;
export type ProposalQuery = z.infer<typeof ProposalQuerySchema>;
