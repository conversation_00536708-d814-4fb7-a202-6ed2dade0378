import { ZodType, ZodError } from "zod";

/**
 * Interface for validation result
 */
export interface ValidationResult {
  isValid: boolean;
  data?: any;
  errors?: ValidationError[];
}

/**
 * Interface for validation error details
 */
export interface ValidationError {
  path: (string | number)[];
  message: string;
  code: string;
}

/**
 * Configuration interface for DataValidator
 */
export interface DataValidatorConfig {
  strictMode?: boolean;
  allowUnknownKeys?: boolean;
  transformData?: boolean;
}

/**
 * DataValidator class for validating data structures using Zod schemas
 *
 * This class provides comprehensive data validation by:
 * 1. Accepting any Zod schema for validation
 * 2. Validating data structures against the provided schema
 * 3. Returning detailed validation results with errors
 * 4. Supporting configuration options for validation behavior
 */
export class DataValidator {
  private config: Required<DataValidatorConfig>;

  /**
   * Constructor for DataValidator
   * @param config - Configuration options for the validator
   */
  constructor(config: DataValidatorConfig = {}) {
    this.config = {
      strictMode: config.strictMode ?? true,
      allowUnknownKeys: config.allowUnknownKeys ?? false,
      transformData: config.transformData ?? true,
    };
  }

  /**
   * Validates data against a Zod schema
   * @param schema - The Zod schema to validate against
   * @param data - The data to validate
   * @returns ValidationResult - Validation result with success status and details
   */
  validate<T>(schema: ZodType<T>, data: unknown): ValidationResult {
    try {
      // Use safeParse for consistent behavior
      const result = schema.safeParse(data);

      if (result.success) {
        return {
          isValid: true,
          data: result.data,
        };
      } else {
        return {
          isValid: false,
          errors: this.formatZodErrors(result.error),
        };
      }
    } catch (error) {
      if (error instanceof ZodError) {
        return {
          isValid: false,
          errors: this.formatZodErrors(error),
        };
      }

      // Handle unexpected errors
      return {
        isValid: false,
        errors: [{
          path: [],
          message: error instanceof Error ? error.message : 'Unknown validation error',
          code: 'UNKNOWN_ERROR',
        }],
      };
    }
  }

  /**
   * Simple boolean validation - returns only true/false
   * @param schema - The Zod schema to validate against
   * @param data - The data to validate
   * @returns boolean - True if validation passes, false otherwise
   */
  isValid<T>(schema: ZodType<T>, data: unknown): boolean {
    const result = this.validate(schema, data);
    return result.isValid;
  }

  /**
   * Validates data and returns the parsed/transformed data if valid
   * @param schema - The Zod schema to validate against
   * @param data - The data to validate
   * @returns T | null - Parsed data if valid, null if invalid
   */
  validateAndTransform<T>(schema: ZodType<T>, data: unknown): T | null {
    const result = this.validate(schema, data);
    return result.isValid ? result.data : null;
  }

  /**
   * Validates multiple data items against the same schema
   * @param schema - The Zod schema to validate against
   * @param dataArray - Array of data items to validate
   * @returns ValidationResult[] - Array of validation results
   */
  validateMany<T>(
    schema: ZodType<T>,
    dataArray: unknown[]
  ): ValidationResult[] {
    return dataArray.map((data) => this.validate(schema, data));
  }

  /**
   * Validates multiple data items and returns only valid ones
   * @param schema - The Zod schema to validate against
   * @param dataArray - Array of data items to validate
   * @returns T[] - Array of valid, transformed data items
   */
  filterValid<T>(schema: ZodType<T>, dataArray: unknown[]): T[] {
    return dataArray
      .map((data) => this.validateAndTransform(schema, data))
      .filter((item): item is T => item !== null);
  }

  /**
   * Formats Zod errors into a more readable format
   * @param zodError - The ZodError to format
   * @returns ValidationError[] - Formatted error array
   */
  private formatZodErrors(zodError: ZodError): ValidationError[] {
    return zodError.issues.map((error: any) => ({
      path: error.path,
      message: error.message,
      code: error.code,
    }));
  }

  /**
   * Updates the validator configuration
   * @param config - New configuration to merge
   */
  updateConfig(config: Partial<DataValidatorConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
  }

  /**
   * Gets the current configuration
   * @returns Current configuration
   */
  getConfig(): Readonly<Required<DataValidatorConfig>> {
    return { ...this.config };
  }

  /**
   * Static factory method to create a DataValidator with default configuration
   * @returns DataValidator instance
   */
  static createDefault(): DataValidator {
    return new DataValidator();
  }

  /**
   * Static factory method to create a DataValidator with strict validation
   * @returns DataValidator instance with strict mode enabled
   */
  static createStrict(): DataValidator {
    return new DataValidator({
      strictMode: true,
      allowUnknownKeys: false,
      transformData: true,
    });
  }

  /**
   * Static factory method to create a DataValidator with lenient validation
   * @returns DataValidator instance with lenient mode enabled
   */
  static createLenient(): DataValidator {
    return new DataValidator({
      strictMode: false,
      allowUnknownKeys: true,
      transformData: false,
    });
  }

  /**
   * Static utility method for quick validation without creating an instance
   * @param schema - The Zod schema to validate against
   * @param data - The data to validate
   * @returns boolean - True if validation passes, false otherwise
   */
  static quickValidate<T>(schema: ZodType<T>, data: unknown): boolean {
    const validator = DataValidator.createDefault();
    return validator.isValid(schema, data);
  }
}

/**
 * Default singleton instance for convenience
 */
export const defaultDataValidator = DataValidator.createDefault();

/**
 * Convenience function for quick validation
 * @param schema - The Zod schema to validate against
 * @param data - The data to validate
 * @returns boolean - True if validation passes, false otherwise
 */
export function validateData<T>(schema: ZodType<T>, data: unknown): boolean {
  return defaultDataValidator.isValid(schema, data);
}
