export * from "./init";

// Convenience validators combining schemas with DataValidator
import { DataValidator } from "./init";
import {
  CreateProposalSchema,
  UpdateProposalSchema,
  ProposalQuerySchema,
  ProposalBusinessValidationSchema,
  CreateDocumentSchema,
  UpdateDocumentSchema,
  DocumentQuerySchema,
} from "../schemas";

/**
 * Pre-configured validators for common use cases
 */
export class ApiValidators {
  private static validator = DataValidator.createStrict();

  // Proposal validators
  static validateCreateProposal(data: unknown) {
    return this.validator.validate(CreateProposalSchema, data);
  }

  static validateUpdateProposal(data: unknown) {
    return this.validator.validate(UpdateProposalSchema, data);
  }

  static validateProposalQuery(data: unknown) {
    return this.validator.validate(ProposalQuerySchema, data);
  }

  static validateProposalBusiness(data: unknown) {
    return this.validator.validate(ProposalBusinessValidationSchema, data);
  }

  // Document validators
  static validateCreateDocument(data: unknown) {
    return this.validator.validate(CreateDocumentSchema, data);
  }

  static validateUpdateDocument(data: unknown) {
    return this.validator.validate(UpdateDocumentSchema, data);
  }

  static validateDocumentQuery(data: unknown) {
    return this.validator.validate(DocumentQuerySchema, data);
  }

  // Boolean validators (for quick checks)
  static isValidCreateProposal(data: unknown): boolean {
    return this.validator.isValid(CreateProposalSchema, data);
  }

  static isValidUpdateProposal(data: unknown): boolean {
    return this.validator.isValid(UpdateProposalSchema, data);
  }

  static isValidCreateDocument(data: unknown): boolean {
    return this.validator.isValid(CreateDocumentSchema, data);
  }

  static isValidUpdateDocument(data: unknown): boolean {
    return this.validator.isValid(UpdateDocumentSchema, data);
  }
}

/**
 * Quick validation functions
 */
export const validateCreateProposal = (data: unknown) =>
  ApiValidators.validateCreateProposal(data);

export const validateUpdateProposal = (data: unknown) =>
  ApiValidators.validateUpdateProposal(data);

export const validateCreateDocument = (data: unknown) =>
  ApiValidators.validateCreateDocument(data);

export const validateUpdateDocument = (data: unknown) =>
  ApiValidators.validateUpdateDocument(data);
