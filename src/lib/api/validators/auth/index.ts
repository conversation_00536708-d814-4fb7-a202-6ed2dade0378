import { NextRequest, NextResponse } from "next/server";

import { auth } from "@/lib/auth";
import type { Session } from "next-auth";

/**
 * Interface for session validation result
 */
export interface SessionValidationResult {
  isValid: boolean;
  session?: Session;
  user?: Session["user"];
  error?: string;
  errorCode?: SessionErrorCode;
}

/**
 * Enum for session error codes
 */
export enum SessionErrorCode {
  NO_SESSION = "NO_SESSION",
  INVALID_SESSION = "INVALID_SESSION",
  SESSION_EXPIRED = "SESSION_EXPIRED",
  INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
  AUTH_ERROR = "AUTH_ERROR",
}

/**
 * Configuration interface for SessionService
 */
export interface SessionServiceConfig {
  requireEmailVerification?: boolean;
  allowedRoles?: string[];
  customValidation?: (session: Session) => boolean;
  errorMessages?: Partial<Record<SessionErrorCode, string>>;
}

/**
 * SessionService class for handling next-auth session validation
 *
 * This class provides comprehensive session management by:
 * 1. Validating user sessions using next-auth
 * 2. Providing role-based access control
 * 3. Custom session validation logic
 * 4. Standardized error responses
 * 5. Easy integration with API route handlers
 */
export class SessionService {
  private config: Required<SessionServiceConfig>;

  /**
   * Constructor for SessionService
   * @param config - Configuration options for the service
   */
  constructor(config: SessionServiceConfig = {}) {
    this.config = {
      requireEmailVerification: config.requireEmailVerification ?? false,
      allowedRoles: config.allowedRoles ?? [],
      customValidation: config.customValidation ?? (() => true),
      errorMessages: {
        NO_SESSION: "Authentication required",
        INVALID_SESSION: "Invalid session",
        SESSION_EXPIRED: "Session has expired",
        INSUFFICIENT_PERMISSIONS: "Insufficient permissions",
        AUTH_ERROR: "Authentication error",
        ...config.errorMessages,
      },
    };
  }

  /**
   * Validates the current session
   * @returns Promise<SessionValidationResult> - Validation result with session data or error
   */
  async validateSession(): Promise<SessionValidationResult> {
    try {
      const session: any = await auth();

      if (!session) {
        return {
          isValid: false,
          error: this.config.errorMessages.NO_SESSION,
          errorCode: SessionErrorCode.NO_SESSION,
        };
      }

      if (!session.user) {
        return {
          isValid: false,
          error: this.config.errorMessages.INVALID_SESSION,
          errorCode: SessionErrorCode.INVALID_SESSION,
        };
      }

      // Check email verification if required
      if (this.config.requireEmailVerification && !session.user.emailVerified) {
        return {
          isValid: false,
          error: "Email verification required",
          errorCode: SessionErrorCode.INSUFFICIENT_PERMISSIONS,
        };
      }

      // Check roles if specified
      if (this.config.allowedRoles.length > 0) {
        const userRole = (session.user as any).role;
        if (!userRole || !this.config.allowedRoles.includes(userRole)) {
          return {
            isValid: false,
            error: this.config.errorMessages.INSUFFICIENT_PERMISSIONS,
            errorCode: SessionErrorCode.INSUFFICIENT_PERMISSIONS,
          };
        }
      }

      // Run custom validation
      if (!this.config.customValidation(session)) {
        return {
          isValid: false,
          error: this.config.errorMessages.INSUFFICIENT_PERMISSIONS,
          errorCode: SessionErrorCode.INSUFFICIENT_PERMISSIONS,
        };
      }

      return {
        isValid: true,
        session,
        user: session.user,
      };
    } catch (error) {
      return {
        isValid: false,
        error: this.config.errorMessages.AUTH_ERROR,
        errorCode: SessionErrorCode.AUTH_ERROR,
      };
    }
  }

  /**
   * Validates session and returns user info if valid
   * @returns Promise<Session["user"] | null> - User object or null if invalid
   */
  async getValidatedUser(): Promise<Session["user"] | null> {
    const result = await this.validateSession();
    return result.isValid ? result.user! : null;
  }

  /**
   * Checks if the current session is valid (boolean only)
   * @returns Promise<boolean> - True if session is valid, false otherwise
   */
  async isSessionValid(): Promise<boolean> {
    const result = await this.validateSession();
    return result.isValid;
  }

  /**
   * Validates session and throws error if invalid
   * @returns Promise<Session> - Valid session object
   * @throws Error if session is invalid
   */
  async requireValidSession(): Promise<Session> {
    const result = await this.validateSession();
    if (!result.isValid) {
      throw new Error(result.error || "Session validation failed");
    }
    return result.session!;
  }

  /**
   * Creates a NextResponse for authentication errors
   * @param errorCode - The error code
   * @param customMessage - Optional custom error message
   * @returns NextResponse with appropriate error
   */
  createErrorResponse(
    errorCode: SessionErrorCode,
    customMessage?: string
  ): NextResponse {
    const message = customMessage || this.config.errorMessages[errorCode];
    const statusCode = this.getStatusCodeForError(errorCode);

    return NextResponse.json(
      {
        error: message,
        code: errorCode,
        authenticated: false,
      },
      { status: statusCode }
    );
  }

  /**
   * Middleware function for API route protection
   * @param handler - The API route handler function
   * @returns Protected API route handler
   */
  protect<T extends any[]>(
    handler: (
      request: NextRequest,
      context: { session: Session; user: Session["user"] },
      ...args: T
    ) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      const validation = await this.validateSession();

      if (!validation.isValid) {
        return this.createErrorResponse(
          validation.errorCode!,
          validation.error
        );
      }

      return handler(
        request,
        {
          session: validation.session!,
          user: validation.user!,
        },
        ...args
      );
    };
  }

  /**
   * Higher-order function for protecting API routes with custom validation
   * @param customValidation - Additional validation logic
   * @param handler - The API route handler function
   * @returns Protected API route handler
   */
  protectWithValidation<T extends any[]>(
    customValidation: (
      session: Session,
      user: Session["user"]
    ) => boolean | Promise<boolean>,
    handler: (
      request: NextRequest,
      context: { session: Session; user: Session["user"] },
      ...args: T
    ) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
      const validation = await this.validateSession();

      if (!validation.isValid) {
        return this.createErrorResponse(
          validation.errorCode!,
          validation.error
        );
      }

      // Run additional custom validation
      const isCustomValid = await customValidation(
        validation.session!,
        validation.user!
      );
      if (!isCustomValid) {
        return this.createErrorResponse(
          SessionErrorCode.INSUFFICIENT_PERMISSIONS,
          "Custom validation failed"
        );
      }

      return handler(
        request,
        {
          session: validation.session!,
          user: validation.user!,
        },
        ...args
      );
    };
  }

  /**
   * Maps error codes to HTTP status codes
   * @param errorCode - Session error code
   * @returns HTTP status code
   */
  private getStatusCodeForError(errorCode: SessionErrorCode): number {
    switch (errorCode) {
      case SessionErrorCode.NO_SESSION:
      case SessionErrorCode.INVALID_SESSION:
      case SessionErrorCode.SESSION_EXPIRED:
        return 401; // Unauthorized
      case SessionErrorCode.INSUFFICIENT_PERMISSIONS:
        return 403; // Forbidden
      case SessionErrorCode.AUTH_ERROR:
        return 500; // Internal Server Error
      default:
        return 401; // Default to Unauthorized
    }
  }

  /**
   * Updates the service configuration
   * @param config - New configuration to merge
   */
  updateConfig(config: Partial<SessionServiceConfig>): void {
    this.config = {
      ...this.config,
      ...config,
      errorMessages: {
        ...this.config.errorMessages,
        ...config.errorMessages,
      },
    };
  }

  /**
   * Gets the current configuration
   * @returns Current configuration
   */
  getConfig(): Readonly<Required<SessionServiceConfig>> {
    return { ...this.config };
  }

  /**
   * Static factory method to create a SessionService with default configuration
   * @returns SessionService instance
   */
  static createDefault(): SessionService {
    return new SessionService();
  }

  /**
   * Static factory method to create a SessionService with strict validation
   * @returns SessionService instance with email verification required
   */
  static createStrict(): SessionService {
    return new SessionService({
      requireEmailVerification: true,
    });
  }

  /**
   * Static factory method to create a SessionService with role-based access
   * @param allowedRoles - Array of allowed roles
   * @returns SessionService instance with role validation
   */
  static createWithRoles(allowedRoles: string[]): SessionService {
    return new SessionService({
      allowedRoles,
    });
  }
}

/**
 * Default singleton instance for convenience
 */
export const defaultSessionService = SessionService.createDefault();
