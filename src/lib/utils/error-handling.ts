import { toast } from "sonner";

/**
 * Standard API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  error: boolean;
  data?: T;
  message?: string;
  errors?: Array<{ field: string; message: string }>;
  statusCode?: number;
  timestamp?: string;
}

/**
 * Error handling utilities for consistent error management
 */
export class ErrorHandler {
  /**
   * Handle API response and show appropriate notifications
   */
  static handleApiResponse<T>(response: ApiResponse<T>): T | null {
    if (response.success && !response.error && response.data) {
      // Show success message if provided
      if (response.message) {
        toast.success(response.message);
      }
      return response.data;
    }

    // Handle error responses
    if (response.error && response.message) {
      toast.error(response.message);
    }

    if (response.errors && response.errors.length > 0) {
      response.errors.forEach((error) => {
        toast.error(`${error.field}: ${error.message}`);
      });
    }

    return null;
  }

  /**
   * Handle generic errors and show notifications
   */
  static handleError(
    error: unknown,
    defaultMessage: string = "An error occurred"
  ): void {
    if (error instanceof Error) {
      toast.error(error.message);
    } else if (typeof error === "string") {
      toast.error(error);
    } else {
      toast.error(defaultMessage);
    }
  }

  /**
   * Handle success responses with optional notification
   */
  static handleSuccess<T>(data: T, message?: string): T {
    if (message) {
      toast.success(message);
    }
    return data;
  }

  /**
   * Wrap async operations with error handling
   */
  static async withErrorHandling<T>(
    operation: () => Promise<T>,
    errorMessage?: string
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      this.handleError(error, errorMessage);
      return null;
    }
  }
}

/**
 * Utility functions for common error scenarios
 */
export const errorUtils = {
  /**
   * Check if response indicates success
   */
  isSuccess: <T>(
    response: ApiResponse<T>
  ): response is ApiResponse<T> & { success: true; data: T } => {
    return response.success === true && response.data !== undefined;
  },

  /**
   * Check if response indicates error
   */
  isError: <T>(
    response: ApiResponse<T>
  ): response is ApiResponse<T> & { success: false; error: string } => {
    return response.success === false;
  },

  /**
   * Extract error message from response
   */
  getErrorMessage: <T>(response: ApiResponse<T>): string => {
    if (response.error) return response.error;
    if (response.errors && response.errors.length > 0) {
      return response.errors.map((e) => `${e.field}: ${e.message}`).join(", ");
    }
    return "Unknown error occurred";
  },

  /**
   * Create success response
   */
  createSuccessResponse: <T>(
    data: T,
    statusCode: number = 200,
    message?: string
  ): ApiResponse<T> => ({
    success: true,
    error: false,
    data,
    message,
    statusCode,
    timestamp: new Date().toISOString(),
  }),

  /**
   * Create error response
   */
  createErrorResponse: (
    errorMessage: string,
    statusCode: number = 500
  ): ApiResponse => ({
    success: false,
    error: true,
    message: errorMessage,
    statusCode,
    timestamp: new Date().toISOString(),
  }),
};

/**
 * Type guard to check if an object is an ApiResponse
 */
export function isApiResponse(obj: any): obj is ApiResponse {
  return (
    typeof obj === "object" &&
    obj !== null &&
    typeof obj.success === "boolean" &&
    typeof obj.error === "boolean"
  );
}

/**
 * Hook for handling API responses in React components
 */
export function useErrorHandler() {
  const handleResponse = <T>(response: ApiResponse<T>): T | null => {
    return ErrorHandler.handleApiResponse(response);
  };

  const handleError = (error: unknown, defaultMessage?: string): void => {
    ErrorHandler.handleError(error, defaultMessage);
  };

  const handleSuccess = <T>(data: T, message?: string): T => {
    return ErrorHandler.handleSuccess(data, message);
  };

  return {
    handleResponse,
    handleError,
    handleSuccess,
  };
}
