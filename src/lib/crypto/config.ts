import CryptoJS from "crypto-js";

/**
 * Crypto configuration constants and environment variables
 */

// Environment variables for encryption
export const CRYPTO_CONFIG = {
  // Main encryption key (should be set in environment)
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || "change-this-key-in-production",

  // Database field encryption key (separate from main key)
  DB_FIELD_KEY:
    process.env.DB_FIELD_ENCRYPTION_KEY || "change-this-db-key-in-production",

  // Session encryption key
  SESSION_KEY:
    process.env.SESSION_ENCRYPTION_KEY ||
    "change-this-session-key-in-production",

  // API token encryption key
  API_TOKEN_KEY:
    process.env.API_TOKEN_ENCRYPTION_KEY || "change-this-api-key-in-production",

  // Default algorithms
  DEFAULT_ALGORITHM: "AES" as const,
  DEFAULT_HASH_ALGORITHM: "SHA256" as const,
  DEFAULT_ENCODING: "base64" as const,

  // Security settings
  MIN_KEY_LENGTH: 32,
  DEFAULT_SALT_LENGTH: 16,
  SESSION_TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
};

/**
 * Validate encryption configuration
 */
export function validateCryptoConfig(): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // Check if using default keys in production
  if (process.env.NODE_ENV === "production") {
    if (CRYPTO_CONFIG.ENCRYPTION_KEY === "change-this-key-in-production") {
      errors.push("ENCRYPTION_KEY must be set in production environment");
    }
    if (CRYPTO_CONFIG.DB_FIELD_KEY === "change-this-db-key-in-production") {
      warnings.push(
        "DB_FIELD_ENCRYPTION_KEY should be set for database field encryption"
      );
    }
    if (CRYPTO_CONFIG.SESSION_KEY === "change-this-session-key-in-production") {
      warnings.push(
        "SESSION_ENCRYPTION_KEY should be set for session encryption"
      );
    }
    if (CRYPTO_CONFIG.API_TOKEN_KEY === "change-this-api-key-in-production") {
      warnings.push(
        "API_TOKEN_ENCRYPTION_KEY should be set for API token encryption"
      );
    }
  }

  // Check key lengths
  if (CRYPTO_CONFIG.ENCRYPTION_KEY.length < CRYPTO_CONFIG.MIN_KEY_LENGTH) {
    errors.push(
      `ENCRYPTION_KEY must be at least ${CRYPTO_CONFIG.MIN_KEY_LENGTH} characters long`
    );
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
}

/**
 * Environment-specific crypto instances
 */
export const cryptoInstances = {
  // For general application data
  general: {
    algorithm: CRYPTO_CONFIG.DEFAULT_ALGORITHM,
    encoding: CRYPTO_CONFIG.DEFAULT_ENCODING,
    key: CRYPTO_CONFIG.ENCRYPTION_KEY,
  },

  // For database field encryption
  database: {
    algorithm: CRYPTO_CONFIG.DEFAULT_ALGORITHM,
    encoding: CRYPTO_CONFIG.DEFAULT_ENCODING,
    key: CRYPTO_CONFIG.DB_FIELD_KEY,
  },

  // For session data
  session: {
    algorithm: CRYPTO_CONFIG.DEFAULT_ALGORITHM,
    encoding: CRYPTO_CONFIG.DEFAULT_ENCODING,
    key: CRYPTO_CONFIG.SESSION_KEY,
  },

  // For API tokens
  apiToken: {
    algorithm: CRYPTO_CONFIG.DEFAULT_ALGORITHM,
    encoding: CRYPTO_CONFIG.DEFAULT_ENCODING,
    key: CRYPTO_CONFIG.API_TOKEN_KEY,
  },
};

/**
 * Security utilities
 */
export const security = {
  /**
   * Generate a cryptographically secure random string
   */
  generateSecureId: (length: number = 32): string => {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    const randomBytes = CryptoJS.lib.WordArray.random(length);
    const randomHex = randomBytes.toString(CryptoJS.enc.Hex);

    for (let i = 0; i < length; i++) {
      const byteValue = parseInt(randomHex.substr(i * 2, 2), 16);
      result += chars[byteValue % chars.length];
    }

    return result;
  },

  /**
   * Generate a secure API key
   */
  generateApiKey: (): string => {
    return security.generateSecureId(64);
  },

  /**
   * Generate a secure session ID
   */
  generateSessionId: (): string => {
    return security.generateSecureId(48);
  },

  /**
   * Create a time-based one-time password (TOTP) secret
   */
  generateTOTPSecret: (): string => {
    return security.generateSecureId(32);
  },
};

/**
 * Validation utilities
 */
export const validation = {
  /**
   * Check if a string is a valid encrypted format
   */
  isValidEncryptedString: (str: string): boolean => {
    try {
      // Check if it's valid base64 using crypto-js
      const decoded = CryptoJS.enc.Base64.parse(str);
      const reencoded = CryptoJS.enc.Base64.stringify(decoded);
      return reencoded === str && str.length > 0;
    } catch {
      return false;
    }
  },

  /**
   * Check if a hash looks valid
   */
  isValidHash: (
    hash: string,
    algorithm: "sha256" | "sha512" | "md5" = "sha256"
  ): boolean => {
    const expectedLengths = {
      sha256: 64,
      sha512: 128,
      md5: 32,
    };

    return (
      hash.length === expectedLengths[algorithm] && /^[a-f0-9]+$/i.test(hash)
    );
  },

  /**
   * Check if a key meets minimum security requirements
   */
  isSecureKey: (key: string): boolean => {
    return (
      key.length >= CRYPTO_CONFIG.MIN_KEY_LENGTH &&
      key !== "change-this-key-in-production" &&
      !/^(password|123456|admin|test)/.test(key.toLowerCase())
    );
  },
};

/**
 * Initialize crypto library with validation
 */
export function initializeCrypto(): {
  success: boolean;
  warnings: string[];
  errors: string[];
} {
  const validation = validateCryptoConfig();

  if (!validation.isValid) {
    console.error("Crypto configuration errors:", validation.errors);
  }

  if (validation.warnings.length > 0) {
    console.warn("Crypto configuration warnings:", validation.warnings);
  }

  return {
    success: validation.isValid,
    warnings: validation.warnings,
    errors: validation.errors,
  };
}
