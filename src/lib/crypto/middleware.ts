import { dataEncryption, password, session } from "./utils";
import { validation } from "./config";

/**
 * Middleware for encrypting/decrypting data in API routes
 */
export class CryptoMiddleware {
  /**
   * Encrypt sensitive fields in request data
   */
  static async encryptRequestData<T extends Record<string, any>>(
    data: T,
    sensitiveFields: (keyof T)[]
  ): Promise<T> {
    const encryptedData = { ...data };

    for (const field of sensitiveFields) {
      if (encryptedData[field] && typeof encryptedData[field] === "string") {
        (encryptedData[field] as any) = await dataEncryption.encryptField(
          encryptedData[field] as string
        );
      }
    }

    return encryptedData;
  }

  /**
   * Decrypt sensitive fields in response data
   */
  static async decryptResponseData<T extends Record<string, any>>(
    data: T,
    sensitiveFields: (keyof T)[]
  ): Promise<T> {
    const decryptedData = { ...data };

    for (const field of sensitiveFields) {
      if (decryptedData[field] && typeof decryptedData[field] === "string") {
        try {
          (decryptedData[field] as any) = await dataEncryption.decryptField(
            decryptedData[field] as string
          );
        } catch (error) {
          console.warn(`Failed to decrypt field ${String(field)}:`, error);
          // Keep original value if decryption fails
        }
      }
    }

    return decryptedData;
  }

  /**
   * Hash passwords in user registration/update
   */
  static async hashPassword(plainPassword: string): Promise<{
    hashedPassword: string;
    salt: string;
  }> {
    const { hash, salt } = await password.hash(plainPassword);
    return {
      hashedPassword: hash,
      salt,
    };
  }

  /**
   * Verify password during authentication
   */
  static async verifyPassword(
    plainPassword: string,
    hashedPassword: string,
    salt: string
  ): Promise<boolean> {
    return await password.verify(plainPassword, hashedPassword, salt);
  }

  /**
   * Create encrypted session token
   */
  static async createSessionToken(userData: {
    userId: string;
    email: string;
    role?: string;
    [key: string]: any;
  }): Promise<string> {
    return await session.createToken({
      ...userData,
      createdAt: new Date().toISOString(),
    });
  }

  /**
   * Verify and decode session token
   */
  static async verifySessionToken(token: string): Promise<{
    userId: string;
    email: string;
    role?: string;
    createdAt: string;
    [key: string]: any;
  } | null> {
    return await session.verifyToken(token);
  }

  /**
   * Sanitize and validate encrypted data
   */
  static validateEncryptedData(data: string): {
    isValid: boolean;
    error?: string;
  } {
    if (!data || typeof data !== "string") {
      return { isValid: false, error: "Data must be a non-empty string" };
    }

    if (!validation.isValidEncryptedString(data)) {
      return { isValid: false, error: "Invalid encrypted data format" };
    }

    return { isValid: true };
  }
}

/**
 * Database field encryption helpers
 */
export class DatabaseCrypto {
  /**
   * Encrypt sensitive database fields before saving
   */
  static async encryptFields<T extends Record<string, any>>(
    record: T,
    fieldsToEncrypt: (keyof T)[]
  ): Promise<T> {
    const encryptedRecord = { ...record };

    for (const field of fieldsToEncrypt) {
      if (
        encryptedRecord[field] &&
        typeof encryptedRecord[field] === "string"
      ) {
        (encryptedRecord[field] as any) = await dataEncryption.encryptField(
          encryptedRecord[field] as string
        );
      }
    }

    return encryptedRecord;
  }

  /**
   * Decrypt sensitive database fields after retrieval
   */
  static async decryptFields<T extends Record<string, any>>(
    record: T,
    fieldsToDecrypt: (keyof T)[]
  ): Promise<T> {
    const decryptedRecord = { ...record };

    for (const field of fieldsToDecrypt) {
      if (
        decryptedRecord[field] &&
        typeof decryptedRecord[field] === "string"
      ) {
        try {
          (decryptedRecord[field] as any) = await dataEncryption.decryptField(
            decryptedRecord[field] as string
          );
        } catch (error) {
          console.warn(
            `Failed to decrypt database field ${String(field)}:`,
            error
          );
          // Keep encrypted value if decryption fails
        }
      }
    }

    return decryptedRecord;
  }

  /**
   * Encrypt an array of records
   */
  static async encryptRecords<T extends Record<string, any>>(
    records: T[],
    fieldsToEncrypt: (keyof T)[]
  ): Promise<T[]> {
    return Promise.all(
      records.map((record) => this.encryptFields(record, fieldsToEncrypt))
    );
  }

  /**
   * Decrypt an array of records
   */
  static async decryptRecords<T extends Record<string, any>>(
    records: T[],
    fieldsToDecrypt: (keyof T)[]
  ): Promise<T[]> {
    return Promise.all(
      records.map((record) => this.decryptFields(record, fieldsToDecrypt))
    );
  }
}

/**
 * API security helpers
 */
export class APICrypto {
  /**
   * Encrypt API response data
   */
  static async encryptResponse<T>(data: T): Promise<{
    encrypted: string;
    timestamp: number;
  }> {
    const responseData = {
      data,
      timestamp: Date.now(),
    };

    const encrypted = await dataEncryption.encryptUserData(responseData);

    return {
      encrypted,
      timestamp: responseData.timestamp,
    };
  }

  /**
   * Decrypt API request data
   */
  static async decryptRequest<T>(encryptedData: string): Promise<T> {
    const decrypted = await dataEncryption.decryptUserData<{
      data: T;
      timestamp: number;
    }>(encryptedData);

    // Optional: Add timestamp validation here
    // const age = Date.now() - decrypted.timestamp;
    // if (age > MAX_REQUEST_AGE) throw new Error('Request too old');

    return decrypted.data;
  }

  /**
   * Generate secure API key for external integrations
   */
  static generateApiKey(): string {
    const CryptoJS = require("crypto-js");
    return CryptoJS.lib.WordArray.random(32).toString(CryptoJS.enc.Hex);
  }
}
