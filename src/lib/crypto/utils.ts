import { CryptoLibrary } from "./encryption";

/**
 * Default crypto instance for simple operations
 */
const defaultCrypto = new CryptoLibrary({
  algorithm: "AES",
  encoding: "base64",
  key: process.env.ENCRYPTION_KEY || "default-key-change-in-production",
});

/**
 * Simple encryption utilities for common use cases
 */
export const crypto = {
  /**
   * Encrypt a string with default settings
   */
  encrypt: async (data: string): Promise<string> => {
    const result = await defaultCrypto.encrypt(data);
    return result.encrypted;
  },

  /**
   * Encrypt a string synchronously with default settings
   */
  encryptSync: (data: string): string => {
    const result = defaultCrypto.encryptSync(data);
    return result.encrypted;
  },

  /**
   * Decrypt a string with default settings
   */
  decrypt: async (encryptedData: string): Promise<string> => {
    return await defaultCrypto.decrypt(
      encryptedData,
      defaultCrypto.getConfig().key
    );
  },

  /**
   * Decrypt a string synchronously with default settings
   */
  decryptSync: (encryptedData: string): string => {
    return defaultCrypto.decryptSync(
      encryptedData,
      defaultCrypto.getConfig().key
    );
  },

  /**
   * Hash a string with SHA256
   */
  hash: async (data: string): Promise<string> => {
    return await defaultCrypto.hash(data, { algorithm: "SHA256" });
  },

  /**
   * Hash a string synchronously with SHA256
   */
  hashSync: (data: string): string => {
    return defaultCrypto.hashSync(data, { algorithm: "SHA256" });
  },

  /**
   * Generate HMAC for data integrity
   */
  hmac: async (data: string, key?: string): Promise<string> => {
    return await defaultCrypto.hmac(data, { key });
  },

  /**
   * Generate HMAC synchronously
   */
  hmacSync: (data: string, key?: string): string => {
    return defaultCrypto.hmacSync(data, { key });
  },

  /**
   * Verify HMAC
   */
  verifyHMAC: async (
    data: string,
    expectedHMAC: string,
    key?: string
  ): Promise<boolean> => {
    return await defaultCrypto.verifyHMAC(data, expectedHMAC, { key });
  },

  /**
   * Verify HMAC synchronously
   */
  verifyHMACSync: (
    data: string,
    expectedHMAC: string,
    key?: string
  ): boolean => {
    return defaultCrypto.verifyHMACSync(data, expectedHMAC, { key });
  },

  /**
   * Generate secure random bytes as hex string
   */
  randomBytes: (length: number = 32): string => {
    return defaultCrypto.generateRandomBytes(length);
  },

  /**
   * Generate secure random key
   */
  generateKey: (length: number = 32): string => {
    return defaultCrypto.generateSecureKey(length);
  },
};

/**
 * Password-specific utilities
 */
export const password = {
  /**
   * Hash a password with salt
   */
  hash: async (
    password: string,
    salt?: string
  ): Promise<{ hash: string; salt: string }> => {
    const passwordSalt = salt || defaultCrypto.generateSecureKey(16);
    const hash = await defaultCrypto.hash(password + passwordSalt, {
      algorithm: "SHA256",
    });
    return { hash, salt: passwordSalt };
  },

  /**
   * Hash a password synchronously with salt
   */
  hashSync: (
    password: string,
    salt?: string
  ): { hash: string; salt: string } => {
    const passwordSalt = salt || defaultCrypto.generateSecureKey(16);
    const hash = defaultCrypto.hashSync(password + passwordSalt, {
      algorithm: "SHA256",
    });
    return { hash, salt: passwordSalt };
  },

  /**
   * Verify a password against a hash
   */
  verify: async (
    password: string,
    hash: string,
    salt: string
  ): Promise<boolean> => {
    const computedHash = await defaultCrypto.hash(password + salt, {
      algorithm: "SHA256",
    });
    return computedHash === hash;
  },

  /**
   * Verify a password synchronously against a hash
   */
  verifySync: (password: string, hash: string, salt: string): boolean => {
    const computedHash = defaultCrypto.hashSync(password + salt, {
      algorithm: "SHA256",
    });
    return computedHash === hash;
  },
};

/**
 * Data encryption utilities for sensitive information
 */
export const dataEncryption = {
  /**
   * Encrypt sensitive user data (PII, etc.)
   */
  encryptUserData: async (data: object | string): Promise<string> => {
    const dataString = typeof data === "string" ? data : JSON.stringify(data);
    return await crypto.encrypt(dataString);
  },

  /**
   * Decrypt sensitive user data
   */
  decryptUserData: async <T = any>(encryptedData: string): Promise<T> => {
    const decryptedString = await crypto.decrypt(encryptedData);
    try {
      return JSON.parse(decryptedString);
    } catch {
      return decryptedString as T;
    }
  },

  /**
   * Encrypt API tokens or secrets
   */
  encryptToken: async (token: string): Promise<string> => {
    return await crypto.encrypt(token);
  },

  /**
   * Decrypt API tokens or secrets
   */
  decryptToken: async (encryptedToken: string): Promise<string> => {
    return await crypto.decrypt(encryptedToken);
  },

  /**
   * Encrypt database fields
   */
  encryptField: async (value: string, fieldKey?: string): Promise<string> => {
    const key = fieldKey || defaultCrypto.getConfig().key;
    const result = await defaultCrypto.encrypt(value, { key });
    return result.encrypted;
  },

  /**
   * Decrypt database fields
   */
  decryptField: async (
    encryptedValue: string,
    fieldKey?: string
  ): Promise<string> => {
    const key = fieldKey || defaultCrypto.getConfig().key;
    return await defaultCrypto.decrypt(encryptedValue, key);
  },
};

/**
 * Session and JWT utilities
 */
export const session = {
  /**
   * Create a secure session token
   */
  createToken: async (payload: object): Promise<string> => {
    const tokenData = {
      ...payload,
      timestamp: Date.now(),
      nonce: defaultCrypto.generateSecureKey(16),
    };
    return await crypto.encrypt(JSON.stringify(tokenData));
  },

  /**
   * Verify and decode a session token
   */
  verifyToken: async <T = any>(token: string): Promise<T | null> => {
    try {
      const decryptedData = await crypto.decrypt(token);
      const tokenData = JSON.parse(decryptedData);

      // Check if token is expired (optional - add expiry logic here)
      // const isExpired = Date.now() - tokenData.timestamp > TOKEN_EXPIRY;
      // if (isExpired) return null;

      return tokenData;
    } catch {
      return null;
    }
  },

  /**
   * Generate a secure session ID
   */
  generateSessionId: (): string => {
    return defaultCrypto.generateSecureKey(32);
  },
};

/**
 * File encryption utilities
 */
export const fileEncryption = {
  /**
   * Encrypt file content
   */
  encryptFile: async (content: Buffer | string): Promise<string> => {
    const contentString =
      content instanceof Buffer ? content.toString("base64") : content;
    return await crypto.encrypt(contentString);
  },

  /**
   * Decrypt file content
   */
  decryptFile: async (encryptedContent: string): Promise<string> => {
    const decryptedString = await crypto.decrypt(encryptedContent);
    return decryptedString; // Return as base64 string instead of Buffer
  },
};

/**
 * Export the main CryptoLibrary class for advanced usage
 */
export { CryptoLibrary };

/**
 * Export default instance for simple usage
 */
export default defaultCrypto;
