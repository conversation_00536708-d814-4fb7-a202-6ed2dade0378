/**
 * Practical examples of using the crypto library in the application
 */

import { crypto, dataEncryption, session } from "./utils";
import { CryptoMiddleware, DatabaseCrypto } from "./middleware";

/**
 * Example: User authentication with encrypted passwords
 */
export class AuthCryptoExample {
  /**
   * Register a new user with encrypted password
   */
  static async registerUser(email: string, plainPassword: string) {
    // Hash the password
    const { hashedPassword, salt } = await CryptoMiddleware.hashPassword(
      plainPassword
    );

    // Encrypt sensitive user data
    const encryptedEmail = await dataEncryption.encryptUserData(email);

    // Save to database (example)
    const userData = {
      email: encryptedEmail,
      password: hashedPassword,
      salt,
      createdAt: new Date(),
    };

    console.log("User registration data:", userData);
    return userData;
  }

  /**
   * Authenticate user with password verification
   */
  static async authenticateUser(
    email: string,
    plainPassword: string,
    storedData: {
      password: string;
      salt: string;
      email: string;
    }
  ) {
    // Decrypt stored email for comparison
    const decryptedEmail = await dataEncryption.decryptUserData<string>(
      storedData.email
    );

    if (decryptedEmail !== email) {
      return { success: false, error: "Invalid credentials" };
    }

    // Verify password
    const isValidPassword = await CryptoMiddleware.verifyPassword(
      plainPassword,
      storedData.password,
      storedData.salt
    );

    if (!isValidPassword) {
      return { success: false, error: "Invalid credentials" };
    }

    // Create session token
    const sessionToken = await CryptoMiddleware.createSessionToken({
      userId: "user-id",
      email: decryptedEmail,
    });

    return {
      success: true,
      sessionToken,
      user: { email: decryptedEmail },
    };
  }
}

/**
 * Example: Proposal data encryption
 */
export class ProposalCryptoExample {
  /**
   * Encrypt sensitive proposal data before saving
   */
  static async encryptProposalData(proposalData: {
    name: string;
    description: string;
    clientEmail?: string;
    notes?: string;
  }) {
    // Define which fields should be encrypted
    const sensitiveFields: (keyof typeof proposalData)[] = [
      "description",
      "clientEmail",
      "notes",
    ];

    return await DatabaseCrypto.encryptFields(proposalData, sensitiveFields);
  }

  /**
   * Decrypt proposal data after retrieval
   */
  static async decryptProposalData(encryptedProposal: {
    name: string;
    description: string;
    clientEmail?: string;
    notes?: string;
  }) {
    // Define which fields should be decrypted
    const sensitiveFields: (keyof typeof encryptedProposal)[] = [
      "description",
      "clientEmail",
      "notes",
    ];

    return await DatabaseCrypto.decryptFields(
      encryptedProposal,
      sensitiveFields
    );
  }

  /**
   * Create encrypted proposal attachment metadata
   */
  static async encryptAttachmentMetadata(metadata: {
    originalName: string;
    path: string;
    size: number;
  }) {
    // Encrypt sensitive metadata
    const encryptedMetadata = await dataEncryption.encryptUserData({
      originalName: metadata.originalName,
      path: metadata.path,
    });

    return {
      encryptedMetadata,
      size: metadata.size, // Size can remain unencrypted
    };
  }
}

/**
 * Example: API token management
 */
export class APITokenExample {
  /**
   * Generate and encrypt API token for external services
   */
  static async generateAPIToken(userId: string, permissions: string[]) {
    const tokenData = {
      userId,
      permissions,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    };

    return await dataEncryption.encryptToken(JSON.stringify(tokenData));
  }

  /**
   * Verify and decode API token
   */
  static async verifyAPIToken(encryptedToken: string): Promise<{
    userId: string;
    permissions: string[];
    createdAt: string;
    expiresAt: string;
  } | null> {
    try {
      const decryptedToken = await dataEncryption.decryptToken(encryptedToken);
      const tokenData = JSON.parse(decryptedToken);

      // Check if token is expired
      if (new Date(tokenData.expiresAt) < new Date()) {
        return null;
      }

      return tokenData;
    } catch {
      return null;
    }
  }
}

/**
 * Example: File encryption for uploads
 */
export class FileEncryptionExample {
  /**
   * Encrypt file before storing
   */
  static async encryptFileForStorage(fileContent: string, fileName: string) {
    // Encrypt file content (assuming it's already base64 encoded)
    const encryptedContent = await dataEncryption.encryptUserData(fileContent);

    // Encrypt file metadata
    const encryptedMetadata = await dataEncryption.encryptUserData({
      originalName: fileName,
      size: fileContent.length,
      uploadedAt: new Date().toISOString(),
    });

    return {
      encryptedContent,
      encryptedMetadata,
    };
  }

  /**
   * Decrypt file for download
   */
  static async decryptFileForDownload(
    encryptedContent: string,
    encryptedMetadata: string
  ) {
    // Decrypt file content
    const contentBase64 = await dataEncryption.decryptUserData<string>(
      encryptedContent
    );

    // Decrypt metadata
    const metadata = await dataEncryption.decryptUserData<{
      originalName: string;
      size: number;
      uploadedAt: string;
    }>(encryptedMetadata);

    return {
      fileContent: contentBase64, // Return as base64 string
      metadata,
    };
  }
}

/**
 * Example: Session management with encryption
 */
export class SessionCryptoExample {
  /**
   * Create encrypted user session
   */
  static async createUserSession(userData: {
    userId: string;
    email: string;
    role: string;
  }) {
    return await session.createToken({
      ...userData,
      sessionId: session.generateSessionId(),
    });
  }

  /**
   * Validate and refresh session
   */
  static async validateSession(sessionToken: string): Promise<{
    isValid: boolean;
    userData?: any;
    needsRefresh?: boolean;
  }> {
    const sessionData = await session.verifyToken(sessionToken);

    if (!sessionData) {
      return { isValid: false };
    }

    // Check if session needs refresh (older than 1 hour)
    const sessionAge = Date.now() - sessionData.timestamp;
    const needsRefresh = sessionAge > 60 * 60 * 1000; // 1 hour

    return {
      isValid: true,
      userData: sessionData,
      needsRefresh,
    };
  }
}

/**
 * Utility functions for common encryption tasks
 */
export const cryptoUtils = {
  /**
   * Encrypt form data before submission
   */
  encryptFormData: async <T extends Record<string, any>>(
    formData: T,
    sensitiveFields: (keyof T)[]
  ): Promise<T> => {
    return await CryptoMiddleware.encryptRequestData(formData, sensitiveFields);
  },

  /**
   * Decrypt form data after retrieval
   */
  decryptFormData: async <T extends Record<string, any>>(
    encryptedData: T,
    sensitiveFields: (keyof T)[]
  ): Promise<T> => {
    return await CryptoMiddleware.decryptResponseData(
      encryptedData,
      sensitiveFields
    );
  },

  /**
   * Generate secure identifiers
   */
  generateSecureId: (): string => {
    return crypto.generateKey(16);
  },

  /**
   * Create data integrity hash
   */
  createIntegrityHash: async (data: any): Promise<string> => {
    const dataString = typeof data === "string" ? data : JSON.stringify(data);
    return await crypto.hash(dataString);
  },

  /**
   * Verify data integrity
   */
  verifyIntegrity: async (
    data: any,
    expectedHash: string
  ): Promise<boolean> => {
    const computedHash = await cryptoUtils.createIntegrityHash(data);
    return computedHash === expectedHash;
  },
};
