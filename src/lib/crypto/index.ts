/**
 * Comprehensive Encryption & Decryption Library
 * Built on top of the cryptography package
 * 
 * This library provides:
 * - Simple encryption/decryption utilities
 * - Password hashing and verification
 * - Data encryption for sensitive information
 * - Session and token management
 * - File encryption capabilities
 * - Security validation utilities
 */

// Main exports
export { CryptoLibrary } from './encryption';
export type {
  EncryptionConfig,
  HashConfig,
  HMACConfig,
  EncryptionResult,
} from './encryption';

// Utility exports
export {
  crypto,
  password,
  dataEncryption,
  session,
  fileEncryption,
} from './utils';

// Configuration exports
export {
  CRYPTO_CONFIG,
  validateCryptoConfig,
  cryptoInstances,
  security,
  validation,
  initializeCrypto,
} from './config';

// Default export for simple usage
export { default as defaultCrypto } from './utils';

/**
 * Quick start examples:
 * 
 * // Simple encryption
 * import { crypto } from '@/lib/crypto';
 * const encrypted = await crypto.encrypt('sensitive data');
 * const decrypted = await crypto.decrypt(encrypted);
 * 
 * // Password hashing
 * import { password } from '@/lib/crypto';
 * const { hash, salt } = await password.hash('user-password');
 * const isValid = await password.verify('user-password', hash, salt);
 * 
 * // Advanced usage
 * import { CryptoLibrary } from '@/lib/crypto';
 * const customCrypto = new CryptoLibrary({
 *   algorithm: 'aes256',
 *   encoding: 'hex',
 *   key: 'your-custom-key'
 * });
 * 
 * // Data encryption
 * import { dataEncryption } from '@/lib/crypto';
 * const encryptedUser = await dataEncryption.encryptUserData({ email: '<EMAIL>' });
 * const userData = await dataEncryption.decryptUserData(encryptedUser);
 */
