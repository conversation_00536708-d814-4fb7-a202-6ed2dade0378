import CryptoJS from "crypto-js";

/**
 * Encryption configuration options
 */
export interface EncryptionConfig {
  algorithm?: "AES";
  encoding?: "base64" | "hex" | "utf8";
  key?: string;
}

/**
 * Hash configuration options
 */
export interface HashConfig {
  algorithm?: "SHA256" | "SHA512" | "SHA1" | "MD5";
  encoding?: "base64" | "hex";
}

/**
 * HMAC configuration options
 */
export interface HMACConfig {
  algorithm?: "SHA256" | "SHA512" | "SHA1" | "MD5";
  encoding?: "base64" | "hex";
  key?: string;
}

/**
 * Encryption result interface
 */
export interface EncryptionResult {
  encrypted: string;
  key: string;
  algorithm: string;
  encoding: string;
}

/**
 * Comprehensive encryption and decryption library
 * Built on top of the crypto-js package
 */
export class CryptoLibrary {
  private defaultConfig: Required<EncryptionConfig>;

  constructor(config: EncryptionConfig = {}) {
    this.defaultConfig = {
      algorithm: config.algorithm || "AES",
      encoding: config.encoding || "base64",
      key: config.key || this.generateSecureKey(),
    };
  }

  /**
   * Generate a secure random key
   */
  generateSecureKey(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString(CryptoJS.enc.Hex);
  }

  /**
   * Generate random bytes as hex string
   */
  generateRandomBytes(length: number = 16): string {
    return CryptoJS.lib.WordArray.random(length).toString(CryptoJS.enc.Hex);
  }

  /**
   * Encrypt data with optional configuration
   */
  async encrypt(
    data: string,
    config: EncryptionConfig = {}
  ): Promise<EncryptionResult> {
    const encryptionConfig = { ...this.defaultConfig, ...config };

    try {
      const encrypted = CryptoJS.AES.encrypt(
        data,
        encryptionConfig.key
      ).toString();

      return {
        encrypted,
        key: encryptionConfig.key,
        algorithm: encryptionConfig.algorithm,
        encoding: encryptionConfig.encoding,
      };
    } catch (error) {
      throw new Error(
        `Encryption failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Encrypt data synchronously
   */
  encryptSync(data: string, config: EncryptionConfig = {}): EncryptionResult {
    const encryptionConfig = { ...this.defaultConfig, ...config };

    try {
      const encrypted = CryptoJS.AES.encrypt(
        data,
        encryptionConfig.key
      ).toString();

      return {
        encrypted,
        key: encryptionConfig.key,
        algorithm: encryptionConfig.algorithm,
        encoding: encryptionConfig.encoding,
      };
    } catch (error) {
      throw new Error(
        `Encryption failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Decrypt data
   */
  async decrypt(
    encryptedData: string,
    key: string,
    config: Partial<EncryptionConfig> = {}
  ): Promise<string> {
    const decryptionConfig = {
      algorithm: config.algorithm || this.defaultConfig.algorithm,
      encoding: config.encoding || this.defaultConfig.encoding,
    };

    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      throw new Error(
        `Decryption failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Decrypt data synchronously
   */
  decryptSync(
    encryptedData: string,
    key: string,
    config: Partial<EncryptionConfig> = {}
  ): string {
    const decryptionConfig = {
      algorithm: config.algorithm || this.defaultConfig.algorithm,
      encoding: config.encoding || this.defaultConfig.encoding,
    };

    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      throw new Error(
        `Decryption failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Hash data
   */
  async hash(data: string, config: HashConfig = {}): Promise<string> {
    const hashConfig = {
      algorithm: config.algorithm || "sha256",
      encoding: config.encoding || "hex",
    };

    try {
      let hash;
      switch (hashConfig.algorithm) {
        case "SHA256":
          hash = CryptoJS.SHA256(data);
          break;
        case "SHA512":
          hash = CryptoJS.SHA512(data);
          break;
        case "SHA1":
          hash = CryptoJS.SHA1(data);
          break;
        case "MD5":
          hash = CryptoJS.MD5(data);
          break;
        default:
          hash = CryptoJS.SHA256(data);
      }

      return hashConfig.encoding === "base64"
        ? hash.toString(CryptoJS.enc.Base64)
        : hash.toString(CryptoJS.enc.Hex);
    } catch (error) {
      throw new Error(
        `Hashing failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Hash data synchronously
   */
  hashSync(data: string, config: HashConfig = {}): string {
    const hashConfig = {
      algorithm: config.algorithm || "sha256",
      encoding: config.encoding || "hex",
    };

    try {
      let hash;
      switch (hashConfig.algorithm) {
        case "SHA256":
          hash = CryptoJS.SHA256(data);
          break;
        case "SHA512":
          hash = CryptoJS.SHA512(data);
          break;
        case "SHA1":
          hash = CryptoJS.SHA1(data);
          break;
        case "MD5":
          hash = CryptoJS.MD5(data);
          break;
        default:
          hash = CryptoJS.SHA256(data);
      }

      return hashConfig.encoding === "base64"
        ? hash.toString(CryptoJS.enc.Base64)
        : hash.toString(CryptoJS.enc.Hex);
    } catch (error) {
      throw new Error(
        `Hashing failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Generate HMAC
   */
  async hmac(data: string, config: HMACConfig = {}): Promise<string> {
    const hmacConfig = {
      algorithm: config.algorithm || "sha256",
      encoding: config.encoding || "hex",
      key: config.key || this.defaultConfig.key,
    };

    try {
      let hmac;
      switch (hmacConfig.algorithm) {
        case "SHA256":
          hmac = CryptoJS.HmacSHA256(data, hmacConfig.key);
          break;
        case "SHA512":
          hmac = CryptoJS.HmacSHA512(data, hmacConfig.key);
          break;
        case "SHA1":
          hmac = CryptoJS.HmacSHA1(data, hmacConfig.key);
          break;
        case "MD5":
          hmac = CryptoJS.HmacMD5(data, hmacConfig.key);
          break;
        default:
          hmac = CryptoJS.HmacSHA256(data, hmacConfig.key);
      }

      return hmacConfig.encoding === "base64"
        ? hmac.toString(CryptoJS.enc.Base64)
        : hmac.toString(CryptoJS.enc.Hex);
    } catch (error) {
      throw new Error(
        `HMAC generation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Generate HMAC synchronously
   */
  hmacSync(data: string, config: HMACConfig = {}): string {
    const hmacConfig = {
      algorithm: config.algorithm || "sha256",
      encoding: config.encoding || "hex",
      key: config.key || this.defaultConfig.key,
    };

    try {
      let hmac;
      switch (hmacConfig.algorithm) {
        case "SHA256":
          hmac = CryptoJS.HmacSHA256(data, hmacConfig.key);
          break;
        case "SHA512":
          hmac = CryptoJS.HmacSHA512(data, hmacConfig.key);
          break;
        case "SHA1":
          hmac = CryptoJS.HmacSHA1(data, hmacConfig.key);
          break;
        case "MD5":
          hmac = CryptoJS.HmacMD5(data, hmacConfig.key);
          break;
        default:
          hmac = CryptoJS.HmacSHA256(data, hmacConfig.key);
      }

      return hmacConfig.encoding === "base64"
        ? hmac.toString(CryptoJS.enc.Base64)
        : hmac.toString(CryptoJS.enc.Hex);
    } catch (error) {
      throw new Error(
        `HMAC generation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Verify HMAC
   */
  async verifyHMAC(
    data: string,
    expectedHMAC: string,
    config: HMACConfig = {}
  ): Promise<boolean> {
    try {
      const computedHMAC = await this.hmac(data, config);
      return computedHMAC === expectedHMAC;
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify HMAC synchronously
   */
  verifyHMACSync(
    data: string,
    expectedHMAC: string,
    config: HMACConfig = {}
  ): boolean {
    try {
      const computedHMAC = this.hmacSync(data, config);
      return computedHMAC === expectedHMAC;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<EncryptionConfig> {
    return { ...this.defaultConfig };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<EncryptionConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }
}
