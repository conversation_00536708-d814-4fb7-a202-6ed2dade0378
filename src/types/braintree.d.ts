declare module "braintree" {
  export interface BraintreeGateway {
    clientToken: {
      generate(
        request?: any
      ): Promise<{ success: boolean; clientToken: string; message?: string }>;
    };
    customer: {
      create(
        request: any
      ): Promise<{ success: boolean; customer: any; message?: string }>;
      find(customerId: string): Promise<any>;
    };
    paymentMethod: {
      create(
        request: any
      ): Promise<{ success: boolean; paymentMethod: any; message?: string }>;
      find(paymentMethodToken: string): Promise<any>;
      update(
        paymentMethodToken: string,
        request: any
      ): Promise<{ success: boolean; paymentMethod: any; message?: string }>;
    };
    transaction: {
      sale(
        request: any
      ): Promise<{ success: boolean; transaction: any; message?: string }>;
      void(
        transactionId: string
      ): Promise<{ success: boolean; transaction: any; message?: string }>;
      refund(
        transactionId: string,
        amount?: string
      ): Promise<{ success: boolean; transaction: any; message?: string }>;
      find(transactionId: string): Promise<any>;
    };
    webhookNotification: {
      parse(signature: string, payload: string): Promise<any>;
    };
  }

  export interface BraintreeGatewayConfig {
    environment: Environment;
    merchantId: string;
    publicKey: string;
    privateKey: string;
  }

  export enum Environment {
    Sandbox = "sandbox",
    Production = "production",
  }

  export class BraintreeGateway {
    constructor(config: BraintreeGatewayConfig);
  }

  export { Environment };
}
