// SEO Configuration for TenderBank
export const seoConfig = {
  // Site information
  site: {
    name: "TenderBank",
    title: "TenderBank - Professional Tender & Bid Management Services in Tanzania",
    description: "Leading tender management company in Tanzania providing expert bid writing, compliance services, and training.",
    url: "https://tenderbank.co.tz",
    logo: "https://tenderbank.co.tz/wp-content/uploads/2025/01/cropped-tbArtboard-6.png",
    favicon: "/favicon.ico",
  },

  // Business information
  business: {
    name: "TenderBank",
    legalName: "TenderBank Limited",
    type: "Professional Services",
    industry: "Business Consulting",
    founded: "2020",
    employees: "10-50",
    address: {
      street: "Sinza-Kijitonyama",
      city: "Dar es Salaam",
      region: "Dar es Salaam",
      country: "Tanzania",
      countryCode: "TZ",
      coordinates: {
        latitude: -6.7924,
        longitude: 39.2083,
      },
    },
    contact: {
      phone: "+************",
      email: "<EMAIL>",
      website: "https://tenderbank.co.tz",
    },
    hours: {
      monday: "08:30-17:30",
      tuesday: "08:30-17:30",
      wednesday: "08:30-17:30",
      thursday: "08:30-17:30",
      friday: "08:30-17:30",
      saturday: "Closed",
      sunday: "Closed",
    },
    socialMedia: {
      linkedin: "https://www.linkedin.com/company/tenderbank-tanzania",
      twitter: "https://twitter.com/tenderbank_tz",
      facebook: "https://www.facebook.com/tenderbank.tanzania",
    },
  },

  // Core services
  services: [
    {
      name: "Bid Management Services",
      description: "Comprehensive tender and bid management from opportunity identification to contract award",
      keywords: ["bid management", "tender management", "procurement consulting"],
    },
    {
      name: "Training Services",
      description: "Professional training on tender writing, procurement processes, and compliance",
      keywords: ["tender training", "procurement training", "bid writing workshops"],
    },
    {
      name: "Compliance Services",
      description: "Ensuring tender submissions meet all regulatory and client requirements",
      keywords: ["procurement compliance", "tender compliance", "regulatory compliance"],
    },
  ],

  // Target keywords by priority
  keywords: {
    primary: [
      "tender management Tanzania",
      "bid writing services",
      "procurement consulting",
      "government tenders Tanzania",
    ],
    secondary: [
      "tender training Tanzania",
      "bid management services",
      "procurement compliance",
      "tender writing training",
    ],
    longTail: [
      "professional tender management services Dar es Salaam",
      "government procurement consulting Tanzania",
      "bid writing training workshops Tanzania",
      "tender compliance services East Africa",
    ],
  },

  // Target locations
  locations: [
    "Tanzania",
    "Dar es Salaam",
    "Dodoma",
    "Mwanza",
    "Arusha",
    "Mbeya",
    "Morogoro",
    "Tanga",
    "East Africa",
  ],

  // Competitor analysis
  competitors: [
    "procurement consultants Tanzania",
    "bid writing services East Africa",
    "tender management companies",
    "business development consultants",
  ],

  // Content categories
  contentCategories: [
    {
      name: "Tender Opportunities",
      description: "Latest government and private sector tender opportunities",
      keywords: ["tender opportunities", "government tenders", "private tenders"],
    },
    {
      name: "Bid Writing Tips",
      description: "Expert advice on writing winning bids and proposals",
      keywords: ["bid writing tips", "proposal writing", "tender success"],
    },
    {
      name: "Procurement News",
      description: "Latest news and updates in procurement and tendering",
      keywords: ["procurement news", "tender updates", "industry news"],
    },
    {
      name: "Training Resources",
      description: "Educational materials and training resources",
      keywords: ["training materials", "procurement guides", "tender templates"],
    },
  ],

  // SEO settings
  seo: {
    titleTemplate: "%s | TenderBank Tanzania",
    defaultTitle: "TenderBank - Professional Tender & Bid Management Services in Tanzania",
    titleSeparator: " | ",
    maxTitleLength: 60,
    maxDescriptionLength: 160,
    
    // Default meta tags
    defaultMeta: {
      robots: "index,follow",
      viewport: "width=device-width,initial-scale=1",
      charset: "utf-8",
      language: "en",
      author: "TenderBank Team",
      publisher: "TenderBank",
      copyright: "© 2025 TenderBank. All rights reserved.",
    },

    // Open Graph defaults
    openGraph: {
      type: "website",
      locale: "en_US",
      siteName: "TenderBank",
      imageWidth: 1200,
      imageHeight: 630,
    },

    // Twitter defaults
    twitter: {
      card: "summary_large_image",
      site: "@tenderbank_tz",
      creator: "@tenderbank_tz",
    },
  },

  // Analytics and tracking
  analytics: {
    googleAnalytics: "G-XXXXXXXXXX", // Replace with actual GA4 ID
    googleTagManager: "GTM-XXXXXXX", // Replace with actual GTM ID
    facebookPixel: "XXXXXXXXXXXXXXX", // Replace with actual Pixel ID
    linkedInInsight: "XXXXXXX", // Replace with actual Partner ID
  },

  // Sitemap configuration
  sitemap: {
    baseUrl: "https://tenderbank.co.tz",
    changeFrequency: {
      home: "weekly",
      services: "monthly",
      about: "yearly",
      contact: "yearly",
      blog: "weekly",
      resources: "monthly",
    },
    priority: {
      home: 1.0,
      services: 0.9,
      about: 0.7,
      contact: 0.6,
      blog: 0.8,
      resources: 0.7,
    },
  },

  // Robots.txt configuration
  robots: {
    allow: [
      "/",
      "/about",
      "/services",
      "/contact",
      "/training",
      "/blog",
      "/resources",
    ],
    disallow: [
      "/admin",
      "/dashboard",
      "/api",
      "/private",
      "/_next",
      "/wp-admin",
      "/wp-content/uploads",
    ],
    sitemap: "https://tenderbank.co.tz/sitemap.xml",
    crawlDelay: 1,
  },
};

// Utility functions
export function generateTitle(pageTitle?: string): string {
  if (!pageTitle) return seoConfig.seo.defaultTitle;
  
  const title = `${pageTitle}${seoConfig.seo.titleSeparator}${seoConfig.site.name}`;
  return title.length > seoConfig.seo.maxTitleLength 
    ? pageTitle 
    : title;
}

export function truncateDescription(description: string): string {
  return description.length > seoConfig.seo.maxDescriptionLength
    ? `${description.substring(0, seoConfig.seo.maxDescriptionLength - 3)}...`
    : description;
}

export function generateCanonicalUrl(path: string): string {
  return `${seoConfig.site.url}${path.startsWith('/') ? path : `/${path}`}`;
}

export function generateImageUrl(imagePath: string): string {
  if (imagePath.startsWith('http')) return imagePath;
  return `${seoConfig.site.url}${imagePath.startsWith('/') ? imagePath : `/${imagePath}`}`;
}
