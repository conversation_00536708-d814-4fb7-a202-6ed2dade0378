import type { Metadata } from "next";

// Base metadata that extends the main site metadata
const baseMetadata = {
  metadataBase: new URL("https://tenderbank.co.tz"),
  alternates: {
    canonical: "https://tenderbank.co.tz",
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
};

// Home page metadata
export const homeMetadata: Metadata = {
  ...baseMetadata,
  title: "TenderBank - Professional Tender & Bid Management Services in Tanzania",
  description:
    "Leading tender management company in Tanzania. Expert bid writing, compliance services, and training. Win more government and private sector contracts with our professional solutions.",
  keywords: [
    "tender management Tanzania",
    "bid writing services",
    "government tenders",
    "procurement consulting",
    "tender training",
    "compliance services",
  ],
  openGraph: {
    title: "TenderBank - Professional Tender Management Services",
    description: "Win more tenders with expert bid writing and compliance services in Tanzania",
    url: "https://tenderbank.co.tz",
    type: "website",
  },
};

// About page metadata
export const aboutMetadata: Metadata = {
  ...baseMetadata,
  title: "About TenderBank - Leading Tender Management Experts in Tanzania",
  description:
    "Learn about TenderBank's mission to help Tanzanian businesses succeed in government and private sector procurement. Our expert team provides comprehensive tender management solutions.",
  alternates: {
    canonical: "https://tenderbank.co.tz/about",
  },
  openGraph: {
    title: "About TenderBank - Tender Management Experts",
    description: "Leading tender management company helping businesses win more contracts in Tanzania",
    url: "https://tenderbank.co.tz/about",
    type: "website",
  },
};

// Services page metadata
export const servicesMetadata: Metadata = {
  ...baseMetadata,
  title: "Tender Management Services - Bid Writing, Training & Compliance | TenderBank",
  description:
    "Comprehensive tender management services including bid writing, procurement training, and compliance consulting. Professional solutions to help you win more contracts in Tanzania.",
  keywords: [
    "bid management services",
    "tender writing training",
    "procurement compliance",
    "tender submission support",
    "contract management",
    "supplier development",
  ],
  alternates: {
    canonical: "https://tenderbank.co.tz/services",
  },
  openGraph: {
    title: "Professional Tender Management Services",
    description: "Expert bid writing, training, and compliance services to help you win more tenders",
    url: "https://tenderbank.co.tz/services",
    type: "website",
  },
};

// Contact page metadata
export const contactMetadata: Metadata = {
  ...baseMetadata,
  title: "Contact TenderBank - Get Expert Tender Management Support",
  description:
    "Contact TenderBank for professional tender management services in Tanzania. Located in Dar es Salaam, we provide expert bid writing and compliance support nationwide.",
  alternates: {
    canonical: "https://tenderbank.co.tz/contact",
  },
  openGraph: {
    title: "Contact TenderBank - Tender Management Experts",
    description: "Get in touch with Tanzania's leading tender management consultancy",
    url: "https://tenderbank.co.tz/contact",
    type: "website",
  },
};

// Training page metadata
export const trainingMetadata: Metadata = {
  ...baseMetadata,
  title: "Tender Writing Training & Procurement Workshops | TenderBank Tanzania",
  description:
    "Professional tender writing training and procurement workshops in Tanzania. Learn from experts and improve your bid success rate with hands-on training programs.",
  keywords: [
    "tender writing training",
    "procurement workshops",
    "bid writing course",
    "tender training Tanzania",
    "procurement training",
    "business development training",
  ],
  alternates: {
    canonical: "https://tenderbank.co.tz/training",
  },
  openGraph: {
    title: "Professional Tender Writing Training",
    description: "Expert-led training programs to improve your tender success rate",
    url: "https://tenderbank.co.tz/training",
    type: "website",
  },
};

// Blog/Resources page metadata
export const blogMetadata: Metadata = {
  ...baseMetadata,
  title: "Tender Management Resources & Insights | TenderBank Blog",
  description:
    "Expert insights, tips, and resources on tender management, bid writing, and procurement in Tanzania. Stay updated with the latest industry trends and best practices.",
  keywords: [
    "tender management tips",
    "bid writing guide",
    "procurement insights",
    "tender opportunities",
    "business development resources",
  ],
  alternates: {
    canonical: "https://tenderbank.co.tz/blog",
  },
  openGraph: {
    title: "Tender Management Resources & Insights",
    description: "Expert tips and insights on winning more tenders and improving bid success",
    url: "https://tenderbank.co.tz/blog",
    type: "website",
  },
};

// Dashboard metadata (for logged-in users)
export const dashboardMetadata: Metadata = {
  title: "Dashboard - TenderBank Management Portal",
  description: "Manage your tender opportunities, track bid progress, and access resources in your TenderBank dashboard.",
  robots: {
    index: false,
    follow: false,
  },
};

// Utility function to generate page-specific metadata
export function generatePageMetadata(
  page: "home" | "about" | "services" | "contact" | "training" | "blog" | "dashboard"
): Metadata {
  const metadataMap = {
    home: homeMetadata,
    about: aboutMetadata,
    services: servicesMetadata,
    contact: contactMetadata,
    training: trainingMetadata,
    blog: blogMetadata,
    dashboard: dashboardMetadata,
  };

  return metadataMap[page] || homeMetadata;
}

// SEO utility functions
export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };
}
