# Authentication & Authorization

The template includes a comprehensive authentication and authorization system built with NextAuth.js and a custom Role-Based Access Control (RBAC) implementation.

## Features

- **Multiple Authentication Methods**
  - OAuth providers (Google, GitHub, etc.)
  - Email/password credentials
  - Session management
- **Role-Based Access Control (RBAC)**
  - Dynamic permissions system
  - Entity-based access control
  - Route protection
- **User Management**
  - Profile management
  - Avatar upload
  - Account linking

## Architecture

### Core Components

1. **NextAuth.js Configuration** (`src/lib/auth.ts`)
   - Provider configuration
   - Session callbacks
   - JWT handling

2. **Auth Hook** (`src/hooks/useAuth.ts`)
   - Authentication state management
   - Login/logout functions
   - User profile operations

3. **RBAC System** (`src/lib/rbac/`)
   - Permission definitions
   - Access control logic
   - Component-level protection

4. **Auth Store** (`src/store/slices/auth.ts`)
   - Redux state management
   - Session persistence
   - User data caching

## Usage

### Basic Authentication

```tsx
import { useAuth } from '@/hooks/useAuth';

function LoginComponent() {
  const { loginWithCredentials, loginWithOAuth, isAuthenticated } = useAuth();

  const handleLogin = async () => {
    try {
      await loginWithCredentials('<EMAIL>', 'password');
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleOAuthLogin = async () => {
    try {
      await loginWithOAuth('google');
    } catch (error) {
      console.error('OAuth login failed:', error);
    }
  };

  if (isAuthenticated) {
    return <div>Welcome!</div>;
  }

  return (
    <div>
      <button onClick={handleLogin}>Login with Email</button>
      <button onClick={handleOAuthLogin}>Login with Google</button>
    </div>
  );
}
```

### Role-Based Access Control

```tsx
import { RBACWrapper } from '@/lib/rbac/context';
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from '@/lib/rbac/permissions';

function ProtectedComponent() {
  return (
    <RBACWrapper
      entity={DEFAULT_ENTITIES.DOCUMENT}
      action={PERMISSION_ACTIONS.CREATE}
    >
      <button>Create Document</button>
    </RBACWrapper>
  );
}
```

### Route Protection

```tsx
// In your page component
import { useAuth } from '@/hooks/useAuth';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

function ProtectedPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/signin');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return null;

  return <div>Protected content</div>;
}
```

## Configuration

### Environment Variables

```env
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3030"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Adding OAuth Providers

1. Configure the provider in `src/lib/auth.ts`:

```typescript
import GoogleProvider from "next-auth/providers/google";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // Add more providers here
  ],
  // ... other configuration
};
```

2. Add environment variables for the new provider
3. Update the login UI to include the new provider

### Custom Permissions

1. Define new entities in `src/lib/rbac/permissions.ts`:

```typescript
export const CUSTOM_ENTITIES = {
  CUSTOM_RESOURCE: 'custom_resource',
} as const;
```

2. Add permission actions:

```typescript
export const CUSTOM_ACTIONS = {
  CUSTOM_ACTION: 'custom_action',
} as const;
```

3. Use in components:

```tsx
<RBACWrapper
  entity={CUSTOM_ENTITIES.CUSTOM_RESOURCE}
  action={CUSTOM_ACTIONS.CUSTOM_ACTION}
>
  <CustomComponent />
</RBACWrapper>
```

## Database Schema

The authentication system uses the following database models:

- `User` - User account information
- `Account` - OAuth account linking
- `Session` - User sessions
- `Role` - User roles and permissions
- `Profile` - Extended user profile data

## Security Considerations

1. **Session Security**
   - Secure session cookies
   - CSRF protection
   - Session expiration

2. **Password Security**
   - Bcrypt hashing
   - Password strength validation
   - Rate limiting

3. **OAuth Security**
   - State parameter validation
   - Secure redirect URIs
   - Token validation

## API Endpoints

- `POST /api/auth/signin` - Sign in with credentials
- `POST /api/auth/signout` - Sign out
- `GET /api/auth/session` - Get current session
- `POST /api/auth/callback/[provider]` - OAuth callbacks
- `GET /api/profile` - Get user profile
- `PUT /api/profile` - Update user profile

## Troubleshooting

### Common Issues

1. **OAuth redirect mismatch**
   - Ensure redirect URIs match in provider settings
   - Check NEXTAUTH_URL configuration

2. **Session not persisting**
   - Verify NEXTAUTH_SECRET is set
   - Check cookie settings

3. **Permission denied errors**
   - Verify user roles and permissions
   - Check RBAC configuration

### Debug Mode

Enable debug logging by setting:

```env
NEXTAUTH_DEBUG=true
```

This will provide detailed logs for authentication flows and help identify issues.
