# Configuration Guide

This guide covers all configuration options available in the PWA template.

## Environment Variables

### Required Variables

```env
# Database Connection
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Authentication
NEXTAUTH_SECRET="your-secure-random-string-at-least-32-characters"
NEXTAUTH_URL="http://localhost:3030"  # Your app URL
```

### Authentication Providers

```env
# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Discord OAuth (optional)
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"
```

### Email Configuration

```env
# SMTP Settings
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"  # true for 465, false for other ports
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"  # Use app password for Gmail

# Email Templates
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Your App Name"
```

### Push Notifications

```env
# VAPID Keys (generate with scripts/generate-vapid-keys.js)
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"
VAPID_SUBJECT="mailto:<EMAIL>"
```

### Payment Processing

```env
# Braintree Configuration
BRAINTREE_ENVIRONMENT="sandbox"  # or "production"
BRAINTREE_MERCHANT_ID="your-merchant-id"
BRAINTREE_PUBLIC_KEY="your-public-key"
BRAINTREE_PRIVATE_KEY="your-private-key"
```

### File Storage

```env
# AWS S3 Configuration (optional)
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-bucket-name"

# Or use local file storage
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="10485760"  # 10MB in bytes
```

### Real-time Features

```env
# Socket.io Configuration
SOCKET_IO_PORT="3001"
SOCKET_IO_CORS_ORIGIN="http://localhost:3030"

# Pusher Configuration (alternative to Socket.io)
PUSHER_APP_ID="your-app-id"
PUSHER_KEY="your-key"
PUSHER_SECRET="your-secret"
PUSHER_CLUSTER="your-cluster"
```

### Development & Debugging

```env
# Development
NODE_ENV="development"  # or "production"
DEBUG="true"
LOG_LEVEL="info"  # error, warn, info, debug

# Analytics (optional)
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
MIXPANEL_TOKEN="your-mixpanel-token"
```

## Application Configuration

### Next.js Configuration

```javascript
// next.config.js
const nextConfig = {
  // Basic configuration
  reactStrictMode: true,
  swcMinify: true,

  // Image optimization
  images: {
    domains: ["example.com", "cdn.example.com"],
    formats: ["image/webp", "image/avif"],
  },

  // Internationalization
  i18n: {
    locales: ["en", "es", "fr"],
    defaultLocale: "en",
  },

  // Headers for security
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
        ],
      },
    ];
  },

  // Redirects
  async redirects() {
    return [
      {
        source: "/old-path",
        destination: "/new-path",
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
```

### Database Configuration

```javascript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"  // or "mysql", "sqlite"
  url      = env("DATABASE_URL")
}

// Connection pooling for production
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")  // For migrations
}
```

### Authentication Configuration

```typescript
// src/lib/auth.ts
import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        // Your authentication logic
        return user;
      },
    }),
  ],

  // Session configuration
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  // JWT configuration
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  // Pages configuration
  pages: {
    signIn: "/signin",
    signUp: "/signup",
    error: "/auth/error",
  },

  // Callbacks
  callbacks: {
    async jwt({ token, user, account }) {
      // Customize JWT token
      return token;
    },
    async session({ session, token }) {
      // Customize session object
      return session;
    },
  },
};
```

### Theme Configuration

```typescript
// src/providers/theme.tsx
export const themeConfig = {
  defaultTheme: "system" as const,
  storageKey: "app-theme",
  themes: ["light", "dark", "system"] as const,

  // Custom theme colors
  colors: {
    light: {
      primary: "221.2 83.2% 53.3%",
      secondary: "210 40% 96%",
      background: "0 0% 100%",
      foreground: "222.2 84% 4.9%",
    },
    dark: {
      primary: "217.2 91.2% 59.8%",
      secondary: "217.2 32.6% 17.5%",
      background: "222.2 84% 4.9%",
      foreground: "210 40% 98%",
    },
  },
};
```

### Notification Configuration

```typescript
// src/lib/notifications/config.ts
export const notificationConfig = {
  // Default preferences for new users
  defaultPreferences: {
    chat: { email: true, inApp: true, push: true },
    system: { email: true, inApp: true, push: false },
    marketing: { email: false, inApp: false, push: false },
  },

  // Email templates directory
  templatesDir: "./src/lib/mail/templates",

  // Push notification settings
  push: {
    vapidSubject: process.env.VAPID_SUBJECT,
    ttl: 24 * 60 * 60, // 24 hours
    urgency: "normal" as const,
  },

  // Rate limiting
  rateLimit: {
    email: { max: 10, window: 60 * 1000 }, // 10 per minute
    push: { max: 5, window: 60 * 1000 }, // 5 per minute
    inApp: { max: 20, window: 60 * 1000 }, // 20 per minute
  },
};
```

### API Configuration

```typescript
// src/lib/api/config.ts
export const apiConfig = {
  // Base service configuration
  baseService: {
    requireAuth: true,
    enableLogging: true,
    validateInput: true,
    validateOutput: false,
    timeout: 30000, // 30 seconds
  },

  // Pagination defaults
  pagination: {
    defaultLimit: 10,
    maxLimit: 100,
    defaultSort: "createdAt",
    defaultOrder: "desc" as const,
  },

  // File upload limits
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "image/jpeg",
      "image/png",
      "image/webp",
      "application/pdf",
      "text/plain",
    ],
  },

  // CORS settings
  cors: {
    origin:
      process.env.NODE_ENV === "production"
        ? ["https://yourapp.com"]
        : ["http://localhost:3030"],
    credentials: true,
  },
};
```

## PWA Configuration

### App Manifest

```json
// public/manifest.json
{
  "name": "Your App Name",
  "short_name": "Your App",
  "description": "Your app description",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "orientation": "portrait-primary",

  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    }
  ],

  "shortcuts": [
    {
      "name": "Dashboard",
      "url": "/",
      "icons": [
        {
          "src": "/icons/dashboard-96x96.png",
          "sizes": "96x96"
        }
      ]
    }
  ],

  "categories": ["productivity", "business"],
  "lang": "en",
  "dir": "ltr"
}
```

### Service Worker Configuration

```javascript
// public/sw.js configuration
const CONFIG = {
  CACHE_NAME: "pwa-template-v1",
  CACHE_URLS: ["/", "/dashboard", "/offline", "/manifest.json"],

  // Cache strategies
  STRATEGIES: {
    pages: "NetworkFirst",
    assets: "CacheFirst",
    api: "NetworkOnly",
  },

  // Background sync
  SYNC_TAGS: {
    notifications: "notification-sync",
    data: "data-sync",
  },
};
```

## Security Configuration

### Content Security Policy

```typescript
// next.config.js
const securityHeaders = [
  {
    key: "Content-Security-Policy",
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self' https://api.example.com",
    ].join("; "),
  },
];
```

### Rate Limiting

```typescript
// src/lib/rate-limit.ts
export const rateLimitConfig = {
  // API rate limits
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
  },

  // Authentication rate limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 requests per windowMs
  },

  // File upload rate limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // limit each IP to 10 uploads per minute
  },
};
```

## Performance Configuration

### Caching Strategy

```typescript
// src/lib/cache/config.ts
export const cacheConfig = {
  // Redis configuration
  redis: {
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379"),
    password: process.env.REDIS_PASSWORD,
  },

  // Cache TTL settings
  ttl: {
    user: 60 * 60, // 1 hour
    session: 24 * 60 * 60, // 24 hours
    static: 7 * 24 * 60 * 60, // 7 days
  },

  // Cache keys
  keys: {
    user: (id: string) => `user:${id}`,
    session: (id: string) => `session:${id}`,
    notifications: (userId: string) => `notifications:${userId}`,
  },
};
```

### Database Optimization

```prisma
// prisma/schema.prisma
model User {
  id    String @id @default(cuid())
  email String @unique
  name  String?

  // Indexes for performance
  @@index([email])
  @@index([createdAt])
}

model Document {
  id     String @id @default(cuid())
  userId String
  name   String

  // Composite indexes
  @@index([userId, createdAt])
  @@index([name, userId])
}
```

## Monitoring Configuration

### Logging

```typescript
// src/lib/logger/config.ts
export const loggerConfig = {
  level: process.env.LOG_LEVEL || "info",
  format: process.env.NODE_ENV === "production" ? "json" : "pretty",

  // Log destinations
  transports: [
    "console",
    ...(process.env.NODE_ENV === "production" ? ["file"] : []),
  ],

  // Log rotation
  rotation: {
    maxFiles: 5,
    maxSize: "10m",
  },
};
```

### Analytics

```typescript
// src/lib/analytics/config.ts
export const analyticsConfig = {
  googleAnalytics: {
    measurementId: process.env.GOOGLE_ANALYTICS_ID,
    enabled: process.env.NODE_ENV === "production",
  },

  mixpanel: {
    token: process.env.MIXPANEL_TOKEN,
    enabled: process.env.NODE_ENV === "production",
  },

  // Custom events to track
  events: {
    userSignup: "user_signup",
    documentUpload: "document_upload",
    notificationSent: "notification_sent",
  },
};
```

## Deployment Configuration

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build application
RUN pnpm build

# Expose port
EXPOSE 3000

# Start application
CMD ["pnpm", "start"]
```

### Environment-Specific Configs

```bash
# .env.development
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug

# .env.production
NODE_ENV=production
DEBUG=false
LOG_LEVEL=error
```
