# Getting Started

This guide will help you set up and customize the PWA template for your project.

## Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- PostgreSQL database (or compatible)
- Git

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd common-pwa-nextjs
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/your_database"

# Authentication
NEXTAUTH_SECRET="your-nextauth-secret-key"
NEXTAUTH_URL="http://localhost:3030"

# OAuth Providers (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Push Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"

# Payments (optional)
BRAINTREE_MERCHANT_ID="your-merchant-id"
BRAINTREE_PUBLIC_KEY="your-public-key"
BRAINTREE_PRIVATE_KEY="your-private-key"
BRAINTREE_ENVIRONMENT="sandbox" # or "production"
```

### 4. Database Setup

Generate Prisma client and set up the database:

```bash
npx prisma generate
npx prisma db push
```

### 5. Generate VAPID Keys (for Push Notifications)

```bash
node scripts/generate-vapid-keys.js
```

Add the generated keys to your `.env.local` file.

### 6. Start Development Server

```bash
pnpm dev
```

Your application will be available at `http://localhost:3030`.

## Project Structure Overview

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (dashboard)/       # Protected dashboard pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── common/           # Shared components
│   └── view/             # Page-specific components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── api/              # API services
│   ├── auth.ts           # Authentication config
│   ├── notifications/    # Notification system
│   └── rbac/             # Role-based access control
├── providers/            # React context providers
├── store/                # Redux store
└── types/                # TypeScript definitions
```

## Basic Configuration

### 1. Update App Information

Edit the app manifest (`public/manifest.json`):

```json
{
  "name": "Your App Name",
  "short_name": "Your App",
  "description": "Your app description",
  "start_url": "/",
  "theme_color": "#your-theme-color"
}
```

### 2. Configure Authentication Providers

Add OAuth providers in `src/lib/auth.ts`:

```typescript
import GoogleProvider from "next-auth/providers/google";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    // Add more providers as needed
  ],
  // ... rest of configuration
};
```

### 3. Customize Theme Colors

Update CSS variables in `src/app/globals.css`:

```css
:root {
  --primary: 221.2 83.2% 53.3%;        /* Your primary color */
  --secondary: 210 40% 96%;             /* Your secondary color */
  --accent: 210 40% 96%;                /* Your accent color */
  /* ... other color variables */
}
```

### 4. Update Branding

Replace the logo and branding in:
- `src/components/common/logo/Brand.tsx`
- `public/icons/` (app icons)
- `public/favicons/` (favicon files)

## Development Workflow

### 1. Creating New Pages

Create pages in the `src/app/` directory using Next.js App Router:

```tsx
// src/app/my-page/page.tsx
export default function MyPage() {
  return (
    <div>
      <h1>My New Page</h1>
    </div>
  );
}
```

### 2. Adding API Endpoints

Create API routes in `src/app/api/`:

```typescript
// src/app/api/my-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'Hello from API' });
}
```

### 3. Creating Components

Add reusable components in `src/components/`:

```tsx
// src/components/common/MyComponent.tsx
interface MyComponentProps {
  title: string;
}

export function MyComponent({ title }: MyComponentProps) {
  return <h2>{title}</h2>;
}
```

### 4. Using Hooks

Leverage the built-in hooks for common functionality:

```tsx
import { useAuth } from '@/hooks/useAuth';
import { useNotifications } from '@/hooks/useNotifications';
import { useTheme } from '@/hooks/useTheme';

function MyComponent() {
  const { user, isAuthenticated } = useAuth();
  const { sendNotification } = useNotifications();
  const { theme, setTheme } = useTheme();

  // Your component logic
}
```

## Database Schema

The template includes pre-configured models for:

- **Users & Authentication**
  - User accounts
  - OAuth providers
  - Sessions
  - Profiles

- **Authorization**
  - Roles
  - Permissions
  - Role assignments

- **Content Management**
  - Documents
  - Proposals
  - Contracts

- **Communication**
  - Chat rooms
  - Messages
  - Notifications

### Adding Custom Models

1. Update `prisma/schema.prisma`:

```prisma
model MyModel {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

2. Generate and apply changes:

```bash
npx prisma generate
npx prisma db push
```

## Customization Examples

### 1. Adding a New Service

```typescript
// src/lib/api/services/my-service.ts
import { BaseService, ServiceResponse } from './base';
import { z } from 'zod';

const MyDataSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
});

export class MyService extends BaseService {
  async createItem(data: z.infer<typeof MyDataSchema>): Promise<ServiceResponse> {
    try {
      const validatedData = this.validateInput(MyDataSchema, data);
      
      const item = await this.prisma.myModel.create({
        data: validatedData,
      });

      return this.success(item, 'Item created successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

### 2. Creating a Custom Hook

```typescript
// src/hooks/useMyFeature.ts
import { useState, useEffect } from 'react';
import { api } from '@/lib/common/requests';

export function useMyFeature() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/my-endpoint');
      setData(response.data);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, refetch: fetchData };
}
```

### 3. Adding Custom Notifications

```typescript
// src/lib/notifications/custom-types.ts
export const CUSTOM_NOTIFICATION_TYPES = {
  CUSTOM_EVENT: 'custom_event',
} as const;

// Usage
import { notify } from '@/lib/notifications';

await notify({
  type: 'custom_event',
  title: 'Custom Event',
  message: 'Something custom happened',
  email: true,
  inApp: true,
  push: false,
});
```

## Testing

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

### Writing Tests

```typescript
// __tests__/components/MyComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { MyComponent } from '@/components/common/MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

## Deployment

### 1. Build the Application

```bash
pnpm build
```

### 2. Environment Variables

Set up production environment variables on your hosting platform.

### 3. Database Migration

Run database migrations in production:

```bash
npx prisma migrate deploy
```

### 4. Deploy

Deploy to your preferred platform (Vercel, Netlify, etc.).

## Next Steps

1. **Customize the UI** - Update colors, fonts, and layouts
2. **Add Features** - Implement your specific business logic
3. **Configure Services** - Set up email, payments, and other integrations
4. **Test Thoroughly** - Write tests for your custom functionality
5. **Deploy** - Launch your application

## Getting Help

- Check the [documentation](./README.md) for detailed guides
- Review the [API documentation](./api-services.md)
- Look at existing components for examples
- Create issues for bugs or feature requests

## Common Issues

### Database Connection Issues
- Verify DATABASE_URL is correct
- Ensure database server is running
- Check network connectivity

### Authentication Problems
- Verify NEXTAUTH_SECRET is set
- Check OAuth provider configuration
- Ensure redirect URLs match

### Build Errors
- Clear `.next` directory and rebuild
- Check for TypeScript errors
- Verify all dependencies are installed

### Service Worker Issues
- Check HTTPS in production
- Verify manifest.json is accessible
- Clear browser cache and service workers
