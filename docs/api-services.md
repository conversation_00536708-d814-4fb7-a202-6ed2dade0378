# REST API Services

The template includes a structured API layer with validation, error handling, and service patterns for building robust backend services.

## Features

- **Service Architecture**
  - Base service class with common functionality
  - Consistent error handling
  - Request/response validation
  - Context management
- **Built-in Services**
  - Document management
  - User profiles
  - Payment processing
  - Role-based access control
- **Validation Layer**
  - Zod schema validation
  - Input sanitization
  - Output formatting
- **Error Handling**
  - Structured error responses
  - Logging integration
  - Client-friendly messages

## Architecture

### Core Components

1. **Base Service** (`src/lib/api/services/base.ts`)
   - Common service functionality
   - Context management
   - Error handling patterns

2. **Service Implementations** (`src/lib/api/services/`)
   - Document service
   - Profile service
   - Braintree service
   - RBAC service

3. **Validation Schemas** (`src/lib/api/validators/`)
   - Zod schemas for data validation
   - Common validation patterns
   - Type generation

4. **API Routes** (`src/app/api/`)
   - Next.js API routes
   - Service integration
   - Request handling

## Base Service Pattern

### Creating a Service

```typescript
import { BaseService, ServiceResponse } from '@/lib/api/services/base';
import { z } from 'zod';

// Define validation schemas
const CreateItemSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
});

const ItemSchema = CreateItemSchema.extend({
  id: z.string().cuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export class ItemService extends BaseService {
  constructor(config?: Partial<BaseServiceConfig>) {
    super({
      requireAuth: true,
      enableLogging: true,
      validateInput: true,
      validateOutput: true,
      ...config,
    });
  }

  async createItem(data: z.infer<typeof CreateItemSchema>): Promise<ServiceResponse> {
    try {
      // Validate input
      const validatedData = this.validateInput(CreateItemSchema, data);

      // Check permissions
      await this.checkPermission('item', 'create');

      // Business logic
      const item = await this.prisma.item.create({
        data: validatedData,
      });

      // Validate output
      const validatedItem = this.validateOutput(ItemSchema, item);

      return this.success(validatedItem, 'Item created successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getItems(filters?: any): Promise<ServiceResponse> {
    try {
      await this.checkPermission('item', 'read');

      const items = await this.prisma.item.findMany({
        where: filters,
        orderBy: { createdAt: 'desc' },
      });

      const validatedItems = items.map(item => 
        this.validateOutput(ItemSchema, item)
      );

      return this.success(validatedItems, 'Items retrieved successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }
}
```

### Using Services in API Routes

```typescript
// src/app/api/items/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { ItemService } from '@/lib/api/services/item';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    const itemService = new ItemService();

    // Set service context
    itemService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const filters = Object.fromEntries(searchParams.entries());

    // Use service
    const result = await itemService.getItems(filters);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 400,
    });
  } catch (error) {
    console.error('Items GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    const itemService = new ItemService();

    itemService.setContext({
      session,
      user: session?.user,
      request,
    });

    const body = await request.json();
    const result = await itemService.createItem(body);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 400,
    });
  } catch (error) {
    console.error('Items POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Built-in Services

### Document Service

```typescript
import { useDocuments } from '@/hooks/useDocuments';

function DocumentComponent() {
  const {
    documents,
    isLoading,
    error,
    createDocument,
    updateDocument,
    deleteDocument,
    refreshData,
  } = useDocuments();

  const handleUpload = async (file: File) => {
    try {
      await createDocument({
        name: file.name,
        file_type: file.type,
        size: file.size.toString(),
        category: 'general',
      });
      refreshData();
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {documents.map(doc => (
        <div key={doc.id}>
          <h3>{doc.name}</h3>
          <p>Type: {doc.file_type}</p>
          <p>Size: {doc.size}</p>
        </div>
      ))}
    </div>
  );
}
```

### Profile Service

```typescript
import { useProfile } from '@/hooks/useProfile';

function ProfileComponent() {
  const {
    profile,
    isLoading,
    updateProfile,
    uploadAvatar,
  } = useProfile();

  const handleUpdateProfile = async (data: any) => {
    try {
      await updateProfile(data);
    } catch (error) {
      console.error('Profile update failed:', error);
    }
  };

  const handleAvatarUpload = async (file: File) => {
    try {
      await uploadAvatar(file);
    } catch (error) {
      console.error('Avatar upload failed:', error);
    }
  };

  return (
    <div>
      <h2>Profile</h2>
      <form onSubmit={(e) => {
        e.preventDefault();
        const formData = new FormData(e.target as HTMLFormElement);
        handleUpdateProfile(Object.fromEntries(formData));
      }}>
        <input name="name" defaultValue={profile?.name} />
        <input name="email" defaultValue={profile?.email} />
        <textarea name="about" defaultValue={profile?.about} />
        <button type="submit">Update Profile</button>
      </form>
    </div>
  );
}
```

## Validation Schemas

### Common Schemas

```typescript
// src/lib/api/validators/schemas/common.ts
import { z } from 'zod';

export const IdSchema = z.string().cuid();
export const RequiredStringSchema = z.string().min(1, 'This field is required');
export const EmailSchema = z.string().email('Invalid email address');
export const UrlSchema = z.string().url('Invalid URL');

export const StatusSchema = z.enum([
  'created',
  'submitted',
  'received',
  'negotiating',
  'agreed',
  'inprogress',
  'reviewing',
  'completed',
]);

export const PaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});
```

### Custom Validation

```typescript
// Custom validation with business logic
const UserRegistrationSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
      'Password must contain uppercase, lowercase, and number'),
}).refine(async (data) => {
  // Check if email already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: data.email }
  });
  return !existingUser;
}, {
  message: 'Email already registered',
  path: ['email'],
});
```

## Error Handling

### Service Error Types

```typescript
export class ServiceError extends Error {
  constructor(
    message: string,
    public statusCode: number = 400,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

export class ValidationError extends ServiceError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class AuthorizationError extends ServiceError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends ServiceError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}
```

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: true;
  message: string;
  code?: string;
  details?: any;
  statusCode: number;
  timestamp: string;
}
```

## Testing Services

```typescript
// __tests__/services/item.test.ts
import { ItemService } from '@/lib/api/services/item';
import { createMockContext } from '@/lib/api/services/__tests__/helpers';

describe('ItemService', () => {
  let service: ItemService;
  let mockContext: any;

  beforeEach(() => {
    service = new ItemService();
    mockContext = createMockContext();
    service.setContext(mockContext);
  });

  describe('createItem', () => {
    it('should create item successfully', async () => {
      const itemData = {
        name: 'Test Item',
        description: 'Test description',
      };

      const result = await service.createItem(itemData);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject(itemData);
    });

    it('should validate input data', async () => {
      const invalidData = {
        name: '', // Invalid: empty string
      };

      const result = await service.createItem(invalidData);

      expect(result.success).toBe(false);
      expect(result.code).toBe('VALIDATION_ERROR');
    });
  });
});
```

## Best Practices

1. **Service Design**
   - Keep services focused on single responsibility
   - Use dependency injection for testability
   - Implement proper error handling

2. **Validation**
   - Validate all inputs at service boundaries
   - Use Zod for type-safe validation
   - Provide clear error messages

3. **Error Handling**
   - Use structured error types
   - Log errors appropriately
   - Return consistent error formats

4. **Performance**
   - Implement pagination for large datasets
   - Use database indexes effectively
   - Cache frequently accessed data

5. **Security**
   - Always validate permissions
   - Sanitize inputs
   - Use parameterized queries
