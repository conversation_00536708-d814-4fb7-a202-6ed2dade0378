# Theme Management

The template includes a comprehensive theming system with light/dark mode support, system preference detection, and customizable color schemes.

## Features

- **Multi-Theme Support**
  - Light theme
  - Dark theme
  - System preference detection
- **Persistent Preferences**
  - Local storage integration
  - User preference sync
  - Cross-session consistency
- **CSS Variables**
  - Dynamic color switching
  - Component-level theming
  - Custom property support
- **Tailwind Integration**
  - Dark mode classes
  - Theme-aware utilities
  - Custom color palette

## Architecture

### Core Components

1. **Theme Provider** (`src/providers/theme.tsx`)
   - Theme state management
   - System preference detection
   - Storage persistence

2. **Theme Hook** (`src/hooks/useTheme.ts`)
   - Theme switching logic
   - Preference management
   - System integration

3. **CSS Variables** (`src/app/globals.css`)
   - Color definitions
   - Theme-specific values
   - Component styling

4. **Tailwind Configuration** (`tailwind.config.js`)
   - Dark mode setup
   - Custom color palette
   - Theme utilities

## Usage

### Basic Theme Switching

```tsx
import { useTheme } from '@/hooks/useTheme';

function ThemeToggle() {
  const { theme, setTheme, actualTheme } = useTheme();

  return (
    <div>
      <p>Current theme: {actualTheme}</p>
      <button onClick={() => setTheme('light')}>Light</button>
      <button onClick={() => setTheme('dark')}>Dark</button>
      <button onClick={() => setTheme('system')}>System</button>
    </div>
  );
}
```

### Theme-Aware Components

```tsx
import { useTheme } from '@/hooks/useTheme';

function ThemedComponent() {
  const { actualTheme } = useTheme();

  return (
    <div className={`
      bg-background text-foreground
      border border-border
      ${actualTheme === 'dark' ? 'shadow-dark' : 'shadow-light'}
    `}>
      <h1 className="text-primary">Themed Content</h1>
      <p className="text-muted-foreground">
        This component adapts to the current theme
      </p>
    </div>
  );
}
```

### Custom Theme Colors

```tsx
// Using CSS variables in components
function CustomCard() {
  return (
    <div style={{
      backgroundColor: 'hsl(var(--card))',
      color: 'hsl(var(--card-foreground))',
      border: '1px solid hsl(var(--border))',
    }}>
      <h2 style={{ color: 'hsl(var(--primary))' }}>
        Custom Themed Card
      </h2>
    </div>
  );
}
```

## Configuration

### CSS Variables

The theme system uses CSS variables defined in `src/app/globals.css`:

```css
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}
```

### Tailwind Configuration

```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class',
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [],
};
```

## Custom Themes

### Creating Custom Color Schemes

1. **Define CSS Variables**

```css
:root {
  /* Custom theme variables */
  --custom-primary: 280 100% 70%;
  --custom-secondary: 320 100% 80%;
  --custom-accent: 200 100% 60%;
}

.custom-theme {
  --primary: var(--custom-primary);
  --secondary: var(--custom-secondary);
  --accent: var(--custom-accent);
}
```

2. **Apply Custom Theme**

```tsx
function CustomThemeProvider({ children }: { children: React.ReactNode }) {
  const [customTheme, setCustomTheme] = useState(false);

  useEffect(() => {
    if (customTheme) {
      document.documentElement.classList.add('custom-theme');
    } else {
      document.documentElement.classList.remove('custom-theme');
    }
  }, [customTheme]);

  return (
    <div>
      <button onClick={() => setCustomTheme(!customTheme)}>
        Toggle Custom Theme
      </button>
      {children}
    </div>
  );
}
```

### Dynamic Theme Generation

```tsx
function DynamicThemeGenerator() {
  const [primaryColor, setPrimaryColor] = useState('#3b82f6');

  const applyDynamicTheme = (color: string) => {
    // Convert hex to HSL
    const hsl = hexToHsl(color);
    
    // Update CSS variables
    document.documentElement.style.setProperty(
      '--primary',
      `${hsl.h} ${hsl.s}% ${hsl.l}%`
    );
    
    // Generate complementary colors
    const secondary = generateComplementary(hsl);
    document.documentElement.style.setProperty(
      '--secondary',
      `${secondary.h} ${secondary.s}% ${secondary.l}%`
    );
  };

  return (
    <div>
      <input
        type="color"
        value={primaryColor}
        onChange={(e) => {
          setPrimaryColor(e.target.value);
          applyDynamicTheme(e.target.value);
        }}
      />
      <p>Primary Color: {primaryColor}</p>
    </div>
  );
}
```

## Theme Persistence

### Local Storage Integration

```tsx
// Theme provider with persistence
export function ThemeProvider({ children, defaultTheme = 'system' }) {
  const [theme, setTheme] = useState<Theme>(defaultTheme);

  useEffect(() => {
    // Load theme from localStorage
    const storedTheme = localStorage.getItem('theme') as Theme;
    if (storedTheme) {
      setTheme(storedTheme);
    }
  }, []);

  useEffect(() => {
    // Save theme to localStorage
    localStorage.setItem('theme', theme);
    
    // Apply theme to document
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
```

### User Preference Sync

```tsx
// Sync theme with user preferences
function useThemeSync() {
  const { theme, setTheme } = useTheme();
  const { user, updateUserProfile } = useAuth();

  // Load theme from user profile
  useEffect(() => {
    if (user?.preferences?.theme) {
      setTheme(user.preferences.theme);
    }
  }, [user, setTheme]);

  // Save theme to user profile
  const saveThemePreference = async (newTheme: Theme) => {
    setTheme(newTheme);
    
    if (user) {
      await updateUserProfile({
        preferences: {
          ...user.preferences,
          theme: newTheme,
        },
      });
    }
  };

  return { theme, setTheme: saveThemePreference };
}
```

## Component Theming

### Theme-Aware UI Components

```tsx
// Button component with theme support
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

function Button({ variant = 'primary', size = 'md', children }: ButtonProps) {
  const baseClasses = 'rounded-md font-medium transition-colors';
  
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  return (
    <button
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
      `}
    >
      {children}
    </button>
  );
}
```

### Dark Mode Specific Styling

```tsx
function DarkModeCard() {
  return (
    <div className="
      bg-card text-card-foreground
      border border-border
      rounded-lg p-6
      shadow-sm
      dark:shadow-lg dark:shadow-black/20
    ">
      <h2 className="text-xl font-semibold mb-2">
        Dark Mode Card
      </h2>
      <p className="text-muted-foreground">
        This card has different shadows in dark mode
      </p>
    </div>
  );
}
```

## Best Practices

1. **Consistent Color Usage**
   - Use semantic color names
   - Maintain contrast ratios
   - Test in both themes

2. **Performance**
   - Minimize theme switching overhead
   - Use CSS variables for dynamic changes
   - Avoid inline styles when possible

3. **Accessibility**
   - Ensure sufficient contrast
   - Support system preferences
   - Provide theme selection options

4. **Maintenance**
   - Keep theme definitions centralized
   - Document custom color meanings
   - Test theme changes thoroughly
