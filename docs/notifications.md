# Multi-Channel Notifications

The template includes a unified notification system that supports email, push notifications, and in-app notifications based on user preferences and notification contexts.

## Features

- **Multi-Channel Support**
  - Email notifications via SMTP
  - Push notifications via Web Push API
  - In-app notifications via toast messages
- **User Preferences**
  - Per-notification-type preferences
  - Channel-specific settings
  - Opt-in/opt-out controls
- **Notification Types**
  - System alerts
  - Chat messages
  - Document updates
  - Role changes
  - Weekly digests

## Architecture

### Core Components

1. **Notification Library** (`src/lib/notifications/index.ts`)
   - Unified notification interface
   - Channel routing logic
   - Preference handling

2. **Service Worker** (`src/lib/service-worker/`)
   - Push notification handling
   - Background sync
   - Offline notification queue

3. **Email Service** (`src/lib/mail/`)
   - SMTP configuration
   - Template system
   - Delivery tracking

4. **Notification Provider** (`src/providers/notification-integration.tsx`)
   - Context management
   - Preference synchronization
   - Channel coordination

## Usage

### Basic Notification

```tsx
import { notify } from '@/lib/notifications';

// Send notification via all enabled channels
await notify({
  type: 'chat',
  title: 'New Message',
  message: 'You have a new message from <PERSON>',
  email: true,
  inApp: true,
  push: true
});
```

### Channel-Specific Notifications

```tsx
import { sendEmailNotification, sendPushNotification, showToast } from '@/lib/notifications';

// Email only
await sendEmailNotification({
  to: '<EMAIL>',
  subject: 'Document Updated',
  template: 'document-update',
  data: { documentName: 'Project Plan.pdf' }
});

// Push only
await sendPushNotification({
  title: 'New Message',
  body: 'You have a new message',
  icon: '/icons/message.png',
  badge: '/icons/badge.png'
});

// In-app only
showToast({
  title: 'Success',
  message: 'Document uploaded successfully',
  type: 'success'
});
```

### Using the Notification Hook

```tsx
import { useNotifications } from '@/hooks/useNotifications';

function NotificationComponent() {
  const {
    preferences,
    updatePreferences,
    sendNotification,
    isSubscribed,
    subscribeToPush,
    unsubscribeFromPush
  } = useNotifications();

  const handleTogglePush = async () => {
    if (isSubscribed) {
      await unsubscribeFromPush();
    } else {
      await subscribeToPush();
    }
  };

  const handleUpdatePreferences = async (type: string, channels: any) => {
    await updatePreferences({
      [type]: channels
    });
  };

  return (
    <div>
      <button onClick={handleTogglePush}>
        {isSubscribed ? 'Disable' : 'Enable'} Push Notifications
      </button>
      
      <div>
        <h3>Notification Preferences</h3>
        {Object.entries(preferences).map(([type, channels]) => (
          <div key={type}>
            <h4>{type}</h4>
            <label>
              <input
                type="checkbox"
                checked={channels.email}
                onChange={(e) => handleUpdatePreferences(type, {
                  ...channels,
                  email: e.target.checked
                })}
              />
              Email
            </label>
            <label>
              <input
                type="checkbox"
                checked={channels.push}
                onChange={(e) => handleUpdatePreferences(type, {
                  ...channels,
                  push: e.target.checked
                })}
              />
              Push
            </label>
            <label>
              <input
                type="checkbox"
                checked={channels.inApp}
                onChange={(e) => handleUpdatePreferences(type, {
                  ...channels,
                  inApp: e.target.checked
                })}
              />
              In-App
            </label>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Configuration

### Environment Variables

```env
# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Push Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"
VAPID_SUBJECT="mailto:<EMAIL>"
```

### VAPID Keys Generation

Generate VAPID keys for push notifications:

```bash
node scripts/generate-vapid-keys.js
```

### Email Templates

Create email templates in `src/lib/mail/templates/`:

```typescript
// src/lib/mail/templates/document-update.ts
export const documentUpdateTemplate = {
  subject: 'Document Updated: {{documentName}}',
  html: `
    <h1>Document Updated</h1>
    <p>The document "{{documentName}}" has been updated.</p>
    <a href="{{documentUrl}}">View Document</a>
  `,
  text: `
    Document Updated
    
    The document "{{documentName}}" has been updated.
    View at: {{documentUrl}}
  `
};
```

## Notification Types

### System Alerts
- Security notifications
- System maintenance
- Error alerts

### Chat Messages
- New messages
- Mentions
- Room invitations

### Document Updates
- Upload notifications
- Status changes
- Sharing notifications

### Role Changes
- Permission updates
- Role assignments
- Access changes

### Weekly Digests
- Activity summaries
- Pending tasks
- System updates

## Service Worker Integration

The notification system integrates with the service worker for:

1. **Background Push Handling**
   ```javascript
   // In service worker
   self.addEventListener('push', (event) => {
     const data = event.data.json();
     self.registration.showNotification(data.title, {
       body: data.body,
       icon: data.icon,
       badge: data.badge,
       data: data.data
     });
   });
   ```

2. **Notification Click Handling**
   ```javascript
   self.addEventListener('notificationclick', (event) => {
     event.notification.close();
     
     if (event.notification.data.url) {
       clients.openWindow(event.notification.data.url);
     }
   });
   ```

3. **Background Sync**
   ```javascript
   self.addEventListener('sync', (event) => {
     if (event.tag === 'notification-sync') {
       event.waitUntil(syncNotifications());
     }
   });
   ```

## API Endpoints

- `POST /api/notifications/send` - Send notification
- `GET /api/notifications/preferences` - Get user preferences
- `PUT /api/notifications/preferences` - Update preferences
- `POST /api/notifications/subscribe` - Subscribe to push
- `DELETE /api/notifications/subscribe` - Unsubscribe from push
- `POST /api/notifications/test` - Send test notification

## Best Practices

1. **Respect User Preferences**
   - Always check user preferences before sending
   - Provide easy opt-out mechanisms
   - Honor "Do Not Disturb" settings

2. **Rate Limiting**
   - Implement notification throttling
   - Batch similar notifications
   - Avoid notification spam

3. **Fallback Strategies**
   - Graceful degradation when channels fail
   - Retry mechanisms for failed deliveries
   - Alternative notification methods

4. **Performance**
   - Async notification processing
   - Queue management for high volume
   - Efficient template rendering

## Troubleshooting

### Common Issues

1. **Push notifications not working**
   - Check VAPID key configuration
   - Verify service worker registration
   - Ensure HTTPS in production

2. **Email delivery issues**
   - Verify SMTP credentials
   - Check spam folders
   - Validate email templates

3. **Permission denied**
   - Check notification permissions
   - Verify user consent
   - Handle permission requests properly

### Debug Mode

Enable notification debugging:

```env
DEBUG_NOTIFICATIONS=true
```

This will log all notification attempts and help identify delivery issues.
